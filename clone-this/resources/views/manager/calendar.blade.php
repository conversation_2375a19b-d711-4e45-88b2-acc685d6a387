<x-app-layout>
    <x-slot name="store_slug">
        {{ $store_slug }}
    </x-slot>

    <x-slot name="header">
        <h2 class="text-lg font-bold">
            {{ __('Calendar') }} <select
                class="inline p-0 ml-1 text-lg font-bold bg-transparent border-none _js-input-store">
                @if (in_array(auth()->user()->role_id, [1,2,3,4,6])) <option
                    value="{{ route('calendar', ['store_slug' => 'all', 'month' => $_GET['month'] ?? '']) }}" {{
                    $store_slug=='all' ? "selected" : null }}>
                    All</option>
                @endif
                @foreach ($stores as $item)
                <option value="{{ route('calendar', ['store_slug' => $item->slug, 'month' => $_GET['month'] ?? '']) }}"
                    {{ $item->slug == $store_slug ?
                    "selected" : null }}>
                    {{ $item->name }}</option>
                @endforeach
            </select>
        </h2>
        {{-- <a class="flex flex-col items-center justify-center ml-auto text-center w-9"
            href="{{ route('calendar', ['store_slug' => $store_slug, 'refresh' => 1]) }}">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-7 w-7" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd"
                    d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                    clip-rule="evenodd" />
            </svg>
            <span class="block text-xs font-bold">Refresh</span></a> --}}
    </x-slot>

    <x-slot name="subheader">
        <div class="flex items-center justify-center h-10 mt-2 text-red-500">
            @if ($store_slug == 'all')
            <a class="w-6 h-6" href="{{ route('calendar', ['store_slug' => $store_slug, 'date' => $date_prev]) }}"><svg
                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
                        clip-rule="evenodd" />
                </svg></a>
            <input type="date"
                class="w-56 p-0 mx-3 text-lg font-bold text-center border-none focus:ring-0 _js-input-date"
                max="{{ $date_next }}" value="{{ $date_now }}">
            @if ($date_next)
            <a class="w-6 h-6" href="{{ route('calendar', ['store_slug' => $store_slug, 'date' => $date_next]) }}"><svg
                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
                        clip-rule="evenodd" />
                </svg></a>
            @endif
            @else
            <a class="w-6 h-6"
                href="{{ route('calendar', ['store_slug' => $store_slug, 'month' => $month_prev]) }}"><svg
                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
                        clip-rule="evenodd" />
                </svg></a>
            <input type="month"
                class="w-56 p-0 mx-3 text-lg font-bold text-center border-none focus:ring-0 _js-input-month"
                max="{{ date('Y-m') }}" value="{{ $month_now }}">
            @if ($month_next)
            <a class="w-6 h-6"
                href="{{ route('calendar', ['store_slug' => $store_slug, 'month' => $month_next]) }}"><svg
                    xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd"
                        d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
                        clip-rule="evenodd" />
                </svg></a>
            @endif
            @endif
        </div>
        @if (!in_array(auth()->user()->role_id, [5]))
        <div class="grid grid-cols-8 gap-0 text-xs font-bold text-center">
            @else
            <div class="grid grid-cols-6 gap-0 text-xs font-bold text-center">
                @endif
                <span class="grid-cols-2 py-2 text-gray-500 capitalize">{{ $store_slug == 'all' ? 'Toko' :
                    __('order.date') }}</span>
                <span class="grid-cols-1 py-2 text-black capitalize">{{ __('order.total') }}</span>
                @if (!in_array(auth()->user()->role_id, [5]))
                <span class="grid-cols-1 py-2 text-yellow-500 capitalize">{{ __('order.bebas') }}</span>
                @endif
                <span class="py-2 text-blue-500 capitalize grid-cols-">{{ __('order.diambil') }}</span>
                <span class="py-2 text-blue-700 capitalize grid-cols-">{{ __('order.terkirim') }}</span>
                <span class="py-2 text-green-500 capitalize grid-cols-">{{ __('order.selesai') }}</span>
                @if (in_array(auth()->user()->role_id, [5]))
                <span class="py-2 text-pink-500 capitalize grid-cols-">B-Bro</span>
                @else
                <span class="py-2 text-red-500 capitalize grid-cols-">{{ __('order.overtime') }}</span>
                <span class="py-2 text-red-700 capitalize grid-cols-">{{ __('order.batal') }}</span>
                @endif
            </div>
            @if (!in_array(auth()->user()->role_id, [5]))
            <div class="grid grid-cols-8 gap-0 text-xs font-bold text-center bg-red-50">
                @else
                <div class="grid grid-cols-6 gap-0 text-xs font-bold text-center">
                    @endif
                    <span class="grid-cols-2 py-2 text-gray-500 capitalize">TOTAL</span>
                    <span class="grid-cols-1 py-2 text-black capitalize">{{ $sum_total_job }}</span>
                    @if (!in_array(auth()->user()->role_id, [5]))
                    <span class="grid-cols-1 py-2 text-yellow-500 capitalize">{{ $sum_total_bebas }}</span>
                    @endif
                    <span class="py-2 text-blue-500 capitalize grid-cols-">{{ $sum_total_diambil }}</span>
                    <span class="py-2 text-blue-700 capitalize grid-cols-">{{ $sum_total_terkirim }}</span>
                    <span class="py-2 text-green-500 capitalize grid-cols-">{{ $sum_total_selesai }}</span>
                    @if (in_array(auth()->user()->role_id, [5]))
                    <span class="py-2 text-pink-500 capitalize grid-cols-">{{ $sum_total_bbro }}</span>
                    @else
                    <span class="py-2 text-red-500 capitalize grid-cols-">{{$sum_total_overtime }}@if($store_slug !==
                        'all')~{{
                        $sum_total_job > 0 ? round($sum_total_overtime/$sum_total_job*100) : 0 }}%@endif</span>
                    <span class="py-2 text-red-700 capitalize grid-cols-">{{ $sum_total_batal }}</span>
                    @endif
                </div>
    </x-slot>

    <div class="pb-20 pt-44">
        @if ($store_slug == 'all')
        @foreach ($data as $i => $item)

        <a href="{{ $item['url'] }}"
            class="grid grid-cols-8 gap-0 text-sm font-bold text-center border-b border-gray-200 border-solid">
            <div class="flex items-center justify-center w-16 pl-1 leading-none text-gray-500 h-11"
                style="font-size: 11px;">
                <div class="w-full text-left break-words">
                    {{$item['store'] }}
                </div>
            </div>
            <span class="flex items-center justify-center grid-cols-1 text-black">{{ $item['total'] }}</span>
            <span class="flex items-center justify-center grid-cols-1 text-yellow-500">{{ $item['bebas'] }}</span>
            <span class="flex items-center justify-center grid-cols-1 text-blue-500">{{ $item['diambil'] }}</span>
            <span class="flex items-center justify-center grid-cols-1 text-blue-700">{{ $item['terkirim'] }}</span>
            <span class="flex items-center justify-center grid-cols-1 text-green-500">{{ $item['selesai'] }}</span>
            <span class="flex items-center justify-center grid-cols-1 text-red-500">{{ $item['overtime'] }}</span>
            <span class="flex items-center justify-center grid-cols-1 text-red-700">{{ $item['batal'] }}</span>
        </a>
        @endforeach
        @else
        @foreach ($data as $i => $item)
        @if (!in_array(auth()->user()->role_id, [5]))
        <a href="{{ $item['url'] }}"
            class="grid grid-cols-8 gap-0 font-bold text-sm text-center border-solid border-gray-200 border-b {{ $month_now == date('Y-m') && $item['date'] == date('d') ? 'bg-yellow-100' : null }}">
            @else
            <a href="{{ $item['url'] }}"
                class="grid grid-cols-6 gap-0 font-bold text-sm text-center border-solid border-gray-200 border-b {{ $i == 0 ? 'bg-yellow-100' : null }}">
                @endif
                <span class="flex flex-col items-center justify-center grid-cols-2 leading-none text-gray-500 h-11">{{
                    $item['date'] }}<span class="mt-0 text-xs font-light">{{ $item['day'] }}</span></span>
                <span class="flex items-center justify-center grid-cols-1 text-black">{{ $item['total'] }}</span>
                @if (!in_array(auth()->user()->role_id, [5]))
                <span class="flex items-center justify-center grid-cols-1 text-yellow-500">{{ $item['bebas']
                    }}</span>
                @endif
                <span class="flex items-center justify-center grid-cols-1 text-blue-500">{{ $item['diambil']
                    }}</span>
                <span class="flex items-center justify-center grid-cols-1 text-blue-700">{{ $item['terkirim']
                    }}</span>
                <span class="flex items-center justify-center grid-cols-1 text-green-500">{{ $item['selesai']
                    }}</span>
                @if (in_array(auth()->user()->role_id, [5]))
                <span class="flex items-center justify-center grid-cols-1 text-pink-500">{{ $item['bbro'] }}<small
                        class="opacity-50">/{{
                        $store->marketing_each_day }}</small></span>
                @else
                <span class="flex items-center justify-center grid-cols-1 text-red-500">{{ $item['overtime'] }}</span>
                <span class="flex items-center justify-center grid-cols-1 text-red-700">{{ $item['batal'] }}</span>
                @endif
            </a>
            @endforeach
            @endif
    </div>

    <x-slot name="js">
        <script>
            $( document ).ready(function() {
                $('._js-input-date').change(function() {
                    const url = "{{ url()->current() }}";
                    const date = this.value;
                    // console.log(url+'?date='+date);
                    window.location.replace(url+'?date='+date);
                });
                $('._js-input-month').change(function() {
                    const url = "{{ url()->current() }}";
                    const month = this.value;
                    // console.log(url+'?month='+month);
                    window.location.replace(url+'?month='+month);
                });
                $('._js-input-store').change(function() {
                    const url = this.value;
                    window.location.replace(url);
                });
            });
        </script>
    </x-slot>
</x-app-layout>