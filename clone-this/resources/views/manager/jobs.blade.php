<x-app-layout>
  <x-slot name="store_slug">
    {{ $store_slug }}
  </x-slot>

  <x-slot name="header">
    <h2 class="text-sm font-bold">
      {{ __('Jobs') }} <select class="inline p-0 ml-0.5 text-sm font-bold bg-transparent border-none _js-input-store">
        @foreach ($stores as $item)
        <option value="{{ route('jobs', ['store_slug' => $item->slug, 'date' => $_GET['date'] ?? '']) }}" {{ $item->slug
          == $store_slug ?
          "selected" : null }}>
          {{ $item->name }}</option>
        @endforeach
      </select>
    </h2>
    <div class="flex ml-auto">
      {{-- @if (in_array(auth()->user()->role_id, [1,2,3,4,5,6])) <a class="mr-3 text-center w-9"
        href="{{ route('operatingcost-add', ['store_slug' => $store_slug]) }}">
        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
          stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round"
            d="M12 6v12m-3-2.818l.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
        </svg>
        <span class="block -mt-1 text-xs">Biaya</span></a>
      @endif --}}
      {{-- @if (auth()->user()->role_id <= 3) <a class="ml-auto text-center w-9 text-yellow-400"
        href="{{ route('order-add-vue', ['store_slug' => $store_slug]) }}">
        <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
            clip-rule="evenodd" />
        </svg><span class="block -mt-1 text-xs">BETA</span></a>
        @endif --}}
        @if (in_array(auth()->user()->role_id, [1,2,3,4,6])) {{-- <a class="ml-auto text-center w-9" target="_blank"
          href="{{ URL::to(" /")."/cs/form/order" }}"> --}}
          <a class="ml-2.5 text-center w-9" href="{{ route('purchase-add', ['store_slug' => $store_slug]) }}">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
              stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round"
                d="M13.5 16.875h3.375m0 0h3.375m-3.375 0V13.5m0 3.375v3.375M6 10.5h2.25a2.25 2.25 0 0 0 2.25-2.25V6a2.25 2.25 0 0 0-2.25-2.25H6A2.25 2.25 0 0 0 3.75 6v2.25A2.25 2.25 0 0 0 6 10.5Zm0 9.75h2.25A2.25 2.25 0 0 0 10.5 18v-2.25a2.25 2.25 0 0 0-2.25-2.25H6a2.25 2.25 0 0 0-2.25 2.25V18A2.25 2.25 0 0 0 6 20.25Zm9.75-9.75H18a2.25 2.25 0 0 0 2.25-2.25V6A2.25 2.25 0 0 0 18 3.75h-2.25A2.25 2.25 0 0 0 13.5 6v2.25a2.25 2.25 0 0 0 2.25 2.25Z" />
            </svg>
            <span class="block -mt-1 text-xs">Prchs</span></a>
          @endif
          @if (auth()->user()->role_id <= 3) {{-- <a class="ml-auto text-center w-9" target="_blank" href="{{ URL::to("
            /")."/cs/form/order" }}"> --}}
            <a class="ml-2.5 text-center w-9" href="{{ route('invoice-add', ['store_slug' => $store_slug]) }}">
              <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor"
                stroke-width="2">
                <path stroke-linecap="round" stroke-linejoin="round"
                  d="M9 13h6m-3-3v6m5 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg><span class="block -mt-1 text-xs">Invc</span></a>
            @endif
            @if (in_array(auth()->user()->role_id, [1,2,3,4,6])) {{-- <a class="ml-auto text-center w-9" target="_blank"
              href="{{ URL::to(" /")."/cs/form/order" }}"> --}}
              <a class="ml-2.5 text-center w-9" href="{{ route('order-add-vue', ['store_slug' => $store_slug]) }}">
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm1-11a1 1 0 10-2 0v2H7a1 1 0 100 2h2v2a1 1 0 102 0v-2h2a1 1 0 100-2h-2V7z"
                    clip-rule="evenodd" />
                </svg><span class="block -mt-1 text-xs">Add</span></a>
              @endif
    </div>
  </x-slot>

  <x-slot name="subheader">
    <div class="flex items-center justify-center h-10 mt-2 text-red-500">
      <a class="w-6 h-6" href="{{ route('jobs', ['store_slug' => $store_slug, 'date' => $date_prev]) }}"><svg
          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm.707-10.293a1 1 0 00-1.414-1.414l-3 3a1 1 0 000 1.414l3 3a1 1 0 001.414-1.414L9.414 11H13a1 1 0 100-2H9.414l1.293-1.293z"
            clip-rule="evenodd" />
        </svg></a>
      <input type="date" class="w-56 p-0 mx-3 text-lg font-bold text-center border-none focus:ring-0 _js-input-date"
        max="{{ date('Y-m-d') }}" value="{{ $date_now }}">
      @if ($date_next)
      <a class="w-6 h-6" href="{{ route('jobs', ['store_slug' => $store_slug, 'date' => $date_next]) }}"><svg
          xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd"
            d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.707l-3-3a1 1 0 00-1.414 1.414L10.586 9H7a1 1 0 100 2h3.586l-1.293 1.293a1 1 0 101.414 1.414l3-3a1 1 0 000-1.414z"
            clip-rule="evenodd" />
        </svg></a>
      @endif
    </div>
    <p class="w-full pb-3 text-sm text-center text-gray-400">{{ $day }}</p>
  </x-slot>

  <div class="px-8 pb-20 pt-36">
    {{-- @if ($store->marketing_each_day > 0) --}}
    @if (auth()->user()->role_id <= 4 || auth()->user()->role_id === 6) @php $persentase_month=$total_job_this_month> 0
      &&
      $total_job_this_month_overtime > 0 ?
      round($total_job_this_month_overtime/$total_job_this_month*100) : 0;
      $persentase_today=$total_job_today> 0 && $total_job_today_overtime > 0 ?
      round($total_job_today_overtime/$total_job_today*100) : 0;
      @endphp
      <x-link href="{{ route('job-list', [
            'store_slug' => $store_slug, 
            'date' => $date_now,
            'status' => 'overtime',
            'type' => 'order',
            ]) }}" class="w-full h-8 pt-1 mt-3 text-gray-500 capitalize bg-gray-200 shadow-none"
        style="font-size: 13px;">
        {{ $total_job_this_month }}/<span class="text-red-600">{{
          $total_job_this_month_overtime }}</span> - <span class="{{ $persentase_month > 10 ? " text-red-600"
          : "text-green-500" }}">{{ $persentase_month }}%</span>&nbsp;&nbsp;|&nbsp;&nbsp;{{ $total_job_today
        }}/<span class="text-red-600">{{
          $total_job_today_overtime }}</span> - <span class="{{ $persentase_today > 10 ? " text-red-600"
          : "text-green-500" }}">{{ $persentase_today }}%</span>
      </x-link>
      @endif
      @php
      $total_marketing_selesai_bydriver = 0;
      foreach ($drivers as $driver) {
      $total_marketing_selesai_bydriver += $driver->marketings_selesai;
      }
      $bbro_is_done = in_array(auth()->user()->role_id, [5, 6]) ? $store->marketing_each_day <= $marketing_selesai_count
        : $store->
        marketing_each_day * $drivers->count() <= $total_marketing_selesai_bydriver; @endphp <x-link
          class="_js-btn-checkin-marketing w-full gap-3 {{ $bbro_is_done ? 'bg-green-600 border-green-900' : 'bg-pink-600 border-pink-900'}}  border-4 text-white mt-6 {{ $date_now != date('Y-m-d') ? 'opacity-50 pointer-events-none' : '' }}">
          <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="4" stroke="currentColor"
            class="w-6 h-6 {{ $bbro_is_done ? 'text-green-900' : 'text-pink-900'}} inline-block">
            <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
          </svg>
          @if (in_array(auth()->user()->role_id, [5, 6]))
          B-Bro ({{ $store->marketing_each_day}}-{{ $marketing_selesai_count }})
          @else
          B-Bro ({{ $store->marketing_each_day * $drivers->count()}}-{{ $total_marketing_selesai_bydriver }})
          @endif
          </x-link>
          {{-- @endif --}}

          @if (in_array(auth()->user()->role_id, [1,2,3,4,5,6]))
          <x-link href="{{ route('operatingcost-add', ['store_slug' => $store_slug]) }}"
            class="w-full gap-3 mt-6 text-white bg-green-700 border-4 border-green-900">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="4"
              stroke="currentColor" class="inline-block w-6 h-6 text-green-900">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
            </svg>
            Pengeluaran Toko
          </x-link>
          @endif

          {{-- @if (in_array(auth()->user()->role_id, [1,2,3,4]))
          <x-link href="{{ route('order-add', ['store_slug' => $store_slug, 'type' => 'asdt']) }}"
            class="w-full gap-3 mt-6 text-white bg-red-700 border-4 border-red-900">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="4"
              stroke="currentColor" class="inline-block w-6 h-6 text-red-900">
              <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
            </svg>
            ASDT
          </x-link>
          @endif --}}

          @if ($invoice_total_count > 0)
          <x-link href="{{ route('job-list', [
            'store_slug' => $store_slug, 
            'date' => $date_now,
            'type' => 'invoice',
            ]) }}" class="w-full mt-8 text-blue-900 bg-blue-200">
            🧾 Invoice ({{ $invoice_total_count }})
          </x-link>
          @endif
          <x-link href="{{ route('job-list', [
            'store_slug' => $store_slug, 
            'date' => $date_now,
            'status' => 'total',
            ]) }}" class="w-full bg-black mt-8 {{ $order_total_count == 0 ? 'opacity-25 pointer-events-none' : '' }}">
            {{ __('order.total') }} Job ({{ $order_total_count }})
          </x-link>
          @if (auth()->user()->role_id != 5 || (in_array(auth()->user()->role_id, [5, 6]) &&
          !$store->hide_freejob_fordelman))
          <x-link href="{{ route('job-list', [
            'store_slug' => $store_slug, 
            'date' => $date_now,
            'status' => 'bebas',
            'type' => 'order',
            ]) }}"
            class="w-full bg-yellow-500 mt-8 {{ $order_bebas_count == 0 ? 'opacity-25 pointer-events-none' : '' }}">
            Job {{ __('order.bebas') }} ({{ $order_bebas_count }})
          </x-link>
          @endif
          <x-link href="{{ route('job-list', [
            'store_slug' => $store_slug, 
            'date' => $date_now,
            'status' => 'diambil',
            'type' => 'order',
            ]) }}"
            class="w-full bg-blue-500 mt-8 {{ $order_diambil_count == 0 ? 'opacity-25 pointer-events-none' : '' }}">
            Job {{ __('order.diambil') }} ({{ $order_diambil_count }})
            @foreach ($drivers as $driver)
            <br><span class="text-xs leading-none">{{ $driver->name }} ({{ $driver->orders_diambil
              }})</span>
            @endforeach
          </x-link>
          <x-link href="{{ route('job-list', [
            'store_slug' => $store_slug, 
            'date' => $date_now,
            'status' => 'terkirim',
            'type' => 'order',
            ]) }}"
            class="w-full bg-blue-700 mt-8 {{ $order_terkirim_count == 0 ? 'opacity-25 pointer-events-none' : '' }}">
            Job {{ __('order.terkirim') }} ({{ $order_terkirim_count }})
            @foreach ($drivers as $driver)
            <br><span class="text-xs leading-none">{{ $driver->name }} ({{ $driver->orders_terkirim
              }})</span>
            @endforeach
            <hr class="my-2">
            <span class="text-xs leading-none">🪙 CASH ({{ $order_terkirim_cash_count }}) </span><span
              class="text-xs leading-none capitalize">Rp{{
              number_format($order_terkirim_cash_total, 0, '', '.')
              }}</span>
            <br><span class="text-xs leading-none">🏧 NON-CASH ({{ $order_terkirim_non_cash_count }})
            </span><span class="text-xs leading-none capitalize">{{
              number_format($order_terkirim_non_cash_total, 0, '', '.')
              }}</span>
          </x-link>
          <x-link href="{{ route('job-list', [
            'store_slug' => $store_slug, 
            'date' => $date_now,
            'status' => 'selesai',
            'type' => 'order',
            ]) }}"
            class="w-full bg-green-500 mt-8 {{ $order_selesai_count == 0 ? 'opacity-25 pointer-events-none' : '' }}">
            Job {{ __('order.selesai') }} ({{ $order_selesai_count }})
            @foreach ($drivers as $driver)
            <br><span class="text-xs leading-none">{{ $driver->name }} ({{ $driver->orders_selesai
              }})</span>
            @endforeach
            <div class="capitalize">
              <hr class="mt-1 mb-2.5" />
              <p class="text-sm mt-1.5 bg-blue-200 text-blue-900 rounded px-0.5">🧾 Invoice ({{
                $invoice_confirmed_non_cash_count }})
                @if(auth()->user()->role_id <= 4 || auth()->user()->role_id === 6) <span class="font-bold">
                    Rp{{ number_format($invoice_confirmed_non_cash_total, 0, '',
                    '.')
                    }}</span>
                  @endif
              </p>
              <hr class="mt-2.5 mb-2.5" />
              <p class="text-sm mt-1.5 bg-green-700 rounded px-0.5">🪙 CASH ({{ $order_selesai_cash_count
                }})
                @if(auth()->user()->role_id <= 4 || auth()->user()->role_id === 6) <span class="font-bold">
                    Rp{{ number_format($order_selesai_cash_total + $order_selesai_split_cash_total, 0,
                    '',
                    '.')
                    }}</span>
                  @endif
              </p>
              <p class="text-sm mt-1.5 bg-blue-800 rounded px-0.5">🏧 NON-CASH ({{
                $order_selesai_non_cash_count + $order_selesai_invoice_count
                }})
                @if(auth()->user()->role_id <= 4 || auth()->user()->role_id === 6) <span class="font-bold">
                    Rp{{ number_format($order_selesai_non_cash_total + $order_selesai_invoice_total, 0, '',
                    '.')
                    }}</span>
                  @endif
              </p>
              @foreach ($banks as $bank)
              @if(trim(strtolower($bank->bank_name)) !== 'deposit')
              <p
                class="text-sm mt-1.5 {{ strtolower($bank->bank_name) == 'qris' ? 'bg-gray-700' : 'bg-blue-700'}} rounded px-0.5">
                {{ $bank->bank_name }} ({{ $bank->orders_count }})
                @if(auth()->user()->role_id <= 4 || auth()->user()->role_id === 6) <span class="font-bold">
                    {{-- Rp{{ number_format($bank->order_total + $bank->invoice_total, 0, '', '.') }}
                    --}}
                    Rp{{ number_format($bank->order_total, 0, '', '.') }}
                  </span>
                  @endif
              </p>
              @endif
              @endforeach
              <p class="text-sm mt-1.5 bg-blue-600 rounded px-0.5">
                AR ({{ $order_selesai_invoice_count }})
                <span class="font-bold">
                  Rp{{ number_format($order_selesai_invoice_total, 0, '', '.') }}
                </span>
              </p>
              @if(auth()->user()->role_id <= 4 || auth()->user()->role_id === 6) <p class="text-sm mt-1.5 px-0.5">TOTAL
                  <span class="font-bold">
                    Rp{{ number_format($order_total, 0, '', '.') }}</span>
                </p>
                @endif

            </div>
          </x-link>
          @if ($total_operatingcost_today > 0)
          <x-link href="{{ route('operatingcost-list', [
            'store_slug' => $store_slug,
            'date' => $date_now,
            ]) }}"
            class="w-full border-4 border-green-700 text-green-700 bg-transparent mt-8 {{ $total_operatingcost_today == 0 ? 'opacity-25 pointer-events-none' : '' }}">
            Total Pengeluaran
            <p class="capitalize text-sm mt-1.5 bg-yellow-200 text-yellow-900 rounded px-0.5">🥊 Total All:
              <span class="font-bold">
                Rp{{ number_format($total_operatingcost_today, 0, '',
                '.')
                }}</span>
            </p>
          </x-link>
          @endif
          @if ($marketing_diambil_count > 0)
          <x-link href="{{ route('job-list', [
            'store_slug' => $store_slug, 
            'date' => $date_now,
            'status' => 'diambil',
            'type' => 'marketing',
            ]) }}"
            class="w-full border-4 border-pink-500 text-pink-500 bg-transparent mt-8 {{ $marketing_diambil_count == 0 ? 'opacity-25 pointer-events-none' : '' }}">
            Bagi Brosur ({{ $marketing_diambil_count }})
            @foreach ($drivers as $driver)
            <br><span class="text-xs leading-none">{{ $driver->name }} ({{ $driver->marketings_diambil
              }})</span>
            @endforeach
          </x-link>
          @endif
          {{-- @if ($marketing_selesai_count > 0) --}}
          <x-link href="{{ route('job-list', [
            'store_slug' => $store_slug, 
            'date' => $date_now,
            'status' => 'selesai',
            'type' => 'marketing',
            ]) }}"
            class="w-full border-4 border-pink-600 text-pink-600 bg-transparent mt-8 {{ $marketing_selesai_count == 0 ? 'opacity-25 pointer-events-none' : '' }}">
            @if(in_array(auth()->user()->role_id, [5, 6]))
            B-BRO Selesai ({{ $store->marketing_each_day}}-{{ $marketing_selesai_count }}) {{
            $marketing_plus_selesai_count > 0 ? '+'.$marketing_plus_selesai_count : '' }}
            @else
            B-BRO Selesai ({{ $store->marketing_each_day * $drivers->count()}}-{{
            $total_marketing_selesai_bydriver }}) {{
            $marketing_plus_selesai_count > 0 ? '+'.$marketing_plus_selesai_count : '' }}
            @foreach ($drivers as $driver)
            <br><span class="text-xs leading-none">{{ $driver->name }} ({{ $store->marketing_each_day }}-{{
              $driver->marketings_selesai }})</span>
            @endforeach
            @foreach ($drivers_plus as $driver_plus)
            <br><span class="text-xs leading-none">{{ $driver_plus->name }} (+{{
              $driver_plus->marketings_selesai }})</span>
            @endforeach
            @endif
          </x-link>
          {{-- @endif --}}
          @if ($marketing_batal_count > 0)
          <x-link href="{{ route('job-list', [
            'store_slug' => $store_slug, 
            'date' => $date_now,
            'status' => 'batal',
            'type' => 'marketing',
            ]) }}"
            class="w-full border-4 border-red-600 text-red-600 bg-transparent mt-8 {{ $marketing_batal_count == 0 ? 'opacity-25 pointer-events-none' : '' }}">
            Batal Bagi Brosur ({{ $marketing_batal_count }})
            @foreach ($drivers as $driver)
            <br><span class="text-xs leading-none">{{ $driver->name }} ({{ $driver->marketings_batal
              }})</span>
            @endforeach
          </x-link>
          @endif
          <x-link href="{{ route('job-list', [
            'store_slug' => $store_slug, 
            'date' => $date_now,
            'status' => 'batal',
            'type' => 'order',
            ]) }}"
            class="w-full bg-red-500 mt-8 {{ $order_batal_count == 0 ? 'opacity-25 pointer-events-none' : '' }}">
            Job {{ __('order.batal') }} ({{ $order_batal_count }})
            @foreach ($drivers as $driver)
            <br><span class="text-xs leading-none">{{ $driver->name }} ({{ $driver->orders_batal }})</span>
            @endforeach
          </x-link>
          @foreach ($orders_unfinish as $order_unfinish)
          <x-link href="{{ route('jobs', [
                'store_slug' => $store_slug, 
                'date' => $order_unfinish['date'],
                ]) }}" class="w-full mt-8 bg-indigo-600">
            Unconfirmed Job ({{ $order_unfinish['count'] }})
            <br><span class="text-xs opacity-50">{{ $order_unfinish['date'] }}</span>
          </x-link>
          @endforeach
  </div>

  {{-- Modal Checkin MARKETING
  ==================================================== --}}
  <div
    class="fixed top-0 left-0 z-50 flex items-center justify-center w-full h-full px-0 py-0 bg-black bg-opacity-50 _js-modal-checkin-marketing"
    style="display: none;">
    <!-- modal -->
    <div class="flex flex-col w-full h-full bg-white rounded shadow-lg">
      <!-- modal header -->
      <div class="flex items-center px-4 py-2 border-b">
        <h3 class="text-2xl font-bold">Bagi Brosur (B-Bro)</h3>
        <small class="text-gray-500 text-sm ml-1.5 font-bold mt-1 _js-gps-status">🔴 GPS OFF</small>
        <button class="ml-auto text-3xl font-bold text-red-600 _js-close-modal-checkin-marketing">&cross;</button>
      </div>
      <!-- modal body -->
      <form class="flex flex-col flex-1 gap-3 p-3 _js-form-checkin-job-marketing">
        @csrf
        <input type="hidden" id="marketing_id" value="{{ $current_marketing->id }}" name="marketing_id"
          class="_js-input-marketing-id">
        <input type="hidden" id="received_latlng" name="received_latlng" class="_js-input-received-latlng">
        <input type="hidden" id="received_latlng_accuracy" name="received_latlng_accuracy"
          class="_js-input-received-latlng-accuracy">
        <div class="relative flex items-center justify-center flex-1 overflow-hidden bg-black rounded-md">
          <svg class="w-10 h-10 text-white animate-spin" xmlns="http://www.w3.org/2000/svg" fill="none"
            viewBox="0 0 24 24">
            <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
            </circle>
            <path class="opacity-75" fill="currentColor"
              d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
            </path>
          </svg>
          <div class="absolute inset-0 z-10 flex items-center">
            <video id="webcam" autoplay playsinline class="w-full"></video>
            <canvas id="canvas" class="hidden"></canvas>
            <audio id="snapSound" src="/audio/demo_audio_snap.wav" preload="auto"></audio>
          </div>
          <div class="absolute left-0 right-0 z-20 bottom-20">
            <div class="relative flex-shrink-0 _js-wrp-img-capture-default" style="display: none;">
              <img src="" class="h-full _js-img-capture" />
              <div
                class="absolute inset-0 z-10 flex items-center justify-center bg-black bg-opacity-50 _js-wrp-loading">
                <svg class="w-6 h-6 text-white _js-loading-process animate-spin" xmlns="http://www.w3.org/2000/svg"
                  fill="none" viewBox="0 0 24 24">
                  <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                  </circle>
                  <path class="opacity-75" fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                  </path>
                </svg>
                <svg xmlns="http://www.w3.org/2000/svg" style="display: none;"
                  class="text-white _js-loading-done h-7 w-7" viewBox="0 0 20 20" fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clip-rule="evenodd" />
                </svg>
              </div>
              <button type="button" style="display: none;"
                class="absolute z-20 bg-white rounded-full _js-btn-delete-photo -top-3 -right-3">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-red-500" viewBox="0 0 20 20"
                  fill="currentColor">
                  <path fill-rule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                    clip-rule="evenodd" />
                </svg>
              </button>
            </div>
            <div class="flex h-16 gap-4 pt-3 pr-3 overflow-x-auto _js-img-list scrollbar-hide">
              @foreach ($current_marketing->photos as $photo)
              <div class="relative flex-shrink-0 _js-wrp-img-capture">
                <img src="{{ $photo->photo_url }}" class="h-full _js-img-capture" />
                <div style="display: none;"
                  class="absolute inset-0 z-10 flex items-center justify-center bg-black bg-opacity-50 _js-wrp-loading">
                  <svg class="w-6 h-6 text-white _js-loading-process animate-spin" xmlns="http://www.w3.org/2000/svg"
                    fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4">
                    </circle>
                    <path class="opacity-75" fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                    </path>
                  </svg>
                </div>
                <button type="button" id="{{ $photo->id }}"
                  class="absolute z-20 bg-white rounded-full _js-btn-delete-photo -top-3 -right-3">
                  <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-red-500" viewBox="0 0 20 20"
                    fill="currentColor">
                    <path fill-rule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                      clip-rule="evenodd" />
                  </svg>
                </button>
              </div>
              @endforeach
            </div>
            {{-- <div
              class="absolute top-0 bottom-0 right-0 z-10 pointer-events-none bg-gradient-to-r from-transparent to-black w-14">
            </div> --}}
          </div>
          <button type="button"
            class="absolute z-20 transform -translate-x-1/2 border-2 border-white rounded-full _js-btn-capture-camera group w-14 h-14 left-1/2 bottom-3">
            {{-- <div class="absolute bg-red-500 rounded-full inset-2"></div> --}}
          </button>
          <button type="button"
            class="absolute z-20 flex items-center justify-center bg-black bg-opacity-50 rounded-full _js-btn-flip-camera right-3 bottom-5 w-11 h-11">
            <svg xmlns="http://www.w3.org/2000/svg" class="w-6 h-6 text-white" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd"
                d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                clip-rule="evenodd" />
            </svg>
          </button>
        </div>
        <label class="block" for="driver_note">
          <div class="font-bold text-gray-700">Catatan {{-- <span class="text-red-600">*</span> --}}
          </div>
          <textarea id="driver_note" name="driver_note"
            class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-driver-note focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50"
            rows="2"></textarea>
        </label>
      </form>
      <div class="flex items-center justify-end p-3 border-t w-100">
        {{-- <button class="px-3 py-1 mr-1 text-white bg-red-600 rounded hover:bg-red-700 close-modal">SELESAI
          JOB</button> --}}
        <button type="button"
          class="w-full px-3 py-2 font-bold text-white bg-blue-700 rounded shadow-lg _js-btn-submit-checkin-job-marketing">SUBMIT
          B-BRO</button>
      </div>
    </div>
  </div>

  <x-slot name="js">
    <script>
      let latlng = null;
    let latlng_accuracy = null;
    let runGetLocation;

    function success(pos) {
      var crd = pos.coords;
      // console.log("Your current position is:");
      // console.log(`Latitude : ${crd.latitude}`);
      // console.log(`Longitude: ${crd.longitude}`);
      // console.log(`More or less ${crd.accuracy} meters.`);
      latlng = crd.latitude + ',' + crd.longitude;
      latlng_accuracy = crd.accuracy;
      $('._js-gps-status').html('🟢 GPS ON')
    }

    function error(err) {
      console.warn("ERROR(" + err.code + "): " + err.message);
      clearInterval(runGetLocation);
    }
    const options = {
      enableHighAccuracy: true,
      timeout: 5000,
      maximumAge: 0
    };

    function getLocation() {
      navigator.geolocation.getCurrentPosition(
        success, error, options
      );
    }

    $(document).ready(function() {
      $('._js-input-date').change(function() {
        const url = "{{ url()->current() }}";
        const date = this.value;
        // console.log(url+'?date='+date);
        window.location.replace(url + '?date=' + date);
      });
      $('._js-input-store').change(function() {
        const url = this.value;
        window.location.replace(url);
      });

      const webcamElement = document.getElementById('webcam');
      const canvasElement = document.getElementById('canvas');
      const snapSoundElement = document.getElementById('snapSound');
      const webcam = new Webcam(webcamElement, 'user', canvasElement, snapSoundElement);

      @if(in_array(auth()->user()->role_id, [5, 6]))

      webcam.start()
        .then(result => {
          console.log("webcam started");
          webcam.flip();
          webcam.start();
        })
        .catch(err => {
          console.log(err);
        });

      document.addEventListener('visibilitychange', (event) => {
        if (document.hidden) {
          console.log('not visible');
          webcam.stop();

        } else {
          console.log('is visible');
          webcam.start()
            .then(result => {
              console.log("webcam started");
              webcam.flip();
              webcam.start();
            })
            .catch(err => {
              console.log(err);
            });
        }
      });

      @endif


      // Job Checkin MARKETING
      // ====================================================
      mediumZoom('._js-img-capture');

      function base64ToBlob(base64, mime) {
        mime = mime || '';
        var sliceSize = 1024;
        var byteChars = window.atob(base64);
        var byteArrays = [];

        for (var offset = 0, len = byteChars.length; offset < len; offset += sliceSize) {
          var slice = byteChars.slice(offset, offset + sliceSize);

          var byteNumbers = new Array(slice.length);
          for (var i = 0; i < slice.length; i++) {
            byteNumbers[i] = slice.charCodeAt(i);
          }

          var byteArray = new Uint8Array(byteNumbers);

          byteArrays.push(byteArray);
        }

        return new Blob(byteArrays, {
          type: mime
        });
      }
      const $modalCheckinMarketing = $('._js-modal-checkin-marketing');
      const $btnCloseModalCheckinMarketing = $('._js-close-modal-checkin-marketing');
      $('body').on('click', '._js-btn-checkin-marketing', function() {
        $modalCheckinMarketing.fadeIn('fast');
        const $this = $(this);
        // const marketingId = $this.data('marketing_id');
        // $('._js-input-marketing-id').val(marketingId);
        if (navigator.geolocation) {
          runGetLocation = setInterval(getLocation, 1000);
        } else {
          // x.innerHTML = "Geolocation is not supported by this browser.";
          console.log("Geolocation is not supported by this browser.");
          clearInterval(runGetLocation);
        }
        @if(auth()->user()->role_id != 5)
        webcam.start()
          .then(result => {
            console.log("webcam started");
            webcam.flip();
            webcam.start();
          })
          .catch(err => {
            console.log(err);
          });
        @endif
      });
      $('._js-btn-flip-camera').on('click', function() {
        webcam.flip();
        webcam.start();
      });
      $('._js-btn-capture-camera').on('click', function() {
        const localId = Math.floor(Math.random() * 100) + 1;
        let photo = webcam.snap();
        $('._js-wrp-img-capture-default')
          .find('._js-img-capture')
          .prop("src", photo)
          .prop("id", 'capture_' + localId);
        $('._js-wrp-img-capture-default')
          .clone()
          .removeClass('_js-wrp-img-capture-default')
          .addClass('_js-wrp-img-capture')
          .prop('id', 'img_capture_' + localId)
          .show()
          .appendTo('._js-img-list');
        mediumZoom('#capture_' + localId);

        const base64ImageContent = photo.replace(/^data:image\/(png|jpg);base64,/, "");
        const blob = base64ToBlob(base64ImageContent, 'image/png');
        const formData = new FormData();
        formData.append('marketing_id', $('._js-input-marketing-id').val());
        formData.append('photo_marketing', blob);
        $.ajax({
          headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
          },
          url: "{{ route('upload-photo-marketing') }}",
          type: 'POST',
          cache: false,
          contentType: false,
          processData: false,
          data: formData,
          success: function(res) {
            console.log('res', res);
            $('#img_capture_' + localId).find('._js-loading-process').hide();
            $('#img_capture_' + localId).find('._js-loading-done').show();
            setTimeout(() => {
              $('#img_capture_' + localId).find('._js-wrp-loading').hide();
              $('#img_capture_' + localId).find('._js-btn-delete-photo')
                .prop('id', res.id).show();
            }, 500);
          },
          error: function(error) {
            console.log('error', error);
          },
        });
      });
      $('._js-img-list').on('click', '._js-btn-delete-photo', function() {
        if (confirm('Hapus foto?')) {
          const $parentWrpImage = $(this).parents('._js-wrp-img-capture');
          $parentWrpImage.find('._js-wrp-loading').show();
          const formData = new FormData();
          formData.append('id', $(this).attr('id'));
          $.ajax({
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            url: "{{ route('delete-photo-marketing') }}",
            type: 'POST',
            cache: false,
            contentType: false,
            processData: false,
            data: formData,
            success: function(res) {
              console.log('res', res);
              $parentWrpImage.remove();
            },
            error: function(error) {
              $parentWrpImage.find('._js-wrp-loading').hide();
              console.log('error', error);
            },
          });
        }
      });
      $btnCloseModalCheckinMarketing.on('click', function() {
        // if ($('._js-img-list').children().length > 0 && confirm('Hapus semua foto?')) {
        $modalCheckinMarketing.fadeOut('fast');
        // $('._js-input-marketing-id').val('');
        $('._js-input-driver-note').val('');
        clearInterval(runGetLocation);
        // webcam.stop();
        // $('._js-img-list').empty();
        // } else {
        //     $modalCheckinMarketing.fadeOut('fast');
        //     $('._js-input-marketing-id').val('');
        //     $('._js-input-driver-note').val('');
        //     clearInterval(runGetLocation);
        //     webcam.stop();
        // }
        @if(auth()->user()->role_id != 5)
        webcam.stop();
        @endif
      });
      $('._js-btn-submit-checkin-job-marketing').on('click', function() {
        $('._js-input-received-latlng').val(latlng);
        $('._js-input-received-latlng-accuracy').val(latlng_accuracy);
        const $form = $('._js-form-checkin-job-marketing');
        const inputs = new FormData($form[0]);
        const marketingId = inputs.get('marketing_id');
        const photoCount = $("._js-img-list").children().length;
        console.log('marketingId', marketingId);
        console.log('inputs', inputs.values());

        console.log('photoCount', photoCount);
        if (photoCount < 5) {
          // alert('Jumlah foto minimal 5!');
          mdtoast('Jumlah foto minimal 5!', {
            duration: 2000,
            type: mdtoast.ERROR
          });
          return;
        }

        // if (!inputs.get('driver_note')) {
        //     mdtoast('"Alasan Pembatalan" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
        // // } else if (!inputs.get('photo').name) {
        // //     mdtoast('"Foto Penyelesaian" harus diisi!', { duration: 2000, type: mdtoast.ERROR });
        // } else {
        if (confirm('Submit checkin?')) {
          mdtoast(
            'LOADING...', {
              // type: mdtoast.INFO,
              interaction: true,
              actionText: 'CLOSE',
              action: function() {
                this.hide();
              }
            });
          $.ajax({
            headers: {
              'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
            },
            url: "{{ route('submit-bbro') }}",
            type: 'POST',
            // type: 'GET',
            data: inputs,
            processData: false,
            contentType: false,
            success: function(res) {
              console.log('res', res);
              console.log('marketingId', marketingId);
              if (res) {
                mdtoast('CHECKIN BERHASIL', {
                  duration: 3000,
                  type: mdtoast.SUCCESS
                });
                $modalCheckinMarketing.fadeOut('fast');
                setTimeout(() => {
                  window.location.reload();
                }, 2000);
              } else {
                mdtoast('CHECKIN GAGAL', {
                  duration: 3000,
                  type: mdtoast.ERROR
                });
              }
            },
            error: function(error) {
              console.log('error', error);
              mdtoast('CHECKIN GAGAL', {
                duration: 3000,
                type: mdtoast.ERROR
              });
            },
          });
          // }
        }
      });
    });
    </script>
  </x-slot>
</x-app-layout>