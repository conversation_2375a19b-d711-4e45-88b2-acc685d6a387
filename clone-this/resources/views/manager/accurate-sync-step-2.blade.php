<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">

<head>
  <meta charset="utf-8">
  <meta name="viewport" content="width=device-width, initial-scale=1">

  <title>Accurate Sync - Step 2</title>

  <!-- Fonts -->
  {{--
  <link href="https://fonts.googleapis.com/css2?family=Nunito:wght@400;600;700&display=swap" rel="stylesheet"> --}}

  <!-- Styles -->
  <link rel="stylesheet" href="{{ mix('css/app.css') }}">

  <!-- Matomo -->
  <script>
    var _paq = window._paq = window._paq || [];
  /* tracker methods like "setCustomDimension" should be called before "trackPageView" */
  _paq.push(['trackPageView']);
  _paq.push(['enableLinkTracking']);
  (function() {
    var u="//stat.ordergasplus.online/";
    _paq.push(['setTrackerUrl', u+'matomo.php']);
    _paq.push(['setSiteId', '2']);
    var d=document, g=d.createElement('script'), s=d.getElementsByTagName('script')[0];
    g.async=true; g.src=u+'matomo.js'; s.parentNode.insertBefore(g,s);
  })();
  </script>
  <!-- End Matomo Code -->


  <!-- Hotjar Tracking Code for Gasplus CS -->
  <script>
    (function(h,o,t,j,a,r){
      h.hj=h.hj||function(){(h.hj.q=h.hj.q||[]).push(arguments)};
      h._hjSettings={hjid:6373432,hjsv:6};
      a=o.getElementsByTagName('head')[0];
      r=o.createElement('script');r.async=1;
      r.src=t+h._hjSettings.hjid+j+h._hjSettings.hjsv;
      a.appendChild(r);
  })(window,document,'https://static.hotjar.com/c/hotjar-','.js?sv=');
  </script>

</head>

<body class="antialiased">
  <div class="px-5 py-4 flex flex-col gap-5 h-screen">
    <h1 class="text-2xl font-bold">Sync <span class="underline">{{ $store_name }}</span> (Step 2 - Select Data to Sync)
    </h1>
    <p class="text-yellow-600 text-xl font-bold">TOTAL: {{ count($data_to_sync) }} data to Sync</p>
    <form action="{{ route('accurate-sync-process-2')}}" method="POST"
      class="flex flex-1 overflow-auto border-b border-gray-200">
      @csrf
      <input type="hidden" name="store_id" value="{{ $store_id }}" />
      <table class="w-full border text-xs">
        <thead>
          <tr class="divide-x">
            <th class="sticky top-0 px-1.5 py-2 text-black bg-yellow-300">Sync</th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-yellow-300">Code / ID</th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-yellow-300">Nama</th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-yellow-300">Kemiripan</th>
            <th class="sticky top-0 px-1.5 py-2 text-black bg-yellow-300">Data Accurate</th>
            <th class="sticky top-0 px-1.5 py-2  bg-green-300 text-green-900">Data GASPLUS</th>
          </tr>
        </thead>
        @php
        $data_count = 0;
        @endphp
        <tbody class="divide-y divide-x">
          @foreach($data_to_sync as $data)
          {{-- @if(empty($data['accurate_customer_id']) || !empty($data['synced_at']))
          @continue
          @else --}}
          @php
          $data_count++;
          @endphp
          {{-- @endif --}}
          <tr class="divide-x bg-blue-50 text-blue-900 hover:bg-blue-100">
            <td class="px-1.5 py-2 text-center">
              <input type="checkbox" name="is_sync_data[]"
                value="{{ $data['accurate_customer_id'].';'.$data['accurate_customer_code'].';'.$data['address_id'].';'.$data['customer_id'] }}"
                {{ (int)$data['similarity_percentage']>= 80 ? 'checked'
              : '' }}>
            </td>
            <td class="px-1.5 py-2 text-left">{{$data['accurate_customer_code']}} / {{ $data['accurate_customer_id'] }}
            </td>
            <td class="px-1.5 py-2 text-left whitespace-nowrap">{{$data['name']}}
              @if(!empty($data['customer_id']))
              <br />
              <a class="text-blue-500 underline font-semibold" href="/cs/show/customer/{{ $data['customer_id'] }}"
                target="_blank">Detail Pelanggan ↗️</a>
              @endif
            </td>
            <td class="px-1.5 py-2 text-center">{{(int)$data['similarity_percentage']}}%</td>
            <td class="px-1.5 py-2 text-left align-top">
              <span
                class="{{ strtolower($data['name']) !== strtolower($data['customer_name']) ? 'text-red-500 font-bold' : '' }}"><strong>Nama:</strong>
                {{ $data['name'] }}</span>
              <br />
              <span class="{{ empty($data['phone01']) && empty($data['phone02']) ? 'text-red-500 font-bold' : '' }}">
                <strong>No. WA:</strong> {{join(' / ', [$data['phone01'],$data['phone02']])}}
              </span>
              <br />
              <strong>Alamat:</strong> {{ $data['address'] }}
              <br />
              <span
                class="{{ strtolower($data['store']) !== strtolower($data['customer_store']) ? 'text-red-500 font-bold' : '' }}">
                <strong>Toko:</strong> {{ $data['store'] }}
              </span>
            </td>
            <td class="px-1.5 py-2 text-left align-top bg-green-50">
              <strong>Nama:</strong> {{$data['customer_name']}}
              <br />
              <strong>No. WA:</strong> {{$data['customer_phone']}}
              <br />
              <strong>Alamat:</strong> {{$data['customer_address']}}
              <br />
              <strong>Toko:</strong> {{$data['customer_store']}}
              <br />
              <strong>Last Order:</strong> {{$data['customer_last_order'] ?? ''}}
            </td>
          </tr>

          @if(!empty($data['customer_id_alt']) && (int)$data['similarity_percentage']< 80) <tr
            class="divide-x opacity-80 hover:bg-gray-100">
            <td class="px-1.5 py-2 text-center">
              <input type="checkbox" name="is_sync_data[]"
                value="{{ $data['accurate_customer_id'].';'.$data['accurate_customer_code'].';'.$data['address_id_alt'].';'.$data['customer_id_alt'] }}"
                {{ (int)$data['similarity_percentage_alt']>= 80 ? 'checked'
              : '' }}>
            </td>
            <td class="px-1.5 py-2 text-left">{{$data['accurate_customer_code']}} / {{ $data['accurate_customer_id'] }}
            </td>
            <td class="px-1.5 py-2 text-left whitespace-nowrap">{{$data['name']}}
              @if(!empty($data['customer_id_alt']))
              <br />
              <a class="text-blue-500 underline font-semibold" href="/cs/show/customer/{{ $data['customer_id_alt'] }}"
                target="_blank">Detail Pelanggan ↗️</a>
              @endif
            </td>
            <td class="px-1.5 py-2 text-center">{{(int)$data['similarity_percentage_alt']}}%</td>
            <td class="px-1.5 py-2 text-left align-top">
              <span
                class="{{ strtolower($data['name']) !== strtolower($data['customer_name']) ? 'text-red-500 font-bold' : '' }}"><strong>Nama:</strong>
                {{ $data['name'] }}</span>
              <br />
              <span class="{{ empty($data['phone01']) && empty($data['phone02']) ? 'text-red-500 font-bold' : '' }}">
                <strong>No. WA:</strong> {{join(' / ', [$data['phone01'],$data['phone02']])}}
              </span>
              <br />
              <strong>Alamat:</strong> {{ $data['address'] }}
              <br />
              <span
                class="{{ strtolower($data['store']) !== strtolower($data['customer_store']) ? 'text-red-500 font-bold' : '' }}">
                <strong>Toko:</strong> {{ $data['store'] }}
              </span>
            </td>
            <td class="px-1.5 py-2 text-left align-top bg-green-50">
              <strong>Nama:</strong> {{$data['customer_name_alt']}}
              <br />
              <strong>No. WA:</strong> {{$data['customer_phone_alt']}}
              <br />
              <strong>Alamat:</strong> {{$data['customer_address_alt']}}
              <br />
              <strong>Toko:</strong> {{$data['customer_store_alt']}}
              <br />
              <strong>Last Order:</strong> {{$data['customer_last_order_alt']}}
            </td>
            </tr>
            @endif


            @endforeach
        </tbody>
      </table>
      <div class="fixed bottom-0 left-0 right-0 p-5 shadow-2xl z-50 bg-white border-t border-gray-200">
        <button type="submit" {{ $data_count===0 ? 'disabled' : '' }}
          class="rounded-md w-full {{ $data_count===0 ? 'opacity-50' : '' }} bg-red-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-red-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-red-600">Confirm
          Data
          & Sync</button>
      </div>
    </form>
    <div class="h-20">&nbsp;</div>
  </div>
</body>

</html>