<x-app-layout>
    <x-slot name="store_slug">
        {{ $store_slug }}
    </x-slot>

    <x-slot name="header">
        <a class="flex items-center -ml-3" href="{{ URL::previous() }}"><svg class="w-9 h-9"
                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                Data Armada
            </h2>
        </a>
        <a href="/cs/form/armada/{{$armada->id}}" target="_blank" class="ml-auto font-bold">✏️ EDIT ↗︎</a>
    </x-slot>

    <div class="px-3 pt-16 pb-20 prose">
        <table class="w-full table-auto">
            <tbody>
                <tr>
                    <th class="text-left">No.Polisi</th>
                    <td class="text-left">{{$armada->licence_number}}
                        @if($armada->stnk_valid_until)
                        <span class="text-xs text-gray-500">Valid until: {{$armada->stnk_valid_until}}</span>
                        @endif
                    </td>
                </tr>
                @if($armada->stnkphoto_url)
                <tr>
                    <th class="text-left whitespace-nowrap">Foto STNK</th>
                    <td class="text-left"><img data-zoomable class="!my-0" src="{{$armada->stnkphoto_url}}" /></td>
                </tr>
                @endif
                @if($armada->chassis_number)
                <tr>
                    <th class="text-left whitespace-nowrap">No.Rangka</th>
                    <td class="text-left">{{$armada->chassis_number}}</td>
                </tr>
                @endif
                @if($armada->machine_number)
                <tr>
                    <th class="text-left whitespace-nowrap">No.Mesin</th>
                    <td class="text-left">{{$armada->machine_number}}</td>
                </tr>
                @endif
                @if($armada->brand)
                <tr>
                    <th class="text-left whitespace-nowrap">Merk</th>
                    <td class="text-left">{{$armada->brand->name}}</td>
                </tr>
                @endif
                @if($armada->model)
                <tr>
                    <th class="text-left whitespace-nowrap">Model</th>
                    <td class="text-left">{{$armada->model->name}}</td>
                </tr>
                @endif
                @if($armada->year)
                <tr>
                    <th class="text-left whitespace-nowrap">Tahun</th>
                    <td class="text-left">{{$armada->year}}</td>
                </tr>
                @endif
                @if($armada->frontphotos->count() > 0)
                <tr>
                    <th class="text-left whitespace-nowrap">Tampak Depan</th>
                    <td class="flex flex-col gap-1 text-left">
                        @foreach ($armada->frontphotos as $photo)
                        <img data-zoomable class="!my-0" src="{{$photo->url}}" />
                        @endforeach
                    </td>
                </tr>
                @endif

                @if($armada->note)
                <tr>
                    <th class="text-left whitespace-nowrap">Catatan</th>
                    <td class="text-left">{{$armada->note}}</td>
                </tr>
                @endif
                @if($armada->employee)
                <tr>
                    <th class="text-left whitespace-nowrap">SDM</th>
                    <td class="flex flex-col text-left">{{$armada->employee->full_name}}
                        {{-- <span class="text-xs text-gray-500">
                            {{$armada->armada->brand ? $armada->armada->brand->name : ''}}
                            {{$armada->armada->model ? $armada->armada->model->name : ''}}
                            {{$armada->armada->year ? $armada->armada->year : ''}}
                        </span> --}}
                    </td>
                </tr>
                @endif
            </tbody>
        </table>
    </div>
    <x-slot name="js">
        <script>
            $(document).ready(function() {
                mediumZoom('[data-zoomable]');
            });
        </script>
    </x-slot>
</x-app-layout>