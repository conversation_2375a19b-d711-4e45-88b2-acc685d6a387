<x-app-layout>
  <x-slot name="store_slug">
    {{ $store_slug }}
  </x-slot>

  <x-slot name="header">
    <div class="absolute inset-0 flex items-center px-4 bg-yellow-500">
      <h2 class="text-lg font-bold">
        {{ __('Report') }} <select class="inline p-0 ml-1 text-lg font-bold bg-transparent border-none _js-input-store">
          @foreach ($stores as $store)
          <option value="{{ route('report', ['store_slug' => $store->slug]) }}" {{ $store->slug == $store_slug ?
            "selected" : null }}>
            {{ $store->name }}</option>
          @endforeach
        </select>
      </h2>
    </div>
  </x-slot>

  <div class="px-8 pb-20 pt-24 flex flex-col gap-8">
    @if (in_array(Auth::user()->role_id, [1,2]))
    <x-link href="{{ route('report.driver.list', ['store_slug' => $store_slug]) }}" class="w-full bg-blue-600">
      In-Job 🛵
    </x-link>
    <x-link href="{{ route('report.lt2.list', ['store_slug' => $store_slug]) }}" class="w-full bg-blue-500">
      Lantai 2 🎢
    </x-link>
    <x-link href="{{ route('report.bbro', ['store_slug' => $store_slug]) }}" class="w-full bg-pink-600">
      Performa B-Bro 🏍
    </x-link>
    <x-link href="{{ route('report.product.select', ['store_slug' => $store_slug]) }}" class="w-full bg-green-600">
      Performa Produk 📦
    </x-link>
    @endif
    @if (in_array(Auth::user()->role_id, [1,2]) || (is_array(Auth::user()->options) && array_key_exists('is_show_ltt',
    Auth::user()->options) && (int) Auth::user()->options['is_show_ltt']))
    <x-link href="{{ route('report.stockopname.detail', ['store_slug' => $store_slug]) }}" class="w-full bg-yellow-600">
      (LTT) Lap. Tutup Toko 🏬
    </x-link>
    @endif
    @if (in_array(Auth::user()->role_id, [1,2,3]))
    <x-link href="{{ route('report.deposit', ['store_slug' => $store_slug]) }}" class="w-full bg-indigo-600">
      Report Deposit 🌿
    </x-link>
    @endif
  </div>

  <x-slot name="js">
    <script>
      $( document ).ready(function() {
            // Change Store
            // --------------------------------------------------
            $('._js-input-store').change(function() {
                const url = this.value;
                window.location.replace(url);
            });
        });
    </script>
  </x-slot>

</x-app-layout>