<x-app-layout>
  <x-slot name="store_slug">
    {{ $store_slug }}
  </x-slot>

  <x-slot name="header">
    <div class="absolute inset-0 flex items-center px-4 bg-yellow-500">
      <h3 class="flex items-center text-base font-bold">
        <a href="{{ route('report', ['store_slug' => $store_slug]) }}" class="flex items-center -ml-3">
          <svg xmlns="http://www.w3.org/2000/svg" class="w-8 h-8" fill="none" viewBox="0 0 24 24" stroke="currentColor"
            stroke-width="2">
            <path stroke-linecap="round" stroke-linejoin="round" d="M15 19l-7-7 7-7" />
          </svg>
          <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 -ml-0.5 mr-1 -mt-0.5" viewBox="0 0 20 20"
            fill="currentColor">
            <path fill-rule="evenodd"
              d="M3 3a1 1 0 000 2v8a2 2 0 002 2h2.586l-1.293 1.293a1 1 0 101.414 1.414L10 15.414l2.293 2.293a1 1 0 001.414-1.414L12.414 15H15a2 2 0 002-2V5a1 1 0 100-2H3zm11.707 4.707a1 1 0 00-1.414-1.414L10 9.586 8.707 8.293a1 1 0 00-1.414 0l-2 2a1 1 0 101.414 1.414L8 10.414l1.293 1.293a1 1 0 001.414 0l4-4z"
              clip-rule="evenodd" />
          </svg>
          Lantai 2
        </a>
      </h3>
    </div>
  </x-slot>

  <div class="pt-16 pb-52">
    <label for="all" class="flex items-center px-4 text-sm border-b border-gray-200 border-solid h-11">
      <input type="checkbox" class="mr-2 _js-checkbox-all" id="all" name="all" value="all">
      <strong class="mr-2">All Delman</strong> <span class="overflow-hidden overflow-ellipsis whitespace-nowrap">All
        Toko</span>
    </label>
    @foreach ($drivers as $store_name => $driver_list)
    {{-- <div class="px-2 py-1 font-bold bg-gray-200">
      {{$store_name}}
    </div> --}}
    @foreach ($driver_list as $driver)
    @if($driver->store)
    <label for="{{ $driver->id }}" class="flex items-center px-4 text-sm border-b border-gray-200 border-solid h-11">
      <input type="checkbox" class="mr-2 _js-checkbox" id="{{ $driver->id }}" name="{{ $driver->id }}"
        value="{{ $driver->id }}">
      <strong class="mr-2">{{ $driver->name }}</strong> <span
        class="overflow-hidden overflow-ellipsis whitespace-nowrap">{{ $driver->store ? $driver->store->name : 'Kosong'
        }}</span>
    </label>
    @endif
    @endforeach
    @endforeach
  </div>

  <div class="fixed left-0 right-0 bottom-16 ">
    <div
      class="z-20 flex flex-col items-center justify-center max-w-xl px-4 pt-3 pb-6 mx-auto bg-white border-t-2 border-gray-300 border-solid shadow ">
      {{-- <h5 class="text-sm font-bold text-gray-400">Pilih MAX 6 Produk</h5> --}}
      <x-link href="#" class="w-full mt-3 bg-green-600 opacity-50 pointer-events-none _js-btn-report">
        Lihat Performa Delman 🛵
      </x-link>
    </div>
  </div>

  <x-slot name="js">
    <script>
      $( document ).ready(function() {
        let ids = [];
        const driverList = @json($drivers);
        const drivers = Object.entries(driverList).map((driver, key) => driver[1]).flat();

        $('._js-checkbox-all').change(function() {
          if(this.checked) {
            $('._js-checkbox').prop('checked', true);
            $('._js-btn-report').removeClass('opacity-50 pointer-events-none');
            ids = drivers.map((driver) => driver.id);
            console.log('ids', ids);
          } else {
            $('._js-checkbox').prop('checked', false);
            $('._js-btn-report').addClass('opacity-50 pointer-events-none');
            ids = [];
          }
          const url = window.location.href + '/' + ids.join('-');
          $('._js-btn-report').attr('href', url);
        });

        $('._js-checkbox').change(function() {
          const countAllCheckbox = $('._js-checkbox').length;
          const countChecked = $('._js-checkbox:checked').length;
          // if (ids.length == 6) {
          //   $(this).prop('checked', false);
          //   alert('Pilih MAX 6 Produk');
          //   return;
          // }
          if(this.checked) {
              ids.push(this.value);
            } else {
            ids.splice (ids.indexOf(this.value), 1);
          }
          if (countChecked === countAllCheckbox) {
            $('._js-checkbox-all')
              .prop('checked', true)
              .prop('indeterminate', false);
          } else if (countChecked > 0 && countChecked < countAllCheckbox) {
            $('._js-checkbox-all')
              .prop('checked', false)
              .prop('indeterminate', true);
          } else {
            $('._js-checkbox-all')
              .prop('checked', false)
              .prop('indeterminate', false);
          }
          if (ids.length == 0) {
            $('._js-btn-report').addClass('opacity-50 pointer-events-none');
          } else {
            $('._js-btn-report').removeClass('opacity-50 pointer-events-none');
          }
          const url = window.location.href + '/' + ids.join('-');
          $('._js-btn-report').attr('href', url);
        });
      });
    </script>
  </x-slot>

</x-app-layout>