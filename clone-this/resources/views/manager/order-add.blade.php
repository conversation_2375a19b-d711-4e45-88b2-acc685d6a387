<x-layout-addjob>
    <x-slot name="store_slug">
        {{ $store_slug }}
    </x-slot>

    <x-slot name="header">
        <a class="flex items-center -ml-3" href="{{ route('jobs', ['store_slug' => $store_slug]) }}"><svg
                class="w-9 h-9" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                stroke="currentColor">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7" />
            </svg>
            <h2 class="text-lg font-bold">
                Add Job
            </h2>
        </a>
        <select class="inline p-0 ml-auto text-lg font-bold bg-transparent border-none _js-select-store">
            @foreach ($stores as $item)
            <option value="{{ route('order-add', ['store_slug' => $item->slug]) }}" {{ $item->
                slug
                == $store_slug ?
                "selected" : null }}>
                {{ $item->name }}</option>
            @endforeach
        </select>
        {{-- <button type="button" class="ml-auto text-center focus:outline-none w-9 _js-btn-submit"><svg
                xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                <path
                    d="M7.707 10.293a1 1 0 10-1.414 1.414l3 3a1 1 0 001.414 0l3-3a1 1 0 00-1.414-1.414L11 11.586V6h5a2 2 0 012 2v7a2 2 0 01-2 2H4a2 2 0 01-2-2V8a2 2 0 012-2h5v5.586l-1.293-1.293zM9 4a1 1 0 012 0v2H9V4z" />
            </svg>
            <span class="block -mt-1 text-xs">Save</span></button> --}}
    </x-slot>

    <form action="{{ route('order-post', ['store_slug' => $store_slug]) }}" class="_js-form" method="POST">
        @csrf
        <input type="hidden" value="1" id="is_send_notif" name="is_send_notif" class="_js-input-is-send-notif">
        <div class="px-5 pt-20 pb-20">
            <div class="grid grid-cols-1 gap-6">
                {{-- <label class="block">
                    <div class="font-bold text-gray-700">Kode <span class="text-red-600">*</span></div>
                    <input type="text"
                        class="block w-full mt-1 text-gray-400 bg-gray-100 border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        value="GP786969" placeholder="">
                </label> --}}
                <label class="block">
                    <div class="font-bold text-gray-700">Tanggal & Jam <span class="text-red-600">*</span></div>
                    <input name="created_at" type="datetime-local" required="required" min="{{ date('Y-m-d\TH:i') }}"
                        max="{{ date('Y-m-d\TH:i', strtotime(" +7 days"))}}" value="{{ date('Y-m-d\TH:i') }}"
                        @if(in_array(auth()->user()->role_id, [4,6])) readonly @endif
                    class="block @if(in_array(auth()->user()->role_id, [4,6])) bg-gray-100 @endif w-full mt-1
                    border-gray-300
                    rounded-md shadow-sm _js-input-created_at focus:border-blue-300 focus:ring focus:ring-blue-200
                    focus:ring-opacity-50">
                </label>
                <div class="block">
                    {{-- <div class="font-bold text-gray-700">Pembayaran</span> --}}
                        <div class="mt-2">
                            @if(!in_array(auth()->user()->role_id, [4,6]))
                            <div>
                                <label class="inline-flex items-center">
                                    <input class="_js-input-oldnew _js-input-oldnew-old" type="radio" name="oldnew"
                                        id="oldnew" value="old">
                                    <span class="ml-2">Pelanggan Lama</span>
                                </label>
                            </div>
                            <div>
                                <label class="inline-flex items-center">
                                    <input class="_js-input-oldnew _js-input-oldnew-new" type="radio" name="oldnew"
                                        id="oldnew" value="new">
                                    <span class="ml-2">Pelanggan Baru</span>
                                </label>
                            </div>
                            @if(auth()->user()->role_id <= 3) <div>
                                <label class="inline-flex items-center">
                                    <input class="_js-input-oldnew _js-input-oldnew-marketing" type="radio"
                                        name="oldnew" id="oldnew" value="marketing">
                                    <span class="ml-2">Bagi Brosur</span>
                                </label>
                        </div>
                        @endif
                        @endif
                        @if(in_array(auth()->user()->role_id, [1,2,3,4,6])) <div>
                            <label class="inline-flex items-center">
                                <input class="_js-input-oldnew _js-input-oldnew-asdt" type="radio" name="oldnew"
                                    id="oldnew" value="asdt">
                                <span class="ml-2">ASDT</span>
                            </label>
                        </div>
                        <div>
                            <label class="inline-flex items-center">
                                <input class="_js-input-oldnew _js-input-oldnew-konsumsitoko" type="radio" name="oldnew"
                                    id="oldnew" value="konsumsitoko">
                                <span class="ml-2">Konsumsi Toko</span>
                            </label>
                        </div>
                        @endif
                    </div>
                </div>
                <input type="hidden" value="{{ $store->id }}" id="store_id" name="store_id">

                {{-- Pelanggan Lama --}}
                <div class="_js-wrp-old" style="display: none;">
                    <input type="hidden" value="" id="store_id_old" name="store_id_old" class="_js-input-store-old">
                    <label class="relative block mb-2.5" for="address_id">
                        <div id="input_pelanggan" class="absolute -top-16"></div>
                        <div class="font-bold text-gray-700">Pelanggan <span class="text-red-600">*</span></div>
                        <select id="address_id" name="address_id" required
                            class="block w-full mt-1 border-gray-300 rounded-md _js-input-pelanggan focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <option value="">Cari nama / no. wa / alamat ...</option>
                            {{-- <option value="">Test 1</option>
                            <option value="2">Test 2</option> --}}
                        </select>
                    </label>
                    <label class="inline-flex items-center mb-2.5">
                        <input type="checkbox" name="is_hide_deposit" id="is_hide_deposit" value="1"
                            class="text-blue-600 border-gray-300 rounded shadow-sm focus:border-blue-300 focus:ring focus:ring-offset-0 focus:ring-blue-200 focus:ring-opacity-50">
                        <span class="ml-2 font-bold text-blue-600">Sembunyikan DEPOSIT</span>
                    </label>
                    <label class="block" for="receiver_phone">
                        <div class="font-bold text-gray-700">No. WhatsApp Pemesan</div>
                        <input type="text" id="receiver_phone" name="receiver_phone"
                            class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-receiver-phone focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            placeholder="">
                        <p class="mt-2 text-sm text-gray-400">Selain No. WhatsApp asli pelanggan</p>
                    </label>
                </div>

                {{-- Pelanggan Baru --}}
                <div class="_js-wrp-new" style="display: none;">
                    <label class="block mb-6" for="store_id_new">
                        <div class="font-bold text-gray-700">Toko <span class="text-red-600">*</span></div>
                        <select id="store_id_new" name="store_id_new" required
                            class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-store-new focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <option value="">Pilih toko..</option>
                            @foreach ($stores as $str)
                            <option data-url="{{ route('order-add', ['store_slug' => $str->slug]) }}"
                                data-open_hour="{{ $str->open_hour }}" data-close_hour="{{ $str->close_hour }}"
                                value="{{ $str->id }}" {{ $store_slug==$str->slug ? 'selected="selected' : '' }}>
                                {{ $str->name }}</option>
                            @endforeach
                        </select>
                    </label>
                    <label class="block mb-6" for="customer_name">
                        <div class="font-bold text-gray-700">Nama Pelanggan Baru <span class="text-red-600">*</span>
                        </div>
                        <input type="text" id="customer_name" name="customer_name"
                            class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-new-customer-name focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            value="" placeholder="">
                    </label>
                    <label class="block mb-6" for="customer_phone">
                        <div class="font-bold text-gray-700">No. WA Pelanggan Baru <span class="text-red-600">*</span>
                        </div>
                        <input type="text" id="customer_phone" name="customer_phone"
                            class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-new-customer-phone focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            value="" placeholder="">
                    </label>
                    <label class="block mb-6" for="customer_address">
                        <div class="font-bold text-gray-700">Alamat Pelanggan Baru <span class="text-red-600">*</span>
                        </div>
                        <textarea id="customer_address" name="customer_address"
                            class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-new-customer-address focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            rows="2"></textarea>
                    </label>
                    <label class="block mb-6" for="customer_latlng">
                        <div class="font-bold text-gray-700">Koordinat (Latitude,Longitude)
                        </div>
                        <input type="text" id="customer_latlng" name="customer_latlng"
                            class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-new-customer-latlng focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            value="" placeholder="e.g. -7.123456,110.123456">
                    </label>
                    <div class="block mb-6">
                        <div class="font-bold text-gray-700">Catatan Tambahan</div>
                        <div class="flex flex-col gap-2 mt-1.5">
                            <div>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" value="1" name="is_secondfloor"
                                        class="text-red-600 border-gray-300 rounded shadow-sm _js-additionalcost-secondfloor-new focus:border-red-300 focus:ring focus:ring-offset-0 focus:ring-red-200 focus:ring-opacity-50">
                                    <span class="ml-2 font-semibold text-red-600">Jasa lantai 2 @Rp3.000</span>
                                </label>
                                <p class="-mt-1 text-sm text-gray-400">Tambah Biaya setiap ORDER</p>
                            </div>
                        </div>
                    </div>
                </div>

                {{-- Bagi Brosur --}}
                <div class="_js-wrp-marketing" style="display: none;">
                    <label class="block mb-6" for="store_id_marketing">
                        <div class="font-bold text-gray-700">Toko <span class="text-red-600">*</span></div>
                        <select id="store_id_marketing" name="store_id_marketing" required
                            class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-store-marketing focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <option value="">Pilih toko..</option>
                            @foreach ($stores as $str)
                            <option data-url="{{ route('order-add', ['store_slug' => $str->slug]) }}"
                                data-open_hour="{{ $str->open_hour }}" data-close_hour="{{ $str->close_hour }}"
                                value="{{ $str->id }}" {{ $store_slug==$str->slug ? 'selected="selected' : '' }}>
                                {{ $str->name }}</option>
                            @endforeach
                        </select>
                    </label>
                    <label class="relative block mb-6" for="area_id">
                        <div id="input_area" class="absolute -top-16"></div>
                        <div class="font-bold text-gray-700">Perumahan <span class="text-red-600">*</span></div>
                        <select required id="area_id" name="area_id"
                            class="block w-full mt-1 border-gray-300 rounded-md _js-input-area focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <option value="">Cari nama</option>
                            {{-- <option value="">Test 1</option>
                            <option value="2">Test 2</option> --}}
                        </select>
                    </label>
                    <label class="block" for="latlng">
                        <div class="font-bold text-gray-700">Koordinat <span class="text-red-600">*</span></div>
                        <div class="relative">
                            <span class="border-red-600 focus:border-red-300 focus:ring-red-200"></span>
                            <input type="text" id="latlng" name="latlng"
                                class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-coordinate focus:ring focus:ring-opacity-50 focus:border-blue-300 focus:ring-blue-200"
                                value="" placeholder="">
                            <div style="display: none;"
                                class="absolute top-0 bottom-0 z-10 flex items-center justify-center _js-loading-coordinate right-3">
                                <svg class="w-5 h-5 text-gray-500 animate-spin" xmlns="http://www.w3.org/2000/svg"
                                    fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                        stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor"
                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                    </path>
                                </svg>
                            </div>
                        </div>
                        <p class="mt-2 text-sm text-gray-400 break-all">Contoh: https://goo.gl/maps/acyGGxZ32hWhTwRPA
                        </p>
                    </label>
                </div>

                <div class="_js-wrp-old-new-asdt-konsumsitoko">
                    <hr>
                    <div class="block pt-3 _js-products">
                        <h3 class="mb-3 text-lg font-bold text-black">Keranjang</h3>
                        {{-- <h3 class="mb-3 text-lg font-bold text-black">Total Produk (<span
                                class="_js-total-product">0</span>)</h3> --}}
                        <div class="block p-3 mb-6 border-2 border-blue-200 rounded-lg bg-blue-50 _js-product-item _js-product-item-default"
                            style="display: none;">
                            <label for="product_id[]" class="block">
                                <div class="font-bold text-black">Produk</div>
                                <select id="product_id[]" name="product_id[]"
                                    class="block w-full mt-1 border-gray-300 rounded-md _js-input-product focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <option value="">Pilih produk..</option>
                                </select>
                            </label>
                            <label class="block mt-2" for="qty[]">
                                <div class="font-bold text-black">Qty</div>
                                <input type="number" min="1" id="qty[]" name="qty[]"
                                    class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-qty focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                    value="" placeholder="">
                            </label>
                            <button type="button"
                                class="w-full px-4 py-2 mt-5 text-sm font-bold tracking-widest text-red-600 uppercase border-2 border-red-600 rounded-md shadow-lg _js-btn-remove-product focus:outline-none bg-red-50">-
                                Hapus Produk</button>
                        </div>
                        <div id="input_product_init"
                            class="block p-3 mb-6 border-2 border-blue-200 rounded-lg bg-blue-50 _js-product-item">
                            <label for="product_id[]" class="block">
                                <div class="font-bold text-black">Produk</div>
                                <select id="product_id[]" name="product_id[]"
                                    class="block w-full mt-1 border-gray-300 rounded-md _js-input-product-init focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                    <option value="">Pilih produk..</option>
                                </select>
                            </label>
                            <label class="block mt-2 mb-2" for="qty[]">
                                <div class="font-bold text-black">Qty</div>
                                <input type="number" min="1" id="qty[]" name="qty[]"
                                    class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-qty-init focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                    value="" placeholder="">
                            </label>
                        </div>
                        <div class="_js-wrp-products">
                            {{-- <div
                                class="block p-3 mb-6 border-2 border-blue-200 rounded-lg bg-blue-50 _js-product-item">
                                <label for="product_id[]" class="block">
                                    <div class="font-bold text-black">Produk <span class="text-red-600">*</span>
                                    </div>
                                    <select id="product_id[]" name="product_id[]" required
                                        class="block w-full mt-1 border-gray-300 rounded-md _js-input-product _js-input-product-required focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        <option value="" checked>Token Listrik</option>
                                    </select>
                                </label>
                                <label class="block mt-2 mb-2" for="qty[]">
                                    <div class="font-bold text-black">No. Meter atau ID Pelanggan <span
                                            class="text-red-600">*</span></div>
                                    <input type="tel" id="qty[]" name="qty[]" required
                                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-qty _js-input-qty-required focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                        value="112233445566" placeholder="">
                                </label>
                                <label for="product_id[]" class="block">
                                    <div class="font-bold text-black">Nominal <span class="text-red-600">*</span>
                                    </div>
                                    <select id="product_id[]" name="product_id[]" required
                                        class="block w-full mt-1 border-gray-300 rounded-md _js-input-product _js-input-product-required focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                        <option value="" checked>100.000 - (Rp103.000)</option>
                                    </select>
                                </label>
                            </div> --}}
                        </div>
                        <button type="button"
                            class="w-full px-4 py-2 text-sm font-bold tracking-widest text-white uppercase bg-blue-500 rounded-md shadow-lg _js-btn-add-product focus:outline-none">+
                            Tambah Produk</button>
                    </div>
                </div>

                <hr>

                <label class="block _js-wrp-asdt-only" style="display: none;" for="receiver_phone_asdt">
                    <div class="font-bold text-gray-700">No. WhatsApp Pemesan</div>
                    <input type="text" id="receiver_phone_asdt" name="receiver_phone_asdt"
                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-receiver-phone focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        placeholder="">
                    {{-- <p class="mt-2 text-sm text-gray-400">Selain No. WhatsApp asli pelanggan</p> --}}
                </label>

                <div class="_js-wrp-old-new _js-wrp-asdt">
                    <label for="note" class="block">
                        <div class="font-bold text-gray-700">Catatan <span class="text-sm text-red-500">by
                                Pelanggan:
                                (KELUAR DI
                                NOTIF)</span></div>
                        <textarea id="note" name="note"
                            class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            rows="2"></textarea>
                    </label>
                </div>
                <div class="_js-wrp-marketing" style="display: none;">
                    <label class="relative block mb-1" for="driver_id">
                        <div id="input_delman" class="absolute -top-16"></div>
                        <div class="font-bold text-gray-700">Delman <span class="text-red-600">*</span></div>
                        <select id="driver_id" name="driver_id"
                            class="block w-full mt-1 border-gray-300 rounded-md _js-input-delman focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <option value="">Pilih delman...</option>
                            @foreach ($drivers as $driver)
                            <option value="{{ $driver->id }}">{{ $driver->name }}</option>
                            @endforeach
                            {{-- <option value="">Test 1</option>
                            <option value="2">Test 2</option> --}}
                        </select>
                    </label>
                </div>

                <label for="note_for_driver" class="block _js-wrp-old-new-marketing _js-wrp-asdt">
                    <div class="font-bold text-gray-700">Catatan <span class="text-sm text-blue-500">untuk
                            Delman / Internal</span>
                    </div>
                    <textarea id="note_for_driver" name="note_for_driver"
                        class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                        rows="2"></textarea>
                </label>
                <div class="block _js-wrp-old-new">
                    <div class="font-bold text-gray-700">Segera?</div>
                    <label class="inline-flex items-center mt-1">
                        <input type="checkbox" name="is_urgent" id="is_urgent" value="1"
                            class="text-red-600 border-gray-300 rounded shadow-sm focus:border-red-300 focus:ring focus:ring-offset-0 focus:ring-red-200 focus:ring-opacity-50">
                        <span class="ml-2">Ya, segera!</span>
                    </label>
                </div>
                <div class="block _js-wrp-old-new-asdt-konsumsitoko">
                    <div class="font-bold text-gray-700">Pembayaran</div>
                    <div class="mt-2">
                        <div>
                            <label class="inline-flex items-center">
                                <input type="radio" required name="payment" id="payment" value="cash" checked>
                                <span class="ml-2">Cash</span>
                            </label>
                        </div>
                        <div>
                            <label class="inline-flex items-center">
                                <input type="radio" required name="payment" id="payment" value="transfer">
                                <span class="ml-2">Transfer</span>
                            </label>
                        </div>
                        <div>
                            <label class="inline-flex items-center">
                                <input type="radio" required name="payment" id="payment" value="qris">
                                <span class="ml-2">QRIS</span>
                            </label>
                        </div>
                    </div>
                </div>

                {{-- Pelanggan Lama --}}
                <div class="_js-wrp-old" style="display: none;">
                    <hr class="mt-2 mb-6">
                    <div class="mb-6 _js-additionalcost-wrp" style="display: none;">
                        <div class="font-bold text-gray-700">Catatan Tambahan</div>
                        <div class="flex flex-col gap-2 mt-1.5">
                            <div>
                                <label class="inline-flex items-center">
                                    <input type="checkbox" value="1" name="is_secondfloor"
                                        class="text-red-600 border-gray-300 rounded shadow-sm _js-additionalcost-secondfloor focus:border-red-300 focus:ring focus:ring-offset-0 focus:ring-red-200 focus:ring-opacity-50">
                                    <span class="ml-2 font-semibold text-red-600">Jasa lantai 2 @Rp3.000</span>
                                </label>
                                <p class="-mt-1 text-sm text-gray-400">Tambah Biaya setiap ORDER</p>
                            </div>
                        </div>
                    </div>

                    <hr class="mt-8 mb-6">
                    <label class="inline-flex items-center mb-3">
                        <h3 class="mr-2 text-lg font-bold text-black">➕ Tambah Alamat Lain</h3>
                        <input type="checkbox" value="1" name="is_add_another_address"
                            class="border-gray-300 rounded shadow-sm _js-add-other-address ">
                    </label>
                    <div class="_js-wrp-another-address" style="display: none;">
                        <label class="block mb-6" for="store_id_another_address">
                            <div class="font-bold text-gray-700">Toko <span class="text-red-600">*</span></div>
                            <select id="store_id_another_address" name="store_id_another_address"
                                class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-store-another-address focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                                <option value="" selected>Pilih toko..</option>
                                @foreach ($stores as $str)
                                <option data-url="{{ route('order-add', ['store_slug' => $str->slug]) }}"
                                    data-open_hour="{{ $str->open_hour }}" data-close_hour="{{ $str->close_hour }}"
                                    value="{{ $str->id }}">
                                    {{ $str->name }}</option>
                                @endforeach
                            </select>
                        </label>
                        <label class="block mb-2" for="another_address">
                            <div class="font-bold text-gray-700">Alamat Lain Pelanggan <span
                                    class="text-red-600">*</span>
                            </div>
                            <textarea id="another_address" name="another_address"
                                class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-another-address-input-address focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                rows="2"></textarea>
                        </label>
                        <div class="mb-6">
                            <div class="flex flex-col gap-2 mt-1.5">
                                <div>
                                    <label class="inline-flex items-center">
                                        <input type="checkbox" value="1" name="another_address_is_secondfloor"
                                            class="text-red-600 border-gray-300 rounded shadow-sm _js-another-address-additionalcost-secondfloor focus:border-red-300 focus:ring focus:ring-offset-0 focus:ring-red-200 focus:ring-opacity-50">
                                        <span class="ml-2 font-semibold text-red-600">Jasa lantai 2 alamat di atas
                                            @Rp3.000</span>
                                    </label>
                                    <p class="-mt-1 text-sm text-gray-400">Tambah Biaya setiap ORDER</p>
                                </div>
                            </div>
                        </div>
                        <label class="block mb-6" for="another_address_latlng">
                            <div class="font-bold text-gray-700">Koordinat (Latitude,Longitude)
                            </div>
                            <input type="text" id="another_address_latlng" name="another_address_latlng"
                                class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-new-customer-latlng focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                                value="" placeholder="e.g. -7.123456,110.123456">
                        </label>
                    </div>
                </div>
                @if(auth()->user()->role_id <= 3) <hr>
                    <label class="block _js-wrp-old-new" for="currency-mask">
                        <h3 class="mb-3 text-lg font-bold text-black">➕ Tambah Deposit</h3>
                        <input type="text" id="currency-mask" name="deposit"
                            class="block w-full mt-1 text-right border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                            value="" placeholder="">
                        <p class="mt-2 text-sm text-gray-400">Bisa minus atau plus</p>
                    </label>
                    @endif
                    <div class="flex flex-col gap-4 mt-3 mb-5">
                        @if($store->is_notif_wa)
                        <x-button class="relative flex items-center w-full bg-red-600 _js-btn-submit">
                            <svg class="h-full text-white animate-spin w-7 _js-btn-loading" style="display: none;"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4">
                                </circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                            <svg class="h-full w-7 _js-btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
                            </svg>
                            <span class="flex items-center justify-center flex-1 h-full text-sm tracking-wide">Save
                                Job & Auto Send Notif</span>
                        </x-button>
                        @else
                        {{-- <x-button class="relative flex items-center w-full bg-red-600 _js-btn-submit">
                            <svg class="h-full text-white animate-spin w-7 _js-btn-loading" style="display: none;"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4">
                                </circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                            <svg class="h-full w-7 _js-btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                                    d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4" />
                            </svg>
                            <span class="flex items-center justify-center flex-1 h-full text-sm tracking-wide">Save
                                Job</span>
                        </x-button> --}}
                        <x-button data-with_send_notif="true"
                            class="relative flex items-center w-full bg-green-600 _js-btn-submit">
                            <svg class="h-full text-white animate-spin w-7 _js-btn-loading" style="display: none;"
                                xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor"
                                    stroke-width="4">
                                </circle>
                                <path class="opacity-75" fill="currentColor"
                                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z">
                                </path>
                            </svg>
                            <svg class="h-full w-7 _js-btn-icon" xmlns="http://www.w3.org/2000/svg" fill="none"
                                viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                    d="M6 12L3.269 3.126A59.768 59.768 0 0121.485 12 59.77 59.77 0 013.27 20.876L5.999 12zm0 0h7.5" />
                            </svg>
                            <span
                                class="flex items-center justify-center flex-1 h-full text-sm tracking-wide _js-submit-text">Submit
                                Job
                                &
                                Send Notif
                            </span>
                        </x-button>
                        @endif
                    </div>
            </div>
        </div>
    </form>

    <x-slot name="js">
        <script src="https://unpkg.com/imask"></script>
        <script>
            let timeout;
            let store_name;
            let open_hour;
            let close_hour;
            let store_id;
            function toPhone(number) {
                if (!number) return number;
                let num = number;
                num = num.replaceAll(" ", "");
                num = num.replaceAll("-", "");
                num = num.replaceAll("+", "");
                // if (num.substring(0, 2) === "62") {
                //     num = "0" + num.substring(2);
                // }
                return num;
            }
            $( document ).ready(function() {
                @if($errors->first('customer_phone'))
                    mdtoast("{{$errors->first('customer_phone')}}", { duration: 3000, type: mdtoast.ERROR });
                @endif
                const hash = window.location.hash;
                const current_store = @json($store);
                if (current_store) {
                    store_id = current_store.id;
                }

                // $('._js-input-receiver-phone').on('input', function(e) {
                //     const val = e.target.value;
                //     console.log("🚀 ~ val:", val)
                //     setTimeout(() => {
                //         $(this).val(toPhone(val.trim()));
                //     }, 500);
                // })

                @if(!in_array(auth()->user()->role_id, [4,6]))
                IMask(document.getElementById('currency-mask'), {
                    mask: Number,  // enable number mask

                    // other options are optional with defaults below
                    scale: 0,  // digits after point, 0 for integers
                    thousandsSeparator: '.',  // any single char
                    // padFractionalZeros: false,  // if true, then pads zeros at end to the length of scale
                    // normalizeZeros: true,  // appends or removes zeros at ends
                    // radix: ',',  // fractional delimiter
                    // mapToRadix: ['.'],  // symbols to process as radix

                    // additional number interval options (e.g.)
                    // min: -10000,
                    // max: 10000
                })
                @endif

                $('._js-select-store').change(function() {
                    const url = this.value;
                    window.location.replace(url+window.location.hash);
                });


                $('._js-input-store-new').change(function() {
                    const url = $('._js-input-store-new').find(":selected").data('url');
                    // console.log('url', url);
                    window.location.replace(url+window.location.hash);
                });
                $('._js-input-store-marketing').change(function() {
                    const url = $('._js-input-store-marketing').find(":selected").data('url');
                    // console.log('url', url);
                    window.location.replace(url+window.location.hash);
                });

                let totalProduct = 1;
                let productOptions = [];
                function generateRandomHexColors(numColors) {
  var colors = [];

  // Generate random RGB values for each color
  for (var i = 0; i < numColors; i++) {
    var r = Math.floor(Math.random() * 256);
    var g = Math.floor(Math.random() * 256);
    var b = Math.floor(Math.random() * 256);

    var hexColor = '#' + r.toString(16).padStart(2, '0') + g.toString(16).padStart(2, '0') + b.toString(16).padStart(2, '0');

    colors.push(hexColor);
  }

  return colors;
}

// function calculateContrastColor(hexColor) {
//   // Calculate the luminance
//   var r = parseInt(hexColor.substr(1, 2), 16);
//   var g = parseInt(hexColor.substr(3, 2), 16);
//   var b = parseInt(hexColor.substr(5, 2), 16);
//   var luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255;

//   // Determine the contrast color based on the luminance
//   var contrast = luminance > 0.5 ? '#000000' : '#FFFFFF';

//   return contrast;
// }

// function generateRandomContrastHexColors(numColors) {
//   var colors = generateRandomHexColors(numColors);
//   var contrastColors = [];

//   // Calculate contrast color for each color
//   for (var i = 0; i < numColors; i++) {
//     var hexColor = colors[i];
//     var contrastColor = calculateContrastColor(hexColor);
//     contrastColors.push([hexColor, contrastColor]);
//   }

//   return contrastColors;
// }

// // Generate 10 random hex colors with contrast colors
// // const randomColorsWithContrast = generateRandomContrastHexColors(20);
const randomColorsWithContrast = [
    '#000000',
    '#dc2626',
    '#d97706',
    '#65a30d',
    '#059669',
    '#0891b2',
    '#2563eb',
    '#7c3aed',
    '#be123c',
    '#450a0a',
    '#713f12',
    '#365314',
    '#164e63',
    '#4c1d95',
    '#701a75',
    '#881337',
    '#020617',
];
console.log("🚀 ~ randomColorsWithContrast:", randomColorsWithContrast)

                const $inputPelanggan = $('._js-input-pelanggan').selectize({
                    plugins: ['remove_button'],
                    valueField: 'id',
                    searchField: ['address', 'name', 'phone'],
                    create: false,
                    score: function() {
                        return function(search) {
                                return 1;
                        }
                    },
                    render: {
                        item: function(item, escape) {
                            return '<div class="" data-issecondfloor="' + escape(item.is_secondfloor) +'" data-store_id="' + escape(item.store_id) +'">' + 
                                '<div class="mt-1 mx-0.5 mb-1">👤 ' + escape(item.name) + (item.addresses_count > 1 ? ` <strong class="text-xs">(${item.addresses_count} Alamat)</strong>` : '') +'</div>' +
                                '<div class="mx-0.5 mb-1 text-xs text-gray-600">📱 ' + escape(item.phone) + '</div>' +
                                '<div class="mb-1 mx-0.5 text-sm text-gray-600">🏠 <span class="font-black text-black">'+escape(item.label)+':</span> ' + escape(item.address) + '</div>' +
                                '<div class="mb-1 mx-0.5 text-sm text-gray-600 font-bold" style="color: '+randomColorsWithContrast[item.store_id]+';">🛍 <span class="font-black">Toko</span> ' + escape(item.store_name) + '</div>' +
                                 (item.note_special ? '<div class="mb-1 mx-0.5 text-sm font-bold text-gray-600">📝 ' +escape(item.note_special) + '</div>': '')  +
                                 (item.deposit != 0 ? '<div class="mb-1 mx-0.5 text-lg font-bold" style="color: '+(item.deposit > 0 ? 'limegreen' : '') + (item.deposit < 0 ? 'red' : '') +'">🌿 '+(item.deposit < 0 ? '-' : '')+'Rp' +Math.abs(item.deposit).toLocaleString("id") + (item.deposit < 0 ? ' (KURANG BAYAR)' : ' (SISA DEPOSIT)')+'</div>': '')  +
                            '</div>';
                        },
                        option: function(item, escape) {
                            return '<div class="border-b border-gray-300" data-issecondfloor="' + escape(item.is_secondfloor) +'" data-store_id="' + escape(item.store_id) +'">' + 
                                '<div class="mx-3 mt-2 mb-1">👤 ' + escape(item.name) + (item.addresses_count > 1 ? ` <strong class="text-xs">(${item.addresses_count} Alamat)</strong>` : '') + '</div>' +
                                '<div class="mx-3 mb-1 text-xs text-gray-600">📱 ' + escape(item.phone) + '</div>' +
                                '<div class="mx-3 mb-1 text-sm text-gray-600">🏠 <span class="font-black text-black">'+escape(item.label)+':</span> ' + escape(item.address) + '</div>' +
                                '<div class="mx-3 mb-1 text-sm font-bold text-gray-600" style="color: '+randomColorsWithContrast[item.store_id]+';">🛍 <span class="font-black">Toko</span> ' + escape(item.store_name) + '</div>' +
                                (item.note_special ? '<div class="mx-3 mb-2 text-sm font-bold text-gray-600">📝 ' + escape(item.note_special) + '</div>' : '') +
                                (item.deposit != 0 ? '<div class="mx-3 mb-2 text-lg font-bold" style="color: '+(item.deposit > 0 ? 'limegreen' : '') + (item.deposit < 0 ? 'red' : '') +'">🌿 '+(item.deposit < 0 ? '-' : '')+'Rp' +Math.abs(item.deposit).toLocaleString("id") + (item.deposit < 0 ? ' (KURANG BAYAR)' : ' (SISA DEPOSIT)') +'</div>': '')  +
                            '</div>';
                        }
                    },
                    onFocus: function() {
                        $('#input_pelanggan')[0].scrollIntoView();
                    },
                    onType: function(str) {
                        console.log('str', str);
                        clearTimeout(timeout);
                        timeout = setTimeout(() => {
                            let val = str;
                            if (val.trim().startsWith() == '0' || val.trim().startsWith() == '+62' || val.trim().startsWith() == '62') {
                                console.log('masuk test');
                                val = val.replaceAll(' ', '');
                                val = val.replaceAll('-', '');
                                val = val.replaceAll('+', '');
                                val = val.replaceAll('(', '');
                                val = val.replaceAll(')', '');
                                if (val.substr(0, 1) == '0') {
                                    val = '62' + val.substr(1);
                                }
                                selectizePelanggan.setTextboxValue(val);
                                $.ajax({
                                    url: "{{ route('search-customer') }}",
                                    data: {
                                        store_id: current_store.id,
                                        query: val,
                                    },
                                    type: 'GET',
                                    error: function() {
                                        selectizePelanggan.clearOptions();
                                    },
                                    success: function(res) {
                                        console.log("🚀 ~ search customer:", res)
                                        const data = res.map(obj => {
                                            obj['name'] = obj.customer.name;
                                            obj['phone'] = obj.customer.phone;
                                            obj['note_special'] = obj.customer.note_special ? obj.customer.note_special : '';
                                            obj['deposit'] = obj.customer.deposit_amount ? obj.customer.deposit_amount : null;
                                            obj['store_name'] = obj.store.name ? obj.store.name : null;
                                            obj['store_id'] = obj.store.id ? obj.store.id : null;
                                            obj['addresses_count'] = obj.customer.addresses_count ? obj.customer.addresses_count : 0;
                                            if (obj.customer.merger) {
                                                const customer = obj.customer.merger.maincustomer;
                                                obj['name'] = customer.name;
                                                obj['id'] = customer.addresses[0].id;
                                                obj['label'] = customer.addresses[0].label;
                                                obj['address'] = customer.addresses[0].address;
                                                obj['is_secondfloor'] = customer.addresses[0].is_secondfloor;
                                                obj['note_special'] = customer.note_special ? customer.note_special : '';
                                                obj['deposit'] = customer.deposit_amount ? customer.deposit_amount : null;
                                                obj['addresses_count'] = customer.addresses_count ? customer.addresses_count : 0;
                                            }
                                            return obj;
                                        })
                                        console.log("🚀 ~ data:", data)
                                        selectizePelanggan.clearOptions();
                                        selectizePelanggan.addOption(data);
                                        selectizePelanggan.refreshOptions(true);
                                    }
                                });
                                // setTimeout(() => {
                                //     selectizePelanggan.open();
                                // }, 10);
                            }
                        }, 1000);
                    },
                    onChange: function(val) {
                        if (val) {
                            const $selectedOption = selectizePelanggan.getOption(val);
                            const isSecondFloor = parseInt($selectedOption.data('issecondfloor'));
                            const storeId = parseInt($selectedOption.data('store_id'));
                            store_id = storeId;
                            setTimeout(() => {
                                selectizePelanggan.blur();
                            }, 10);
                            $('._js-input-store-old').val(storeId);
                            $('._js-additionalcost-wrp').show();
                            $('._js-additionalcost-secondfloor').prop('checked', isSecondFloor ? true : false);
                        } else {
                            selectizePelanggan.clearOptions();
                            $('._js-input-store-old').val('');
                            $('._js-additionalcost-wrp').hide();
                            $('._js-additionalcost-secondfloor').prop('checked', false);
                        }
                        totalProduct = 1;
                        $('._js-total-product').html(totalProduct);
                        $('._js-wrp-products').empty();
                        selectizeProductInit.clear();
                        selectizeProductInit.clearOptions();
                        $.ajax({
                            url: "{{ route('search-product') }}",
                            data: {
                                store_id: store_id,
                                address_id: val,
                            },
                            type: 'GET',
                            error: function(error) {
                                console.log('error', error);
                            },
                            success: function(res) {
                                const data = res.map(obj => {
                                    const local_price = obj.local_price && parseInt(obj.local_price) > 0 ? obj.local_price : obj.price;
                                    obj['price'] = obj.special_price && parseInt(obj.special_price) > 0 ? obj.special_price : local_price;
                                    obj['stock'] = obj.special_stock ? obj.special_stock : obj.stock;
                                    obj['is_available'] = obj.special_is_available ? obj.special_is_available : obj.is_available;
                                    return obj;
                                })
                                selectizeProductInit.addOption(data);
                                productOptions = data;
                            }
                        });
                        $('._js-input-qty-init').val('');
                    },
                    load: function(query, callback) {
                        if (query.length >= 3) {
                            let val = query;
                            if (val.substring(0, 3) === "+62" || val.substring(0, 2) === "62" || val.substring(0, 2) === "08") {
                                val = toPhone(val);
                            }
                            $.ajax({
                                url: "{{ route('search-customer') }}",
                                data: {
                                    store_id: current_store.id,
                                    query: val,
                                },
                                type: 'GET',
                                error: function() {
                                    callback();
                                },
                                success: function(res) {
                                    console.log("🚀 ~ search customer:", res)
                                    const data = res.map(obj => {
                                        // if (!obj) return;
                                        obj['name'] = obj.customer.name;
                                        obj['phone'] = obj.customer.phone;
                                        obj['note_special'] = obj.customer.note_special ? obj.customer.note_special : '';
                                        obj['deposit'] = obj.customer.deposit_amount ? obj.customer.deposit_amount : null;
                                        obj['store_name'] = obj.store.name ? obj.store.name : null;
                                        obj['store_id'] = obj.store.id ? obj.store.id : null;
                                        obj['addresses_count'] = obj.customer.addresses_count ? obj.customer.addresses_count : 0;
                                        // if (obj.customer.merger) {
                                        //     const customer = obj.customer.merger.maincustomer;
                                        //     obj['name'] = customer.name;
                                        //     obj['id'] = customer.addresses[0].id;
                                        //     obj['label'] = customer.addresses[0].label;
                                        //     obj['address'] = customer.addresses[0].address;
                                        //     obj['note_special'] = customer.note_special ? customer.note_special : '';
                                        //     obj['deposit'] = customer.deposit_amount ? customer.deposit_amount : null;
                                        // }
                                        return obj;
                                    })
                                    selectizePelanggan.clearOptions();
                                    callback(data);
                                }
                            });
                        } else {
                            selectizePelanggan.clearOptions();
                            return callback();
                        }
                    }
                });
                const selectizePelanggan = $inputPelanggan[0].selectize;
                const selectizeProduct = {};

                let areaOptions = [];
                const $inputArea = $('._js-input-area').selectize({
                    plugins: ['remove_button'],
                    valueField: 'id',
                    searchField: ['name', 'latlng'],
                    create: true,
                    render: {
                        item: function(item, escape) {
                            return '<div class="">' + 
                                '<div class="mt-1 mx-0.5 mb-1">🏘 ' + escape(item.name ? item.name : item.text) + '</div>' +
                                // (item.latlng ?'<div class="mx-0.5 mb-1 text-xs text-gray-600">📍 ' + escape(item.latlng) + '</div>' : '<div class="mx-0.5 mb-1 text-xs text-gray-600">📍 Isi koordinat di bawah!</div>' )+
                                // '<div class="mb-1 mx-0.5 text-sm text-gray-600">🏠 <span class="font-black text-black">'+escape(item.label)+':</span> ' + escape(item.address) + '</div>' +
                                //  (item.note_special ? '<div class="mb-1 mx-0.5 text-sm font-bold text-gray-600">📝 ' +escape(item.note_special) + '</div>': '')  +
                                //  (item.deposit ? '<div class="mb-1 mx-0.5 text-lg font-bold" style="color: '+(item.deposit > 0 ? 'limegreen' : 'red')+'">🌿 '+(item.deposit < 0 ? '-' : '')+'Rp' +Math.abs(item.deposit).toLocaleString("id") + (item.deposit < 0 ? ' (KURANG BAYAR)' : ' (SISA DEPOSIT)')+'</div>': '')  +
                            '</div>';
                        },
                        option: function(item, escape) {
                            return '<div class="border-b border-gray-300">' + 
                                '<div class="mx-3 mt-2 mb-1">🏘 ' + escape(item.name ? item.name : item.text) + '</div>' +
                                // (item.latlng ?'<div class="mx-0.5 mb-1 text-xs text-gray-600">📍 ' + escape(item.latlng) + '</div>' : '<div class="mx-0.5 mb-1 text-xs text-gray-600">📍 Isi koordinat di bawah!</div>' )+
                                // '<div class="mx-3 mb-2 text-sm text-gray-600">🏠 <span class="font-black text-black">'+escape(item.label)+':</span> ' + escape(item.address) + '</div>' +
                                // (item.note_special ? '<div class="mx-3 mb-2 text-sm font-bold text-gray-600">📝 ' + escape(item.note_special) + '</div>' : '') +
                                // (item.deposit ? '<div class="mx-3 mb-2 text-lg font-bold" style="color: '+(item.deposit > 0 ? 'limegreen' : 'red')+'">🌿 '+(item.deposit < 0 ? '-' : '')+'Rp' +Math.abs(item.deposit).toLocaleString("id") + (item.deposit < 0 ? ' (KURANG BAYAR)' : ' (SISA DEPOSIT)') +'</div>': '')  +
                            '</div>';
                        },
                        // option_create: function(item) {
                        //     return '<div class="border-b border-gray-300">' + 
                        //         '<div class="mx-3 mt-2 mb-1">➕ ' + item.input + '</div>' +
                        //         // '<div class="mx-3 mb-2 text-sm text-gray-600">🏠 <span class="font-black text-black">'+escape(item.label)+':</span> ' + escape(item.address) + '</div>' +
                        //         // (item.note_special ? '<div class="mx-3 mb-2 text-sm font-bold text-gray-600">📝 ' + escape(item.note_special) + '</div>' : '') +
                        //         // (item.deposit ? '<div class="mx-3 mb-2 text-lg font-bold" style="color: '+(item.deposit > 0 ? 'limegreen' : 'red')+'">🌿 '+(item.deposit < 0 ? '-' : '')+'Rp' +Math.abs(item.deposit).toLocaleString("id") + (item.deposit < 0 ? ' (KURANG BAYAR)' : ' (SISA DEPOSIT)') +'</div>': '')  +
                        //     '</div>';
                        // }
                    },
                    onFocus: function() {
                        $('#input_area')[0].scrollIntoView();
                    },
                    // onType: function(str) {
                    //     // console.log('str', str);
                    //     clearTimeout(timeout);
                    //     timeout = setTimeout(() => {
                    //         let val = str;
                    //         if (val.substr(0, 1) == '0' || val.substr(0, 3) == '+62' || val.substr(0, 2) == '62') {
                    //             val = val.replaceAll(' ', '');
                    //             val = val.replaceAll('-', '');
                    //             val = val.replaceAll('+', '');
                    //             val = val.replaceAll('(', '');
                    //             val = val.replaceAll(')', '');
                    //             if (val.substr(0, 1) == '0') {
                    //                 val = '62' + val.substr(1);
                    //             }
                    //             selectizeArea.setTextboxValue(val);
                    //             $.ajax({
                    //                 url: "{{ route('search-customer') }}",
                    //                 data: {
                    //                     store_id: $('._js-input-store').val(),
                    //                     query: val,
                    //                 },
                    //                 type: 'GET',
                    //                 error: function() {
                    //                     selectizeArea.clearOptions();
                    //                 },
                    //                 success: function(res) {
                    //                     // console.log('res', res);
                    //                     const data = res.map(obj => {
                    //                         obj['name'] = obj.customer.name;
                    //                         obj['phone'] = obj.customer.phone;
                    //                         obj['note_special'] = obj.customer.note_special ? obj.customer.note_special : '';
                    //                         obj['deposit'] = obj.customer.deposit_amount ? obj.customer.deposit_amount : null;
                    //                         return obj;
                    //                     })
                    //                     selectizeArea.clearOptions();
                    //                     selectizeArea.addOption(data);
                    //                     selectizeArea.refreshOptions(true);
                    //                 }
                    //             });
                    //             // setTimeout(() => {
                    //             //     selectizeArea.open();
                    //             // }, 10);
                    //         }
                    //     }, 1000);
                    // },
                    onChange: function(val) {
                        if (val) {
                            const selectedArea = areaOptions.find(item => parseInt(item.id) === parseInt(val));
                            if (typeof selectedArea !== "undefined") {
                                $('._js-input-coordinate').val(selectedArea.latlng);
                            }
                            setTimeout(() => {
                                selectizeArea.blur();
                                // selectizeArea.clearOptions();
                            }, 10);
                        } else {
                            selectizeArea.clearOptions();
                            $('._js-input-coordinate').val('');
                        }
                    },
                    load: function(query, callback) {
                        if (query.length >= 3) {
                            $.ajax({
                                url: "{{ route('search-area') }}",
                                data: {
                                    // store_id: $('._js-input-store').val(),
                                    store_id: store_id,
                                    query: query,
                                },
                                type: 'GET',
                                error: function() {
                                    callback();
                                },
                                success: function(res) {
                                    // console.log('res', res);
                                    const data = res.map(obj => {
                                        obj['name'] = obj.name;
                                        obj['latlng'] = obj.latlng;
                                        return obj;
                                    })
                                    areaOptions = data;
                                    callback(data);
                                }
                            });
                        } else {
                            selectizeArea.clearOptions();
                            return callback();
                        }
                    }
                });
                const selectizeArea = $inputArea[0].selectize;

                function setInputError(input) {
                    input.removeClass('border-gray-300 focus:border-blue-300 focus:ring-blue-200')
                    input.addClass('border-red-600 focus:border-red-300 focus:ring-red-200')
                }
                function setInputDefault(input) {
                    input.removeClass('border-red-600 focus:border-red-300 focus:ring-red-200')
                    input.addClass('border-gray-300 focus:border-blue-300 focus:ring-blue-200')
                }

                function getUrlOnly(input) {
                    if(!input) return '';
                    
                    // const urlRegex = /(((https?:\/\/)|(www\.))[^\s]+)/g;
                    // const urlRegex = /^(http[s]?:\/\/.*?\/[a-zA-Z-_]+.*)$/;
                    const urlRegex = /(https?:\/\/[^ ]*)/;
                    return input.match(urlRegex)[1];
                    // return message.replace(urlRegex, function (url) {
                    //     let hyperlink = url;
                    //     if (!hyperlink.match('^https?:\/\/')) {
                    //     hyperlink = 'http://' + hyperlink;
                    //     }
                    //     // return '<a href="' + hyperlink + '" target="_blank" rel="noopener noreferrer">' + url + '</a>'
                    //     return hyperlink;
                    // });
                }

                // async function getCoordinateFromUrl(url) {
                //     if (url.includes('/@')) {
                //         let latlng = url;
                //         const url = latlng.split('/@');
                //         if (url[1]) {
                //             const at = url[1].split('z');
                //             if (at[0]) {
                //                 const zero = at[0].split(',');
                //                 if (zero[0] && zero[1]) {
                //                     const lat = zero[0];
                //                     const lng = zero[1];
                //                     latlng = lat+','+lng;
                //                 }
                //             }
                //         }
                //         return latlng;
                //     } else {
                //             console.log('url', "{{ route('get-coordinate-gmap') }}"+'?url='+encodeURIComponent(url));
                //             const res = await axios.get("{{ route('get-coordinate-gmap') }}"+'?url='+encodeURIComponent(url));
                //             // let latlng = url;
                //             // let clone = res.clone();
                //             console.log('res', res.data);
                //             // const arr = clone.text().split('/@');
                //             // if (arr[1]) {
                //             //     const zero = arr[0].split(',');
                //             //     if (zero[0] && zero[1]) {
                //             //         const lat = zero[0];
                //             //         const lng = zero[1];
                //             //         latlng = lat+','+lng;
                //             //     }
                //             // }
                //             if (res && res.data) {
                //                 return res.data;
                //             }
                //             return url;
                //         // } catch (error) {
                //         //     console.log('error', error);
                //         //     return url;
                //         // }
                //     }
                // }

                let timeoutGetCoordinate;
                $('._js-input-coordinate').on('input', async function(e) {
                    const $this = $(this);
                    const val = $this.val();
                    if (!val) {
                        setInputDefault($this);
                    }
                    clearTimeout(timeoutGetCoordinate);
                    timeoutGetCoordinate = setTimeout(async () => {
                        const regexLatLng = /^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/g;
                        const passLatLng = regexLatLng.test(val);

                        if (!passLatLng) {
                            const url = getUrlOnly(val);
                            const regexGmapUrl = /(\google.com+)/g;
                            const regexGmapShare = /(\goo.gl+)/g;
                            const regexGPage = /(\g.page+)/g;
                            const passGmapShare = regexGmapShare.test(url);
                            const passGmapUrl = regexGmapUrl.test(url);
                            const passGPage = regexGPage.test(url);

                            if (passGmapUrl || passGmapShare) {
                                console.log('passGmapShare');
                                $('._js-loading-coordinate').show();
                                $this.prop('disabled', true);
                                $.ajax({
                                    url: "{{ route('get-coordinate-gmap') }}",
                                    data: {
                                        url: url,
                                    },
                                    type: 'GET',
                                    error: function(err) {
                                        console.log('err', err);
                                        $('._js-loading-coordinate').hide();
                                        $this.prop('disabled', false);
                                        setInputDefault($this);
                                        mdtoast('TERJADI KESALAHAN', { duration: 1500, type: mdtoast.ERROR });
                                    },
                                    success: function(res) {
                                        console.log('get-coordinate-gmap res', res);
                                        // const coordinate = getCoordinateFromUrl(res);
                                        // console.log('coordinate', coordinate);
                                        $this.val(res);
                                        $('._js-loading-coordinate').hide();
                                        $this.prop('disabled', false);
                                        setInputDefault($this);
                                    }
                                });
                            // } else if (passGmapUrl) {
                            //     console.log('passGmapUrl');
                            //     const coordinate = await getCoordinateFromUrl(getUrlOnly(val));
                            //     $this.val(coordinate);
                            //     setInputDefault($this);
                            } else {
                                setInputError($this);
                                mdtoast('INPUT TIDAK VALID', { duration: 1500, type: mdtoast.ERROR });
                            }
                        }  else {
                            setInputDefault($this);
                        }
                    }, 500);
                });

                const $inputProductInit = $('._js-input-product-init').selectize({
                    plugins: ['remove_button'],
                    valueField: 'id',
                    searchField: ['code', 'name'],
                    create: false,
                    // preload: true,
                    render: {
                        item: function(item, escape) {
                            console.log('item', item);
                            const isUnavailable = !parseInt(item.is_available);
                            return '<div class="flex'+ (isUnavailable && ' opacity-50 pointer-events-none') +'">' + 
                                '<div class="mt-1 mb-1 ml-1 mr-1">' + 
                                    '<img class="object-contain w-11 h-11" src="'+ item.featurephoto_url +'">' +         
                                '</div>' + 
                                '<div class="mt-1 mb-1 mr-1">' + 
                                    '<div class="mb-1">' + 
                                        '<span class="font-bold">' + escape(item.code) + '</span>' +
                                        '<span class="ml-1">' + escape(item.name) + '</span>' +
                                        (!parseInt(item.is_available) ? '<span class="ml-1 text-xs text-red-500">Tidak Tersedia</span>' : '') +
                                    '</div>' + 
                                    '<div class="mb-1 text-xs text-gray-500">' +
                                        '(Rp' + escape(item.price.toLocaleString('id')) + ')'+
                                        (parseInt(item.stock) !== 999 ? ' ~ Stok: ' + escape(item.stock.toLocaleString('id')) : '') +
                                    '</div>' +
                                '</div>' + 
                            '</div>';
                        },
                        option: function(item, escape) {
                            const isUnavailable = !parseInt(item.is_available);
                            return '<div class="flex border-b border-gray-300'+ (isUnavailable && ' opacity-50 pointer-events-none') +'">' + 
                                '<div class="mt-2 mb-1 ml-2 mr-2">' + 
                                    '<img class="object-contain w-11 h-11" src="'+ item.featurephoto_url +'">' +         
                                '</div>' + 
                                '<div class="mt-2 mb-1 mr-2">' + 
                                    '<div class="mb-1">' + 
                                        '<span class="font-bold">' + escape(item.code) + '</span>' +
                                        '<span class="ml-1">' + escape(item.name) + '</span>' +
                                        (!parseInt(item.is_available) ? '<span class="ml-1 text-xs text-red-500">Tidak Tersedia</span>' : '') +
                                    '</div>' + 
                                    '<div class="mb-1 text-xs text-gray-500">' +
                                        '(Rp' + escape(item.price.toLocaleString('id')) + ')'+
                                        (parseInt(item.stock) !== 999 ? ' ~ Stok: ' + escape(item.stock.toLocaleString('id')) : '') +
                                    '</div>' +
                                '</div>' + 
                            '</div>';
                        }
                    },
                    onFocus: function() {
                        $('#input_product_init')[0].scrollIntoView();
                        window.scrollBy(0, -65);
                    },
                    onChange: function(val) {
                        if (val) {
                            setTimeout(() => {
                                selectizeProductInit.blur();
                            }, 125);
                        }
                        // } else {
                        //     selectizeProductInit.clearOptions();
                        // }
                    },
                    // load: function(query, callback) {
                    //     // if (query.length >= 3) {
                    //         $.ajax({
                    //             url: "{{ route('search-product') }}",
                    //             data: {
                    //                 store_id: $('._js-input-store').val(),
                    //                 query: query,
                    //             },
                    //             type: 'GET',
                    //             error: function() {
                    //                 callback();
                    //             },
                    //             success: function(res) {
                    //                 // console.log(res);
                    //                 const data = res.map(obj => {
                    //                     obj['price'] = obj.local_price ? obj.local_price : obj.price;
                    //                     return obj;
                    //                 })
                    //                 callback(data);
                    //             }
                    //         });
                    //     // } else {
                    //         // selectizeProductInit.clearOptions();
                    //         // return callback();
                    //     // }
                    // }
                });
                const selectizeProductInit = $inputProductInit[0].selectize;
                $.ajax({
                    url: "{{ route('search-product') }}",
                    data: {
                        // store_id: $('._js-input-store').val(),
                        store_id: store_id,
                        address_id: $('._js-input-pelanggan').val(),
                    },
                    type: 'GET',
                    error: function(error) {
                        console.log('error', error);
                    },
                    success: function(res) {
                        console.log('res', res);
                        const data = res.map(obj => {
                            const local_price = obj.local_price && parseInt(obj.local_price) > 0 ? obj.local_price : obj.price;
                            obj['price'] = obj.special_price && parseInt(obj.special_price) > 0 ? obj.special_price : local_price;
                            return obj;
                        })
                        selectizeProductInit.addOption(data);
                        productOptions = data;
                    }
                });

                // console.log("🚀 ~ current_store:", current_store)
                // open_hour = $('._js-input-store').find(":selected").data('open_hour');
                // close_hour = $('._js-input-store').find(":selected").data('close_hour');
                // store_name = $('._js-input-store').find(":selected").text();
                open_hour = current_store ? current_store.open_hour : '';
                close_hour = current_store ? current_store.close_hour : '';
                store_name = current_store ? current_store.name : '';
                // $('._js-input-store').change(function() {
                //     open_hour = $(this).find(":selected").data('open_hour');
                //     close_hour = $(this).find(":selected").data('close_hour');
                //     store_name = $(this).find(":selected").text();
                //     totalProduct = 1;
                //     $('._js-total-product').html(totalProduct);
                //     $('._js-wrp-products').empty();
                //     selectizePelanggan.clear();
                //     selectizePelanggan.clearOptions();
                //     selectizeProductInit.clear();
                //     selectizeProductInit.clearOptions();
                //     $.ajax({
                //         url: "{{ route('search-product') }}",
                //         data: {
                //             store_id: $('._js-input-store').val(),
                //         },
                //         type: 'GET',
                //         error: function(error) {
                //             console.log('error', error);
                //         },
                //         success: function(res) {
                //             // console.log('res', res);
                //             const data = res.map(obj => {
                //                 obj['price'] = obj.local_price && parseInt(obj.local_price) > 0 ? obj.local_price : obj.price;
                //                 return obj;
                //             })
                //             selectizeProductInit.addOption(data);
                //             productOptions = data;
                //         }
                //     });
                //     $.ajax({
                //         url: "{{ route('get-drivers') }}",
                //         data: {
                //             store_id: $('._js-input-store').val(),
                //         },
                //         type: 'GET',
                //         error: function(error) {
                //             console.log('error', error);
                //         },
                //         success: function(res) {
                //             // console.log('drivers', res);
                //             const $inputDriver = $('._js-input-delman');
                //             $inputDriver.val();
                //             $inputDriver.empty();
                //             $inputDriver.append($("<option></option>").attr("value", '').text('Pilih delman...'));
                //             if (res && res.length > 0) {
                //                 $.each(res, function(key,value) {
                //                     $inputDriver.append($("<option></option>").attr("value", value.id).text(value.name));
                //                 });
                //             }
                //         }
                //     });
                //     $('._js-input-qty-init').val('');
                //     const val = this.value;
                //     if (!val) {
                //         selectizePelanggan.disable();
                //         selectizeProductInit.disable();
                //     } else {
                //         selectizePelanggan.enable();
                //         selectizeProductInit.enable();
                //     }
                // });

                function setAddAnotherAddressOff() {
                    $('._js-wrp-another-address').hide();
                    $('._js-add-other-address').prop('checked', false);
                    $('._js-another-address-input-address').prop('required', false);
                    $('._js-input-store-another-address').prop('required', false);
                    $('._js-another-address-additionalcost-secondfloor').prop('checked', false);
                }

                function setAddAnotherAddressOn() {
                    $('._js-wrp-another-address').show();
                    $('._js-add-other-address').prop('checked', true);
                    $('._js-another-address-input-address').val('').prop('required', true);
                    $('._js-input-store-another-address').val('').prop('required', true);
                    $('._js-another-address-additionalcost-secondfloor').prop('checked', false);
                }

                $('._js-add-other-address').change(function() {
                    const isChecked = this.checked;
                    if (isChecked) {
                        setAddAnotherAddressOn();
                    } else {
                        setAddAnotherAddressOff();
                    }
                })

                function requiredInputOld() {
                    $('._js-wrp-marketing').hide();
                    $('._js-wrp-new').hide();
                    $('._js-wrp-old-new').show();
                    $('._js-wrp-old').show();
                    $('._js-wrp-old-new-asdt-konsumsitoko').show();
                    $('._js-wrp-old-new-marketing').show();
                    $('._js-input-new-customer-name').val('').prop('required', false);
                    $('._js-input-new-customer-phone').val('').prop('required', false);
                    $('._js-input-new-customer-address').val('').prop('required', false);
                    $('._js-input-new-customer-latlng').val('').prop('required', false);
                    $('._js-input-receiver-phone').val('');
                    selectizePelanggan.clear();
                    $('._js-input-pelanggan').val('').prop('required', true);
                    $('.selectize-control._js-input-pelanggan').find('input').prop('required', true);
                    selectizeArea.clear();
                    $('._js-input-area').val('').prop('required', false);
                    $('.selectize-control._js-input-area').find('input').prop('required', false);
                    $('._js-input-coordinate').val('').prop('required', false);
                    $('.selectize-control._js-input-product-required').find('input').prop('required', true);
                    $('._js-input-product-required').prop('required', true);
                    // $('._js-input-product-init').prop('required', false);
                    // $('.selectize-control._js-input-product-init').find('input').prop('required', false);
                    $('._js-input-qty-required').prop('required', true);
                    $('._js-input-delman').prop('required', false);
                    $('._js-submit-text').html('Submit Job & Send Notif');
                }

                function onClickNewCustomer() {
                    $('._js-wrp-marketing').hide();
                    $('._js-wrp-old').hide();
                    $('._js-wrp-old-new').show();
                    $('._js-wrp-old-new-asdt-konsumsitoko').show();
                    $('._js-wrp-new').show();
                    $('._js-wrp-old-new-marketing').show();
                    $('._js-input-new-customer-name').val('').prop('required', true);
                    $('._js-input-new-customer-phone').val('').prop('required', true);
                    $('._js-input-new-customer-address').val('').prop('required', true);
                    $('._js-input-new-customer-latlng').val('').prop('required', false);
                    $('._js-input-receiver-phone').val('');
                    selectizePelanggan.clear();
                    $('._js-input-pelanggan').val('').prop('required', false);
                    $('.selectize-control._js-input-pelanggan').find('input').prop('required', false);
                    selectizeArea.clear();
                    $('._js-input-area').val('').prop('required', false);
                    $('.selectize-control._js-input-area').find('input').prop('required', false);
                    $('._js-input-coordinate').val('').prop('required', false);
                    $('.selectize-control._js-input-product-required').find('input').prop('required', true);
                    $('._js-input-product-required').prop('required', true);
                    // $('._js-input-product-init').prop('required', false);
                    // $('.selectize-control._js-input-product-init').find('input').prop('required', false);
                    $('._js-input-qty-required').prop('required', true);
                    $('._js-input-delman').prop('required', false);
                    $('._js-submit-text').html('Submit Job & Send Notif');
                }

                function onClickJobMarketing() {
                    $('._js-wrp-new').hide();
                    $('._js-wrp-old').hide();
                    $('._js-wrp-old-new').hide();
                    $('._js-wrp-old-new-asdt-konsumsitoko').hide();
                    $('._js-wrp-old-new-marketing').show();
                    $('._js-wrp-marketing').show();
                    $('._js-input-new-customer-name').val('').prop('required', false);
                    $('._js-input-new-customer-phone').val('').prop('required', false);
                    $('._js-input-new-customer-address').val('').prop('required', false);
                    $('._js-input-new-customer-latlng').val('').prop('required', false);
                    $('._js-input-receiver-phone').val('');
                    selectizePelanggan.clear();
                    $('._js-input-pelanggan').val('').prop('required', false);
                    $('.selectize-control._js-input-pelanggan').find('input').prop('required', false);
                    selectizeArea.clear();
                    $('._js-input-area').val('').prop('required', true);
                    // $('.selectize-control._js-input-area').find('input').prop('required', true);
                    $('._js-input-coordinate').val('').prop('required', true);
                    $('.selectize-control._js-input-product-required').find('input').prop('required', false);
                    $('._js-input-product-required').prop('required', false);
                    // $('._js-input-product-init').prop('required', false);
                    // $('.selectize-control._js-input-product-init').find('input').prop('required', false);
                    $('._js-input-qty-required').prop('required', false);
                    $('._js-input-delman').prop('required', true);
                    $('._js-submit-text').html('Submit Job');
                }

                function onClickAsdtKonsumsiToko() {
                    $('._js-wrp-new').hide();
                    $('._js-wrp-old').hide();
                    $('._js-wrp-old-new').hide();
                    $('._js-wrp-marketing').hide();
                    $('._js-wrp-old-new-marketing').hide();
                    $('._js-wrp-old-new-asdt-konsumsitoko').show();
                    $('._js-input-new-customer-name').val('').prop('required', false);
                    $('._js-input-new-customer-phone').val('').prop('required', false);
                    $('._js-input-new-customer-address').val('').prop('required', false);
                    $('._js-input-new-customer-latlng').val('').prop('required', false);
                    $('._js-input-receiver-phone').val('');
                    selectizePelanggan.clear();
                    $('._js-input-pelanggan').val('').prop('required', false);
                    $('.selectize-control._js-input-pelanggan').find('input').prop('required', false);
                    selectizeArea.clear();
                    $('._js-input-area').val('').prop('required', false);
                    $('.selectize-control._js-input-area').find('input').prop('required', false);
                    $('._js-input-coordinate').val('').prop('required', false);
                    $('.selectize-control._js-input-product-required').find('input').prop('required', true);
                    $('._js-input-product-required').prop('required', true);
                    // $('._js-input-product-init').prop('required', true);
                    // $('.selectize-control._js-input-product-init').find('input').prop('required', true);
                    $('._js-input-qty-required').prop('required', true);
                    $('._js-input-delman').prop('required', false);
                    $('._js-submit-text').html('Submit Job');
                }

                $('._js-input-oldnew').change(function() {
                    const val = this.value;
                    window.location.hash = val;
                    console.log("🚀 ~ val:", val)
                    $('._js-additionalcost-wrp').hide();
                    $('._js-additionalcost-secondfloor').prop('checked', false);
                    $('._js-additionalcost-secondfloor-new').prop('checked', false);
                    if (val === 'old') {
                        requiredInputOld();
                        $('._js-wrp-asdt-only').hide();
                    } else if (val === 'new') {
                        onClickNewCustomer();
                        $('._js-wrp-asdt-only').hide();
                    } else if (val === 'marketing') {
                        onClickJobMarketing();
                        $('._js-wrp-asdt-only').hide();
                    } else if (val === 'asdt') {
                        onClickAsdtKonsumsiToko();
                        $('._js-wrp-asdt').show();
                        $('._js-wrp-asdt-only').show();
                    } else if (val === 'konsumsitoko') {
                        onClickAsdtKonsumsiToko();
                        $('._js-wrp-asdt-only').hide();
                    }
                    setAddAnotherAddressOff();
                });

                $('._js-btn-add-product').on('click', function() {
                    totalProduct++;
                    const localId = Math.floor(Math.random() * 100) + 1;
                    $('._js-total-product').html(totalProduct);
                    $('._js-product-item-default').clone().removeClass('_js-product-item-default').prop('id', 'input_product_'+localId).show().appendTo('._js-wrp-products');
                    const $parent = $('#input_product_'+localId);
                    $parent.find('._js-input-qty').prop('required', true).addClass('_js-input-qty-required');
                    $parent.find('._js-input-product').prop('required', true).addClass('_js-input-product-required');
                    const $inputProduct = $parent.find('._js-input-product').selectize({
                        plugins: ['remove_button'],
                        valueField: 'id',
                        searchField: ['code', 'name'],
                        options: productOptions,
                        create: false,
                        render: {
                            item: function(item, escape) {
                                const isUnavailable = !parseInt(item.is_available);
                            return '<div class="flex'+ (isUnavailable && ' opacity-50 pointer-events-none') +'">' + 
                                    '<div class="mt-1 mb-1 ml-1 mr-1">' + 
                                        '<img class="object-contain w-11 h-11" src="'+ item.featurephoto_url +'">' +         
                                    '</div>' + 
                                    '<div class="mt-1 mb-1 mr-1">' + 
                                        '<div class="mb-1">' + 
                                            '<span class="font-bold">' + escape(item.code) + '</span>' +
                                            '<span class="ml-1">' + escape(item.name) + '</span>' +
                                            (!parseInt(item.is_available) ? '<span class="ml-1 text-xs text-red-500">Tidak Tersedia</span>' : '') +
                                        '</div>' + 
                                        '<div class="mb-1 text-xs text-gray-500">(Rp' + escape(item.price.toLocaleString('id')) + ')</div>' +
                                    '</div>' + 
                                '</div>';
                            },
                            option: function(item, escape) {
                                console.log('item', item);
                                const isUnavailable = !parseInt(item.is_available);
                            return '<div class="flex border-b border-gray-300'+ (isUnavailable && ' opacity-50 pointer-events-none') +'">' + 
                                    '<div class="mt-2 mb-1 ml-2 mr-2">' + 
                                        '<img class="object-contain w-11 h-11" src="'+ item.featurephoto_url +'">' +         
                                    '</div>' + 
                                    '<div class="mt-2 mb-1 mr-2">' + 
                                        '<div class="mb-1">' + 
                                            '<span class="font-bold">' + escape(item.code) + '</span>' +
                                            '<span class="ml-1">' + escape(item.name) + '</span>' +
                                            (!parseInt(item.is_available) ? '<span class="ml-1 text-xs text-red-500">Tidak Tersedia</span>' : '') +
                                        '</div>' + 
                                        '<div class="mb-1 text-xs text-gray-500">' +
                                        '(Rp' + escape(item.price.toLocaleString('id')) + ')'+
                                        (parseInt(item.stock) !== 999 ? ' ~ Stok: ' + escape(item.stock.toLocaleString('id')) : '') +
                                    '</div>' +
                                    '</div>' + 
                                '</div>';
                            }
                        },
                        onFocus: function() {
                            $('#input_product_'+localId)[0].scrollIntoView();
                            window.scrollBy(0, -65);
                            setTimeout(() => {
                                selectizeProductInit.blur();
                            }, 10);
                        },
                        onChange: function(val) {
                            if (val) {
                                setTimeout(() => {
                                    selectizeProduct[localId].blur();
                                }, 10);
                            }
                            // } else {
                            //     selectizeProduct[localId].clearOptions();
                            // }
                        },
                        // load: function(query, callback) {
                        //     if (query.length >= 3) {
                        //         $.ajax({
                        //             url: "{{ route('search-product') }}",
                        //             data: {
                        //                 store_id: $('._js-input-store').val(),
                        //                 query: query,
                        //             },
                        //             type: 'GET',
                        //             error: function() {
                        //                 callback();
                        //             },
                        //             success: function(res) {
                        //                 // console.log(res);
                        //                 const data = res.map(obj => {
                        //                     obj['code'] = obj.product.code;
                        //                     obj['name'] = obj.product.name;
                        //                     obj['price'] = obj.local_price ? obj.local_price : obj.product.price;
                        //                     return obj;
                        //                 })
                        //                 callback(data);
                        //             }
                        //         });
                        //     } else {
                        //         selectizeProduct[localId].clearOptions();
                        //         return callback();
                        //     }
                        // }
                    });
                    selectizeProduct[localId] = $inputProduct[0].selectize;
                });

                $('._js-products').on('click', '._js-btn-remove-product', function() {
                    if (confirm('Hapus produk?')) {
                        totalProduct--;
                        $('._js-total-product').html(totalProduct);
                        $(this).parents('._js-product-item').remove();
                    }
                });

                function submitForm(withSendNotif = false) {
                    console.log("$('._js-form')[0].reportValidity()", $('._js-form')[0].reportValidity());
                    if ($('._js-form')[0].reportValidity()) {
                        if ($('._js-input-oldnew').val() === 'new') {
                            axios.post("{{ route('validate-customer') }}", {
                                // store_id: $('._js-input-store').val(),
                                store_id: store_id,
                                // name: $('._js-input-new-customer-name').val(),
                                phone: $('._js-input-new-customer-phone').val(),
                                // address: $('._js-input-new-customer-address').val(),
                            })
                            .then(function (res) {
                                // console.log('res', res);
                                if (res && res.data) {
                                    mdtoast('NO. WA SUDAH TERDAFTAR', { duration: 3000, type: mdtoast.ERROR });
                                    $('._js-input-new-customer-phone').addClass('text-red-600').focus();
                                } else {
                                    $(this).css({'opacity': 0.5, 'cursor': 'wait'}).prop("disabled",true);
                                    $('._js-btn-icon').hide();
                                    $('._js-btn-loading').show();
                                    $('._js-form').submit();
                                }
                            })
                            .catch(function (error) {
                                console.log('error', error);
                                mdtoast('VALIDASI GAGAL', { duration: 1500, type: mdtoast.ERROR });
                            });
                        } else {
                            $(this).css({'opacity': 0.5, 'cursor': 'wait'}).prop("disabled",true);
                            $('._js-btn-icon').hide();
                            $('._js-btn-loading').show();
                            // if (withSendNotif) {
                            //     $('._js-input-is-send-notif').val("1");
                            // } else {
                            //     $('._js-input-is-send-notif').val("0");
                            // }
                            $('._js-form').submit();
                        }
                    }
                }

                $('._js-btn-submit').on('click', function(e) {
                    const withSendNotif = $(this).data('with_send_notif');
                    console.log("🚀 ~ withSendNotif:", withSendNotif)
                    const oh = moment(open_hour, 'hh:mm:ss');
                    const ch = moment(close_hour, 'hh:mm:ss');
                    if (!moment($('._js-input-created_at').val()).isBetween(oh, ch)) {
                        if (confirm(`Maaf, jam operasional ${store_name.trim()}:\n${oh.format('hh:mm A')} - ${ch.format('hh:mm A')}\nLanjutkan SAVE JOB?`)) {
                            submitForm(withSendNotif || false);
                        } else {
                            e.preventDefault();
                            return false;
                        }
                    } else {
                        submitForm(withSendNotif || false);
                    }
                });

                $('._js-input-new-customer-phone').on('input', function() {
                    $(this).removeClass('text-red-600');
                });

                if (hash === '#new') {
                    $('._js-input-oldnew-new').prop('checked', true).trigger('click');
                    onClickNewCustomer();
                } else if (hash === '#marketing') {
                    $('._js-input-oldnew-marketing').prop('checked', true).trigger('click');
                    onClickJobMarketing();
                } else if (hash === '#asdt') {
                    $('._js-input-oldnew-asdt').prop('checked', true).trigger('click');
                    onClickAsdtKonsumsiToko();
                    setAddAnotherAddressOff();
                    $('._js-wrp-asdt').show();
                    $('._js-wrp-asdt-only').show();
                } else if (hash === '#konsumsitoko') {
                    $('._js-input-oldnew-konsumsitoko').prop('checked', true).trigger('click');
                    onClickAsdtKonsumsiToko();
                    setAddAnotherAddressOff();
                } else {
                    @if(in_array(auth()->user()->role_id, [4,6]))
                        $('._js-input-oldnew-asdt').prop('checked', true).trigger('click');
                        onClickAsdtKonsumsiToko();
                    @else
                        $('._js-input-oldnew-old').prop('checked', true).trigger('click');
                        requiredInputOld();
                    @endif
                    setAddAnotherAddressOff();
                }

            });
        </script>
    </x-slot>
</x-layout-addjob>