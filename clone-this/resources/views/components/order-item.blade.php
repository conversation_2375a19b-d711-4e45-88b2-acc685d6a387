@php
if ($order->received_at) {
$start_date = new DateTime($order->created_at);
$since_start = $start_date->diff(new DateTime($order->received_at));
// echo $since_start->days.' days total<br>';
// echo $since_start->y.' years<br>';
// echo $since_start->m.' months<br>';
// echo $since_start->d.' days<br>';
// echo $since_start->h.' hours<br>';
// echo $since_start->i.' minutes<br>';
// echo $since_start->s.' seconds<br>';
}
@endphp

<div
  class="px-4 py-4 border-solid border-gray-300 border-b-2 relative animate__animated _js-order-item-{{ $order->id }}">
  <a target="_blank" href="{{ auth()->user()->role_id <= 3 ? URL::to(" /")."/cs/form/order/".$order->id : '' }}"
    class="absolute inset-0 {{ auth()->user()->role_id <= 3 ? '' : 'pointer-events-none' }}"></a>
  <div class="relative pointer-events-none index-10">
    <h4 class="text-lg font-bold">{{ $order->code }}<span class="ml-1 text-xs text-gray-400">🕐
        {{ date('H:i', strtotime($order->created_at)) }}</span>
      @if ($order->received_at)
      @if ($since_start->h > 0 || $since_start->i > 60)
      <span class="ml-1 text-xs text-red-500">🔴
        {{ $since_start->h > 0 ? $since_start->h.':' : '' }}{{ $since_start->i }}</span>
      @else
      <span class="ml-1 text-xs text-green-500">🟢 {{ $since_start->i }}</span>
      @endif
      @endif
      @if ($order->distance_store_customer)
      <span class="ml-1 text-xs text-gray-500">{{ $order->distance_store_customer }}</span>
      @endif
      @if ($order->distance_customer_received)
      <span class="text-xs text-gray-800">/ {{ $order->distance_customer_received }}</span>
      @endif
    </h4>
    <p
      class="_js-status-item-{{ $order->id }} px-2 py-0.5 mb-1 text-xs font-bold rounded inline-block text-white uppercase {{ __('order.status.color.'.$order->status->code) }}">
      JOB <span class="_js-status-text-{{ $order->id }}">{{ __('order.status.label.'.$order->status->code) }}</span></p>
    @if ($order->status->code == 'canceled')

    @endif
    @if ($order->driver)
    <p
      class="_js-driver-item-{{ $order->id }} px-2 py-0.5 mb-1 ml-1 text-xs bg-blue-500 font-bold rounded inline-block text-white">
      🛵
      {{ $order->driver->name }}</p>
    @endif
    @if ($order->status_id == 5 && $order->driver_note)
    <p class="font-bold text-blue-500">⚠️ {{ $order->driver_note }} @if ($order->cancelby)
      <span class="ml-1 text-sm text-gray-300">(by {{ $order->cancelby->name }})</span>
      @endif
    </p>
    @endif
    <p class="">👤 {{ $order->customer->name }}</p>
    <a href="https://wa.me/{{ $order->customer->phone }}" target="_blank"
      class="relative text-red-600 underline pointer-events-auto">📲 {{ $order->customer->phone }}</a>
    <p class="">🏠 {{ $order->address ? $order->address->address : "" }}</p>
    @if($order->address && $order->address->latlng)
    <a href="http://www.google.com/maps/place/{{ $order->address ? $order->address->latlng : "" }}" target="_blank"
      class="relative text-red-600 underline pointer-events-auto">📍
      {{ $order->address ? $order->address->latlng : "" }}</a>
    @endif
    @foreach ($order->products as $product)
    <p class="font-bold">🟢 {{ $product->code.' '.$product->name }} ({{ $product->pivot->qty }}x)</p>
    @endforeach
    @if ($order->note)
    <p class="font-bold text-blue-500">📝 {{ $order->note }}</p>
    @endif
    <hr class="my-2">
    <p class="">💰 <span class="font-bold">TOTAL:</span> Rp{{ number_format($order->total, 0, '', '.') }}
      @if($order->payment)
      <span class="font-bold uppercase">({{ $order->payment }})</span>
      @endif
    </p>
    @if($order->amount_will_pay)
    <p class="">💵 <span class="font-bold">BAYAR:</span> Rp{{ number_format($order->amount_will_pay, 0, '', '.') }}</p>
    @endif
    <p class="text-yellow-500">💸 <span class="font-bold">KEMBALI:</span>
      Rp{{ number_format(999999999, 0, '', '.') }}</p>

    @if ($date_now == date('Y-m-d'))
    {{-- Select Driver --}}
    @if((auth()->user()->role_id <= 4 || auth()->user()->role_id === 6) && $order->status_id <= 3) <label
        class="block mt-3">
        {{-- <span class="font-bold text-gray-700">Toko *</span> --}}
        <select data-order_id="{{ $order->id }}"
          class="relative block w-full mt-1 border-gray-300 rounded-md shadow-sm pointer-events-auto _js-input-driver focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50">
          <option value="">Pilih driver..</option>
          @foreach ($drivers as $driver)
          <option value="{{ $driver->id }}" {{ $order->driver_id == $driver->id ? 'selected="selected' : '' }}>
            {{ $driver->name }}</option>
          @endforeach
        </select>
        </label>
        @endif
        {{-- Action--}}
        @if((auth()->user()->role_id <= 3 || in_array(auth()->user()->role_id, [5, 6])) && $order->status_id <= 3) <div
            class="flex justify-around mt-3 _js-wrp-action">
            <button type="button" data-order_id="{{ $order->id }}"
              class="relative flex-1 px-2 py-2 mr-2 font-bold text-white bg-green-500 rounded shadow-lg pointer-events-auto _js-btn-selesai">SELESAI</button>
            <button type="button" data-order_id="{{ $order->id }}"
              class="relative flex-1 px-2 py-2 ml-2 font-bold text-white bg-red-500 rounded shadow-lg pointer-events-auto _js-btn-batal">BATAL</button>
  </div>
  @endif
  @endif
</div>
<span class="absolute index-10 top-1 right-1.5 opacity-40 text-xs font-bold text-gray-400">{{ $order->author ?
  $order->author->name : 'PWA' }}</span>
</div>