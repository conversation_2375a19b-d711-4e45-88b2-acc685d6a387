<template>
    <div>
        <p
            v-if="
                !paymentMethodAsk ||
                (paymentMethodAsk && paymentMethodAsk == 'cash')
            "
            class="flex justify-between mt-1 text-yellow-500"
        >
            <span class="font-bold">💶 DIBAYAR:</span>
            <span class="pr-1 font-bold">Rp{{ pay.toLocaleString("id") }}</span>
        </p>
        <div
            v-if="
                !paymentMethodAsk ||
                (paymentMethodAsk && paymentMethodAsk == 'cash')
            "
            class="flex items-center mt-1"
        >
            <p class="mr-1 font-bold text-blue-500 whitespace-nowrap">
                💸 KEMBALIAN:
            </p>
            <money
                :data-order_id="orderId"
                placeholder="Set nominal kembalian"
                :class="[
                    '_js-input-return-' + orderId,
                    'px-1 py-0.5 pointer-events-auto relative block flex-1 text-right rounded-md border border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50 font-bold text-blue-500',
                    safeParseInt(roleId) === 5 ||
                    safeParseInt(orderStatusId) >= 2
                        ? 'border-none shadow-none'
                        : '',
                ]"
                v-model="customReturn"
                v-bind="money"
                :disabled="
                    safeParseInt(roleId) === 5 ||
                    safeParseInt(orderStatusId) >= 2
                "
            ></money>
        </div>
        <template
            v-if="
                paymentMethodAsk &&
                paymentMethodAsk != 'cash' &&
                safeParseInt(orderStatusId) >= 4
            "
        >
            <div class="flex items-center mt-1">
                <p class="mr-1 font-bold text-green-500 whitespace-nowrap">
                    💵 DITRANSFER:
                </p>
                <money
                    :data-order_id="orderId"
                    placeholder="Set nominal transferan"
                    :class="[
                        '_js-input-amount-pay-' + orderId,
                        'px-1 py-0.5 pointer-events-auto relative block flex-1 text-right rounded-md border border-gray-300 shadow-sm focus:border-green-300 focus:ring focus:ring-red-200 focus:ring-opacity-50 font-bold text-green-500',
                        safeParseInt(orderStatusId) > 4
                            ? 'border-none shadow-none'
                            : '',
                    ]"
                    v-model="customTransfer"
                    v-bind="money"
                    :disabled="safeParseInt(orderStatusId) > 4"
                ></money>
            </div>
            <div class="flex items-center mt-1">
                <p class="mr-1 font-bold text-green-500 whitespace-nowrap">
                    🪙 SPLIT CASH:
                </p>
                <money
                    :data-order_id="orderId"
                    placeholder="Set nominal split cash"
                    :class="[
                        '_js-input-split-to-cash-' + orderId,
                        'px-1 py-0.5 pointer-events-auto relative block flex-1 text-right rounded-md border border-gray-300 shadow-sm focus:border-green-300 focus:ring focus:ring-red-200 focus:ring-opacity-50 font-bold text-green-500',
                        safeParseInt(orderStatusId) > 4
                            ? 'border-none shadow-none'
                            : '',
                    ]"
                    v-model="customCash"
                    v-bind="money"
                    :disabled="safeParseInt(orderStatusId) > 4"
                ></money>
            </div>
            <p
                :class="[
                    { 'text-green-500': safeParseInt(newDeposit) > 0 },
                    { 'text-red-500': safeParseInt(newDeposit) < 0 },
                    'mt-1 flex justify-between',
                ]"
                v-if="safeParseInt(newDeposit) != 0"
            >
                <span class="font-bold">🌱 DEPOSIT BARU:</span
                ><span class="pr-1 font-bold"
                    >{{ safeParseInt(newDeposit) > 0 ? "+" : ""
                    }}{{ 0 > safeParseInt(newDeposit) ? "-" : "" }} Rp{{
                        Math.abs(newDeposit).toLocaleString("id")
                    }}</span
                >
            </p>
        </template>
        <p
            class="flex justify-between mt-1 text-green-500"
            v-if="
                !paymentMethodAsk ||
                (paymentMethodAsk && paymentMethodAsk == 'cash')
            "
        >
            <span class="font-bold">💵 SETOR:</span
            ><span class="pr-1 font-bold"
                >Rp{{ deposit.toLocaleString("id") }}</span
            >
        </p>
        <p
            class="flex justify-between mt-1 text-green-500"
            v-if="paymentMethodAsk && paymentMethodAsk == 'cash'"
        >
            <span class="font-bold">🪙 CASH MASUK:</span
            ><span class="pr-1 font-bold"
                >Rp{{ totalAfterDepositAmount.toLocaleString("id") }}</span
            >
        </p>
    </div>
</template>

<script>
import { Money } from "v-money";
export default {
    components: { Money },
    props: {
        roleId: Number,
        orderId: Number,
        totalAfterDepositAmount: Number,
        transferAmount: Number,
        cashAmount: Number,
        payAmount: Number,
        returnAmount: Number,
        depositAmount: Number,
        orderStatusId: Number,
        paymentMethodAsk: String,
    },
    data: function () {
        return {
            pay: this.payAmount ? this.payAmount : "",
            deposit: this.depositAmount ? this.depositAmount : "",
            customReturn: this.returnAmount ? this.returnAmount : "",
            customTransfer: this.transferAmount
                ? this.transferAmount
                : this.totalAfterDepositAmount - this.cashAmount > 0
                ? this.totalAfterDepositAmount - this.cashAmount
                : 0,
            customCash: this.cashAmount ? this.cashAmount : 0,
            customPay:
                this.transferAmount || this.cashAmount
                    ? this.transferAmount + this.cashAmount
                    : this.totalAfterDepositAmount,
            newDeposit: 0,
            // this.cashAmount
            //   ? this.cashAmount - this.totalAfterDepositAmount
            //   : 0,
            money: {
                decimal: ",",
                thousands: ".",
                prefix: "Rp",
                suffix: "",
                precision: 0,
                masked: false /* doesn't work with directive */,
            },
        };
    },
    watch: {
        totalAfterDepositAmount: function (newValue, oldValue) {
            this.customTransfer = this.transferAmount
                ? this.transferAmount
                : newValue - this.cashAmount > 0
                ? newValue - this.cashAmount
                : 0;
            this.customPay =
                this.transferAmount || this.cashAmount
                    ? this.transferAmount + this.cashAmount
                    : newValue;
            this.newDeposit =
                this.transferAmount || this.cashAmount
                    ? this.transferAmount + this.cashAmount - newValue
                    : 0;
        },
        transferAmount: function (newValue, oldValue) {
            this.customTransfer = newValue
                ? newValue
                : this.totalAfterDepositAmount - this.cashAmount > 0
                ? this.totalAfterDepositAmount - this.cashAmount
                : 0;
            this.customPay =
                newValue || this.cashAmount
                    ? newValue + this.cashAmount
                    : this.totalAfterDepositAmount;
            this.newDeposit =
                newValue || this.cashAmount
                    ? newValue + this.cashAmount - this.totalAfterDepositAmount
                    : 0;
        },
        cashAmount: function (newValue, oldValue) {
            this.customCash = newValue ? newValue : 0;
            this.customPay =
                this.transferAmount || newValue
                    ? this.transferAmount + newValue
                    : this.totalAfterDepositAmount;
            this.newDeposit =
                this.transferAmount || newValue
                    ? this.transferAmount +
                      newValue -
                      this.totalAfterDepositAmount
                    : 0;
            this.customTransfer = this.transferAmount
                ? this.transferAmount
                : this.totalAfterDepositAmount - newValue > 0
                ? this.totalAfterDepositAmount - newValue
                : 0;
        },
        payAmount: function (newValue, oldValue) {
            this.pay = newValue ? newValue : "";
        },
        depositAmount: function (newValue, oldValue) {
            this.deposit = newValue ? newValue : "";
        },
        returnAmount: function (newValue, oldValue) {
            this.customReturn = newValue ? newValue : "";
        },
        customReturn: function (newValue, oldValue) {
            this.deposit = this.totalAfterDepositAmount + newValue;
        },
        customTransfer: function (newValue, oldValue) {
            this.customPay = newValue + this.customCash;
            this.newDeposit =
                newValue + this.customCash - this.totalAfterDepositAmount;
        },
        customCash: function (newValue, oldValue) {
            this.customTransfer =
                this.totalAfterDepositAmount - newValue > 0
                    ? this.totalAfterDepositAmount - newValue
                    : 0;
            this.customPay = newValue + this.customTransfer;
            this.newDeposit =
                newValue + this.customTransfer - this.totalAfterDepositAmount;
        },
    },
};
</script>
