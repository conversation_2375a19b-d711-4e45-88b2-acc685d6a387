<template>
    <div class="d-flex align-items-center">
        <input
            :class="['SharpText hide-controls', { SharpTextHide: is_hide }]"
            type="text"
            disabled="disabled"
            :value="value"
        />
    </div>
</template>

<script>
let runGetLocation;
export default {
    props: {
        value: String,
        is_hide: Boolean
    },
    mounted: function() {
        this.$nextTick(function() {
            const thisVue = this;
            function success(pos) {
                var crd = pos.coords;
                console.log("Your current position is:");
                console.log(`Latitude : ${crd.latitude}`);
                console.log(`Longitude: ${crd.longitude}`);
                console.log(`More or less ${crd.accuracy} meters.`);
                thisVue.$emit("input", crd.accuracy);
            }
            function error(err) {
                console.warn("ERROR(" + err.code + "): " + err.message);
                thisVue.$emit(
                    "input",
                    "ERROR(" + err.code + "): " + err.message
                );
            }

            const options = {
                enableHighAccuracy: true,
                timeout: 5000,
                maximumAge: 0
            };

            function getLocation() {
                navigator.geolocation.getCurrentPosition(
                    success,
                    error,
                    options
                );
            }

            if (navigator.geolocation) {
                runGetLocation = setInterval(getLocation, 1000);
            } else {
                // x.innerHTML = "Geolocation is not supported by this browser.";
                console.log("Geolocation is not supported by this browser.");
                thisVue.$emit(
                    "input",
                    "Geolocation is not supported by this browser."
                );
            }
        });
    },
    destroyed: function() {
        clearInterval(runGetLocation);
    }
};
</script>

<style type="text/css" scoped>
.SharpTextHide {
    position: fixed;
    top: -99999999px;
    left: -99999999px;
    opacity: 0;
    pointer-events: none;
}
.SharpTextPrepend__text {
    font-size: 14px;
    padding: 0 0.5em;
    background-color: #e0e4e8;
    line-height: 40px;
    border: 1px solid transparent;
    box-shadow: 0 0 2px 0 rgba(0, 0, 0, 0.1);
}
</style>
