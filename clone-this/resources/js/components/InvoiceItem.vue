<template>
    <form :class="'relative _js-form-confirm-invoice-' + order.id">
        <!-- <a
      target="_blank"
      :href="
        safeParseInt(roleId) <= 3
          ? baseUrl + '/cs/form/marketing/' + order.id
          : ''
      "
      :class="[
        'absolute inset-0',
        safeParseInt(roleId) <= 3 ? '' : 'pointer-events-none',
      ]"
    ></a> -->
        <a
            target="_blank"
            href="#"
            :class="[
                'absolute inset-0',
                'pointer-events-none',
                // safeParseInt(roleId) <= 2 ? '' : 'pointer-events-none',
            ]"
        ></a>
        <div class="relative pointer-events-none index-10">
            <h4 class="flex items-center text-lg font-bold">
                {{ order.code
                }}<span class="text-xs ml-2.5 text-gray-400"
                    >🕐 {{ moment(order.created_at).format("HH:mm") }}</span
                >
                <span
                    :class="[
                        'text-white px-2 py-0.5 ml-2 text-xs font-bold rounded inline-block bg-transparent uppercase',
                        !order.payment_method_confirmed
                            ? 'bg-blue-500'
                            : 'bg-green-500',
                    ]"
                >
                    {{ !order.payment_method_confirmed ? "UNPAID" : "PAID" }}
                </span>
            </h4>
            <div class="my-1">
                <a
                    :href="
                        safeParseInt(roleId) <= 3
                            ? baseUrl + '/cs/show/customer/' + order.customer.id
                            : ''
                    "
                    target="_blank"
                    :class="[
                        safeParseInt(roleId) <= 3
                            ? 'pointer-events-auto rounded bg-yellow-500 font-bold text-white px-2 py-0.5'
                            : '',
                    ]"
                    >👤 {{ order.customer.name }}</a
                >
            </div>
            <p v-if="order.customer.note_special" class="font-bold">
                📝 {{ order.customer.note_special }}
            </p>
            <div>
                <a
                    :href="'https://wa.me/' + order.customer.phone"
                    target="_blank"
                    class="relative mr-1 text-yellow-600 underline pointer-events-auto"
                    >📲 {{ order.customer.phone }}</a
                >
                <template v-if="order.receiver_phone">
                    /
                    <a
                        :href="'https://wa.me/' + toWa(order.receiver_phone)"
                        target="_blank"
                        class="relative ml-1 text-green-600 underline pointer-events-auto"
                        >📲 {{ order.receiver_phone }}</a
                    >
                </template>
            </div>
            <hr class="my-2" />
            <div
                v-for="ord in order.orders"
                :key="ord.id"
                class="flex flex-col gap-1"
            >
                <h4 class="flex items-center font-bold">
                    {{ ord.code
                    }}<span class="ml-auto text-xs font-normal text-gray-400">{{
                        moment(ord.created_at).format("D MMM YYYY")
                    }}</span>
                </h4>
                <div
                    v-for="product in ord.products"
                    :key="product.id"
                    class="flex items-center font-semibold"
                >
                    <div class="text-sm">
                        🟢 {{ product.code }} - {{ product.name }} @Rp{{
                            safeParseInt(product.pivot.price).toLocaleString(
                                "id"
                            )
                        }}x{{ product.pivot.qty }}
                    </div>
                    <div class="ml-auto text-right text-black">
                        Rp{{
                            safeParseInt(
                                product.pivot.price * product.pivot.qty
                            ).toLocaleString("id")
                        }}
                    </div>
                </div>
                <hr class="mt-2 mb-3" />
            </div>
            <p class="flex justify-between mt-1 text-blue-500">
                <span class="font-bold">POTONGAN:</span
                ><span class="font-bold"
                    >Rp{{
                        safeParseInt(order.discount).toLocaleString("id")
                    }}</span
                >
            </p>
            <p class="flex justify-between mt-1 text-red-500">
                <span class="font-bold">TOTAL TAGIHAN:</span
                ><span class="font-bold"
                    >Rp{{
                        safeParseInt(order.total_after_discount).toLocaleString(
                            "id"
                        )
                    }}</span
                >
            </p>
            <template v-if="order.note">
                <p class="mt-1 font-bold text-red-500">
                    📝 {{ order.note }}
                    <span class="text-sm opacity-60">(by Pelanggan)</span>
                </p>
            </template>

            <template v-if="!order.payment_proof_url">
                <div
                    class="flex items-center mt-2 font-bold text-gray-500 uppercase"
                >
                    <p class="whitespace-nowrap">⬆️</p>
                    <button
                        data-order_type="invoice"
                        :data-order_id="order.id"
                        type="button"
                        class="relative flex items-center justify-center w-full h-8 ml-2 overflow-hidden font-bold text-white bg-green-700 border border-gray-500 rounded shadow pointer-events-auto _js-btn-upload-paymentproof text-shadow-xl"
                    >
                        UPLOAD BUKTI TRANSFER
                    </button>
                </div>
            </template>
            <template v-if="order.payment_proof_url">
                <div
                    class="flex items-center mt-1 font-bold text-gray-500 uppercase"
                >
                    <p class="whitespace-nowrap">🧾</p>
                    <div class="flex w-full h-8">
                        <div
                            class="w-full ml-1.5 rounded overflow-hidden flex justify-center items-center relative shadow border-gray-500 border h-8"
                        >
                            <img
                                :src="order.payment_proof_url"
                                data-zoomable
                                :id="'invoice_img_zoom_' + order.id"
                                class="object-cover w-full h-auto pointer-events-auto"
                            />
                            <span
                                class="absolute flex items-center justify-center w-full h-full text-white text-shadow-xl"
                                >LIHAT BUKTI TRANSFER</span
                            >
                        </div>
                        <button
                            data-order_type="invoice"
                            :data-order_id="order.id"
                            type="button"
                            class="_js-btn-upload-paymentproof pointer-events-auto w-10 ml-1.5 rounded overflow-hidden flex justify-center items-center relative shadow border-gray-500 border bg-green-700 h-8"
                        >
                            <span
                                class="absolute flex items-center justify-center w-full h-full font-bold text-white text-shadow-xl"
                                >⬆️</span
                            >
                        </button>
                    </div>
                </div>
            </template>

            <template v-if="!order.payment_method_confirmed">
                <div class="flex items-center mt-3">
                    <p class="mr-1 font-bold text-green-500 whitespace-nowrap">
                        💵 DITRANSFER:
                    </p>
                    <money
                        :data-order_id="order.id"
                        placeholder="Set nominal transferan"
                        :class="[
                            '_js-input-amount-pay-' + order.id,
                            'px-1 -mr-1 py-0.5 pointer-events-auto relative block flex-1 text-right rounded-md border border-gray-300 shadow-sm focus:border-green-300 focus:ring focus:ring-red-200 focus:ring-opacity-50 font-bold text-green-500',
                        ]"
                        v-model="input.amount_pay"
                        v-bind="money"
                        required
                    ></money>
                </div>
                <!-- <div class="flex items-center mt-1">
          <p class="mr-1 font-bold text-green-500 whitespace-nowrap">
            🪙 SPLIT CASH:
          </p>
          <money
            :data-order_id="order.id"
            placeholder="Set nominal split cash"
            :class="[
              '_js-input-split-to-cash-' + order.id,
              'px-1 py-0.5 pointer-events-auto relative block flex-1 text-right rounded-md border border-gray-300 shadow-sm focus:border-green-300 focus:ring focus:ring-red-200 focus:ring-opacity-50 font-bold text-green-500',
              safeParseInt(order.id) > 4 ? 'border-none shadow-none' : '',
            ]"
            v-model="customCash"
            v-bind="money"
            :disabled="safeParseInt(order.id) > 4"
          ></money>
        </div> -->
                <p
                    :class="[
                        {
                            'text-green-500':
                                safeParseInt(input.new_deposit) > 0,
                        },
                        { 'text-red-500': safeParseInt(input.new_deposit) < 0 },
                        'mt-2 flex justify-between',
                    ]"
                    v-if="safeParseInt(input.new_deposit) != 0"
                >
                    <input
                        type="hidden"
                        v-model="input.new_deposit"
                        name="new_deposit"
                        id="new_deposit"
                    />
                    <span class="font-bold">🌱 DEPOSIT BARU:</span
                    ><span class="font-bold"
                        >{{ safeParseInt(input.new_deposit) > 0 ? "+" : ""
                        }}{{
                            0 > safeParseInt(input.new_deposit) ? "-" : ""
                        }}
                        Rp{{
                            Math.abs(input.new_deposit).toLocaleString("id")
                        }}</span
                    >
                </p>
            </template>

            <!-- <hr class="my-2" /> -->
            <template
                v-if="
                    order.bank &&
                    (order.payment_method_confirmed === 'transfer' ||
                        order.payment_method_confirmed === 'qris')
                "
            >
                <p class="mt-1 font-bold text-blue-700 uppercase">
                    {{
                        order.payment_method_confirmed === "qris" ? "🔠" : "🏧"
                    }}
                    {{ order.bank.bank_name }} -
                    {{ order.bank.account_number }} -
                    {{ order.bank.holder_name }}
                </p>
                <p
                    class="text-sm font-bold text-blue-600"
                    v-if="order.note_confirm"
                >
                    {{ order.note_confirm }}
                </p>
            </template>
            <p v-if="order.confirmedby" class="text-xs font-bold text-gray-400">
                Confirmed by: {{ order.confirmedby.name }}
            </p>
            <template v-else-if="order.payment_method_confirmed === 'cash'">
                <p class="mt-1 font-bold text-blue-700 uppercase">✅ (CASH)</p>
            </template>

            <select
                id="bank_id"
                name="bank_id"
                v-if="!order.payment_method_confirmed"
                :data-order_id="order.id"
                :class="[
                    '_js-input-bank-confirmed-' + order.id,
                    '_js-input-bank-confirmed pointer-events-auto relative block w-full mt-3 rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50',
                ]"
                required
                v-model="input.bank_id"
            >
                <option value="" disabled>Pilih rekening..</option>
                <option
                    v-for="(bank, index) in banks"
                    :key="bank.id"
                    :value="bank.id"
                >
                    {{ bank.bank_name }} / {{ bank.account_number }} /
                    {{ bank.holder_name }}
                </option>
            </select>
            <textarea
                v-if="!order.payment_method_confirmed"
                v-model="input.note_confirm"
                id="confirm_note"
                name="confirm_note"
                placeholder="Catatan konfirmasi..."
                :class="[
                    '_js-input-confirm-note-' + order.id,
                    'mt-3 block w-full pointer-events-auto rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50',
                ]"
                rows="2"
            ></textarea>
            <div class="flex gap-2 mt-3" v-if="!order.payment_method_confirmed">
                <button
                    @click="onClickCancel"
                    type="button"
                    :data-order_id="order.id"
                    :class="[
                        'flex-1 pointer-events-auto relative text-white bg-red-500 py-2 shadow-lg px-2 rounded font-bold w-full',
                        isLoadingSubmit && 'opacity-50 pointer-events-none',
                    ]"
                >
                    {{ isLoadingSubmit ? "MEBATALKAN" : "BATAL" }}
                </button>
                <button
                    @click="onClickSubmit"
                    type="button"
                    :data-order_id="order.id"
                    :class="[
                        'flex-1 pointer-events-auto relative text-white bg-green-500 py-2 shadow-lg px-2 rounded font-bold w-full',
                        isLoadingSubmit && 'opacity-50 pointer-events-none',
                    ]"
                >
                    {{ isLoadingSubmit ? "CONFIRMING" : "CONFIRM" }}
                </button>
            </div>
            <a
                target="_blank"
                :href="'/manager/invoice-download/' + order.id"
                :data-order_id="order.id"
                :class="[
                    'mt-3 flex justify-center items-center pointer-events-auto relative text-white bg-blue-500 py-2 shadow-lg px-2 rounded font-bold w-full',
                    isLoadingSubmit && 'opacity-50 pointer-events-none',
                ]"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="h-6 w-6 -mt-0.5 mr-1"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke="currentColor"
                    stroke-width="2"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                    />
                </svg>
                PDF
            </a>
        </div>
        <span
            class="absolute index-10 top-1 right-1.5 opacity-40 text-xs font-bold text-gray-400"
            >{{ order.author ? order.author.name : "System" }}</span
        >
    </form>
</template>

<script>
// import MarketingFinish from "./MarketingFinish";

// function parseJson(str) {
//   try {
//     const value = JSON.parse(str);
//     return value;
//   } catch (e) {
//     return str;
//   }
// }
import { Money } from "v-money";
export default {
    components: { Money },
    // components: {
    //   MarketingFinish,
    // },
    props: {
        roleId: String | Number,
        baseUrl: String,
        dateNow: String,
        order: Object,
        banks: Array,
    },
    data: function () {
        return {
            isLoadingSubmit: false,
            input: {
                invoice_id: this.order.id,
                bank_id: "",
                amount_pay: this.order.total_after_discount,
                note_confirm: "",
                new_deposit: 0,
            },
            images: null,
            money: {
                decimal: ",",
                thousands: ".",
                prefix: "Rp",
                suffix: "",
                precision: 0,
                masked: false /* doesn't work with directive */,
            },
        };
    },
    mounted() {
        // console.log("Example component mounted");
        this.images = mediumZoom("#invoice_img_zoom_" + this.order.id);
    },
    updated() {
        this.images.detach();
        this.images = mediumZoom("#invoice_img_zoom_" + this.order.id);
    },
    // computed: {
    //   roleId: function () {
    //     return this.roleId ? this.roleId : this.offlineData.roleId;
    //   },
    //
    // },
    methods: {
        moment: moment,
        toWa(number) {
            if (!number) return number;
            let num = number;
            num = num.replace(/[^0-9]/g, "");
            // num.replace(' ', '');
            // num.replace('-', '');
            // num.replace('+', '');
            // num.replace('(', '');
            // num.replace(')', '');
            if (num.substr(0, 1) == "0") {
                num = "62" + num.substr(1);
            } else if (num.substr(0, 1) == "8") {
                num = "62" + num;
            }
            return num;
        },
        onClickCancel: function () {
            if (confirm("Batalkan invoice?")) {
                const orderId = this.order.id;
                const ini = this;
                this.isLoadingSubmit = true;
                console.log("orderId", orderId);
                axios
                    .get("/manager/invoice-cancel/" + orderId)
                    .then((res) => {
                        console.log("res", res);
                        if (res && res.data && res.data.success) {
                            ini.isLoadingSubmit = false;
                            mdtoast("INVOICE BERHASIL DIBATALKAN", {
                                duration: 1500,
                                type: mdtoast.SUCCESS,
                            });
                            $("._js-invoice-item-" + orderId).addClass(
                                "animate__bounceOut"
                            );
                            setTimeout(() => {
                                $("._js-invoice-item-" + orderId).remove();
                            }, 1000);
                        } else {
                            ini.isLoadingSubmit = false;
                            mdtoast("TERJADI KESALAHAN", {
                                duration: 1500,
                                type: mdtoast.ERROR,
                            });
                        }
                    })
                    .catch((error) => {
                        console.log("error", error);
                        ini.isLoadingSubmit = false;
                        mdtoast("TERJADI KESALAHAN", {
                            duration: 1500,
                            type: mdtoast.ERROR,
                        });
                    });
            }
        },
        onClickSubmit: function () {
            if (
                $(
                    "._js-form-confirm-invoice-" + this.order.id
                )[0].reportValidity()
            ) {
                const orderId = this.order.id;
                const ini = this;
                this.isLoadingSubmit = true;
                axios
                    .post("/manager/invoice-confirm", this.input)
                    .then((res) => {
                        console.log("res", res);
                        if (res && res.data && res.data.success) {
                            ini.isLoadingSubmit = false;
                            mdtoast("INVOICE BERHASIL TERKONFIRMASI", {
                                duration: 1500,
                                type: mdtoast.SUCCESS,
                            });
                            $("._js-invoice-item-" + orderId).addClass(
                                "animate__bounceOut"
                            );
                            setTimeout(() => {
                                $("._js-invoice-item-" + orderId).remove();
                            }, 1000);
                        } else {
                            ini.isLoadingSubmit = false;
                            mdtoast("TERJADI KESALAHAN", {
                                duration: 1500,
                                type: mdtoast.ERROR,
                            });
                        }
                    })
                    .catch((error) => {
                        console.log("error", error);
                        ini.isLoadingSubmit = false;
                        mdtoast("TERJADI KESALAHAN", {
                            duration: 1500,
                            type: mdtoast.ERROR,
                        });
                    });
            }
        },
    },
    watch: {
        "input.amount_pay": function (newValue, oldValue) {
            this.input.new_deposit = newValue - this.order.total_after_discount;
        },
    },
};
</script>
