<template>
    <div class="flex flex-col gap-5">
        <label class="relative block" for="store_id">
            <div id="input_store_id" class="absolute -top-16"></div>
            <div class="font-bold text-gray-700">
                Toko <span class="text-red-600">*</span>
            </div>
            <select
                id="store_id"
                name="store_id"
                required
                class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                v-model="input.store_id"
            >
                <option value="">Pilih toko..</option>
                <option
                    v-for="store in stores"
                    :key="parseInt(store.id)"
                    :value="parseInt(store.id)"
                >
                    {{ store.name }}
                </option>
            </select>
        </label>
        <label class="relative block" for="customer_id">
            <div id="input_customer_id" class="absolute -top-16"></div>
            <div class="font-bold text-gray-700">
                Pelanggan <span class="text-red-600">*</span>
            </div>
            <select
                id="customer_id"
                name="customer_id"
                required
                class="block w-full mt-1 border-gray-300 rounded-md _js-input-pelanggan focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
            ></select>
        </label>
        <label class="block" for="receiver_phone">
            <div class="font-bold text-gray-700">
                No. WhatsApp Penerima Invoice
            </div>
            <input
                type="text"
                id="receiver_phone"
                name="receiver_phone"
                class="block w-full mt-1 border-gray-300 rounded-md shadow-sm _js-input-receiver-phone focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                value=""
                placeholder=""
            />
            <p class="mt-2 text-sm text-gray-400">
                Selain No. WhatsApp asli pelanggan
            </p>
        </label>
        <div class="flex flex-col gap-3 text-gray-800">
            <h3 class="text-lg font-bold text-gray-700">Order & Item AR</h3>
            <hr />
            <p
                v-if="isLoadingGetOrders"
                class="text-lg font-semibold text-gray-400"
            >
                Loading...
            </p>
            <p
                v-if="!isLoadingGetOrders && orders.length == 0"
                class="text-lg font-semibold text-gray-400"
            >
                Pilih pelanggan dahulu!
            </p>
            <template v-if="orders.length > 0">
                <div
                    v-for="order in orders"
                    :key="order.id"
                    class="flex flex-col gap-1"
                >
                    <h4 class="flex items-center font-bold">
                        <label class="flex items-center">
                            <input
                                class="mr-1"
                                type="checkbox"
                                checked
                                name="order_ids[]"
                                :value="order.id"
                            />
                            {{ order.code }}
                        </label>
                        <span
                            class="ml-auto text-xs font-normal text-gray-400"
                            >{{
                                moment(order.created_at).format("D MMM YYYY")
                            }}</span
                        >
                        <span class="ml-3 text-right text-black">
                            Rp{{
                                safeParseInt(order.total).toLocaleString("id")
                            }}
                        </span>
                    </h4>
                    <div
                        v-for="product in order.products"
                        :key="product.id"
                        class="flex items-center font-semibold"
                    >
                        <div class="flex flex-1 text-sm">
                            🟢 {{ product.code }} - {{ product.name }}
                            <span class="pl-2 ml-auto"
                                >@Rp{{
                                    product.pivot.price.toLocaleString("id")
                                }}x{{ product.pivot.qty }}</span
                            >
                        </div>
                        <!-- <div class="ml-auto text-right text-black">
              Rp{{
                safeParseInt(
                  product.pivot.price * product.pivot.qty
                ).toLocaleString("id")
              }}
            </div> -->
                    </div>
                    <hr class="mt-2 mb-3" />
                </div>
                <div class="items-center hidden">
                    <p class="mr-1 font-bold text-blue-500 whitespace-nowrap">
                        POTONGAN:
                    </p>
                    <money
                        id="discount"
                        name="discount"
                        placeholder="0"
                        class="px-1 -mr-1 py-0.5 pointer-events-auto relative block flex-1 text-right rounded-md border border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50 font-semibold text-blue-500"
                        v-model="input.discount"
                        v-bind="money"
                    ></money>
                </div>
                <p class="flex justify-between text-red-500">
                    <span class="font-bold">TOTAL TAGIHAN:</span
                    ><span class="font-bold"
                        >Rp{{
                            input.total_after_discount.toLocaleString("id")
                        }}</span
                    >
                </p>
                <input
                    type="hidden"
                    v-model="input.total_bill"
                    name="total_bill"
                    id="total_bill"
                />
                <input
                    type="hidden"
                    v-model="input.total_after_discount"
                    name="total_after_discount"
                    id="total_after_discount"
                />
                <!-- <input
          type="hidden"
          v-for="order in orders"
          :key="order.created_at"
          :value="order.id"
          name="order_ids[]"
          id="order_ids"
        /> -->
            </template>
        </div>
        <label for="note" class="block">
            <div class="font-bold text-gray-700">
                Catatan
                <span class="text-sm text-red-500"> (KELUAR DI NOTIF)</span>
            </div>
            <textarea
                id="note"
                name="note"
                class="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                rows="2"
            ></textarea>
        </label>
        <button
            @click="onClickSubmit"
            type="button"
            :class="[
                'px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none transition ease-in-out duration-150 w-full bg-red-600 relative mt-3 mb-5',
                (isLoadingSubmit || orders.length == 0) &&
                    'opacity-50 pointer-events-none',
            ]"
        >
            <svg
                v-if="isLoadingSubmit"
                class="absolute top-0 h-full ml-auto text-white animate-spin w-7 left-5"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
            >
                <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                ></circle>
                <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
            </svg>
            <svg
                v-if="!isLoadingSubmit"
                class="w-7 h-full ml-auto absolute -top-0.5 left-5 _js-btn-icon"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                />
            </svg>
            {{ isLoadingSubmit ? "SAVING INVOICE" : "SAVE INVOICE" }}
        </button>
    </div>
</template>

<script>
import { Money } from "v-money";
import axios from "axios";
export default {
    components: { Money },
    props: {
        stores: Array,
        current_store_id: Number,
        customers: Array,
    },
    data: function () {
        return {
            selectizeCustomerId: null,
            input: {
                store_id: parseInt(this.current_store_id),
                customer_id: null,
                discount: 0,
                total_bill: 0,
                total_after_discount: 0,
            },
            isLoadingGetOrders: false,
            isLoadingSubmit: false,
            orders: [],
            money: {
                decimal: ",",
                thousands: ".",
                prefix: "Rp",
                suffix: "",
                precision: 0,
                masked: false /* doesn't work with directive */,
            },
        };
    },
    // computed: {
    //   totalBill: function () {
    //     return
    //   },
    // },
    mounted() {
        const ini = this;
        const customersFiltered = this.customers.map((customer) => {
            console.log("customer", customer);
            if (parseInt(customer.merger_id) && !parseInt(customer.is_main)) {
                return customer.merger.maincustomer;
            } else {
                return customer;
            }
        });
        console.log("customersFiltered", customersFiltered);
        const customerUnique = [
            ...new Map(customersFiltered.map((m) => [m.id, m])).values(),
        ];
        console.log("customerUnique", customerUnique);
        this.selectizeCustomerId = $("._js-input-pelanggan").selectize({
            // plugins: ["remove_button"],
            closeAfterSelect: true,
            options: customerUnique,
            placeholder: "Cari nama / no.whatsapp ...",
            valueField: "id",
            searchField: ["name", "phone"],
            create: false,
            render: {
                item: function (item, escape) {
                    return (
                        '<div class="">' +
                        '<div class="mt-1 mx-0.5 mb-1 font-bold">👤 ' +
                        escape(item.name) +
                        "</div>" +
                        '<div class="mx-0.5 mb-1 text-xs text-gray-600">📱 ' +
                        escape(item.phone) +
                        "</div>" +
                        '<div class="mb-1 mx-0.5 text-sm text-gray-600">🏠 ' +
                        // '<span class="font-black text-gray-800">' +
                        // escape(item.addresses[0].label) +
                        // ":</span> " +
                        escape(item.addresses[0].address) +
                        "</div>" +
                        (item.note_special
                            ? '<div class="mb-1 mx-0.5 text-sm font-bold text-gray-600">📝 ' +
                              escape(item.note_special) +
                              "</div>"
                            : "") +
                        // (item.deposit != 0
                        //   ? '<div class="mb-1 mx-0.5 text-lg font-bold" style="color: ' +
                        //     (item.deposit > 0 ? "limegreen" : "red") +
                        //     '">🌿 ' +
                        //     (item.deposit < 0 ? "-" : "") +
                        //     "Rp" +
                        //     Math.abs(item.deposit).toLocaleString("id") +
                        //     (item.deposit < 0 ? " (KURANG BAYAR)" : " (SISA DEPOSIT)") +
                        //     "</div>"
                        //   : "") +
                        "</div>"
                    );
                },
                option: function (item, escape) {
                    return (
                        '<div class="border-b border-gray-300">' +
                        '<div class="mx-3 mt-2 mb-1 font-bold">👤 ' +
                        escape(item.name) +
                        "</div>" +
                        '<div class="mx-3 mb-1 text-xs text-gray-600">📱 ' +
                        escape(item.phone) +
                        "</div>" +
                        '<div class="mx-3 mb-1 text-sm text-gray-600">🏠 ' +
                        // '<span class="font-black text-gray-800">' +
                        // escape(item.addresses[0].label) +
                        // ":</span> " +
                        escape(item.addresses[0].address) +
                        "</div>" +
                        (item.note_special
                            ? '<div class="mx-3 mb-2 text-sm font-bold text-gray-600">📝 ' +
                              escape(item.note_special) +
                              "</div>"
                            : "") +
                        // (item.deposit != 0
                        //   ? '<div class="mx-3 mb-2 text-lg font-bold" style="color: ' +
                        //     (item.deposit > 0 ? "limegreen" : "red") +
                        //     '">🌿 ' +
                        //     (item.deposit < 0 ? "-" : "") +
                        //     "Rp" +
                        //     Math.abs(item.deposit).toLocaleString("id") +
                        //     (item.deposit < 0 ? " (KURANG BAYAR)" : " (SISA DEPOSIT)") +
                        //     "</div>"
                        //   : "") +
                        "</div>"
                    );
                },
            },
            onFocus: function () {
                $("#input_customer_id")[0].scrollIntoView();
            },
            onChange: function (val) {
                ini.input.customer_id = parseInt(val);
                ini.orders = [];
                ini.input.discount = 0;
                if (val) {
                    setTimeout(() => {
                        ini.selectizeCustomerId.blur();
                        console.log("blur");
                    }, 100);
                    ini.isLoadingGetOrders = true;
                    axios
                        .get("/manager/invoice-get-orders/" + val)
                        .then((response) => {
                            // handle success
                            console.log("response", response);
                            if (
                                response &&
                                response.data &&
                                response.data.orders &&
                                response.data.orders.length > 0
                            ) {
                                console.log("masuk ada data");
                                ini.orders = response.data.orders;
                                ini.isLoadingGetOrders = false;
                            } else {
                                console.log("masuk data kosong");
                                ini.orders = response.data.orders;
                                ini.isLoadingGetOrders = false;
                                mdtoast("DATA KOSONG", {
                                    duration: 1500,
                                    type: mdtoast.ERROR,
                                });
                            }
                        })
                        .catch((error) => {
                            // handle error
                            console.log(error);
                            ini.isLoadingGetOrders = false;
                            mdtoast("TERJADI KESALAHAN", {
                                duration: 1500,
                                type: mdtoast.ERROR,
                            });
                        });
                }
            },
        })[0].selectize;
    },
    methods: {
        moment: moment,
        onClickSubmit: function () {
            if ($("._js-form")[0].reportValidity()) {
                this.isLoadingSubmit = true;
                $("._js-form").submit();
                // axios
                //   .post("/manager/invoice-post/" + this.input.store_id, this.input)
                //   .then(function (res) {
                //     // console.log('res', res);
                //     if (res && res.data && res.data.success) {
                //       // mdtoast("NO. WA SUDAH TERDAFTAR", {
                //       //   duration: 3000,
                //       //   type: mdtoast.ERROR,
                //       // });
                //       // $("._js-input-new-customer-phone")
                //       //   .addClass("text-red-600")
                //       //   .focus();
                // 			this.isLoadingSubmit = false;
                //     } else {
                // 			this.isLoadingSubmit = false;
                //       mdtoast("TERJADI KESALAHAN", {
                //         duration: 1500,
                //         type: mdtoast.ERROR,
                //       });
                //     }
                //   })
                //   .catch(function (error) {
                //     console.log("error", error);
                // 		this.isLoadingSubmit = false;
                //     mdtoast("TERJADI KESALAHAN", {
                //       duration: 1500,
                //       type: mdtoast.ERROR,
                //     });
                //   });
            }
        },
    },
    watch: {
        "input.store_id": function (newValue, oldValue) {
            console.log("newValue", newValue);
            this.input.customer_id = null;
            this.input.discount = 0;
            this.orders = [];
            const ini = this;
            ini.selectizeCustomerId.clear(true);
            ini.selectizeCustomerId.clearOptions(true);
            ini.selectizeCustomerId.disable();
            ini.selectizeCustomerId.settings.placeholder = "Loading...";
            ini.selectizeCustomerId.updatePlaceholder();
            axios
                .get("/manager/invoice-get-customers/" + newValue)
                .then((response) => {
                    // handle success
                    console.log("response", response);
                    if (
                        response &&
                        response.data &&
                        response.data.customers &&
                        response.data.customers.length > 0
                    ) {
                        const dataFiltered = response.data.customers.map(
                            (customer) => {
                                if (
                                    parseInt(customer.merger_id) &&
                                    !parseInt(customer.is_main)
                                ) {
                                    return customer.merger.maincustomer;
                                } else {
                                    return customer;
                                }
                            }
                        );
                        const dataUnique = [
                            ...new Map(
                                dataFiltered.map((m) => [m.id, m])
                            ).values(),
                        ];
                        console.log("dataUnique", dataUnique);
                        console.log("dataFiltered", dataUnique);
                        ini.selectizeCustomerId.settings.placeholder =
                            "Cari nama / no.whatsapp ...";
                        ini.selectizeCustomerId.updatePlaceholder();
                        ini.selectizeCustomerId.addOption(dataFiltered);
                        ini.selectizeCustomerId.refreshOptions(false);
                        ini.selectizeCustomerId.enable();
                    } else {
                        console.log("masuk data kosong");
                        ini.selectizeCustomerId.settings.placeholder =
                            "Cari nama / no.whatsapp ...";
                        ini.selectizeCustomerId.updatePlaceholder();
                        // ini.selectizeCustomerId.enable();
                        mdtoast("TIDAK ADA PELANGGAN BER-AR", {
                            duration: 1500,
                            type: mdtoast.INFO,
                        });
                    }
                })
                .catch((error) => {
                    // handle error
                    console.log(error);
                    ini.selectizeCustomerId.settings.placeholder =
                        "Cari nama / no.whatsapp ...";
                    ini.selectizeCustomerId.updatePlaceholder();
                    // ini.selectizeCustomerId.enable();
                    mdtoast("TERJADI KESALAHAN", {
                        duration: 1500,
                        type: mdtoast.ERROR,
                    });
                });
        },
        "input.discount": function (newValue, oldValue) {
            this.input.total_after_discount = this.input.total_bill - newValue;
        },
        orders: function (newValue, oldValue) {
            if (newValue && newValue.length > 0) {
                const ttlBill = newValue.reduce(
                    (acc, item) =>
                        parseInt(acc) + parseInt(item.total_after_deposit),
                    0
                );
                this.input.total_bill = ttlBill;
                this.input.total_after_discount = ttlBill - this.input.discount;
            } else {
                this.input.total_bill = 0;
                this.input.total_after_discount = 0;
            }
        },
    },
};
</script>
