<template>
    <div>
        <select
            :data-order_id="orderId"
            :class="[
                '_js-input-payment-method-ask-' + orderId,
                'pointer-events-auto relative block w-full mt-3 rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50',
            ]"
            v-model="payment"
        >
            <option value="">Pilih pembayaran..</option>
            <option value="cash">Cash</option>
            <option value="non-cash">Non-Tunai (Transfer/QRIS/Invoice)</option>
        </select>
        <template v-if="payment === 'non-cash'">
            <textarea
                id="payment_note"
                name="payment_note"
                placeholder="Catatan pembayaran..."
                :class="[
                    '_js-input-payment-note-' + orderId,
                    'mt-3 block w-full pointer-events-auto rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50',
                ]"
                rows="2"
            ></textarea>
            <div class="flex items-center mt-3">
                <p class="mr-1 font-bold text-green-500 whitespace-nowrap">
                    🪙 SPLIT CASH:
                </p>
                <money
                    :data-order_id="orderId"
                    placeholder="Set nominal split cash"
                    :class="[
                        '_js-input-split-to-cash-' + orderId,
                        'px-1 py-0.5 pointer-events-auto relative block flex-1 text-right rounded-md border border-gray-300 shadow-sm focus:border-green-300 focus:ring focus:ring-red-200 focus:ring-opacity-50 font-bold text-green-500',
                    ]"
                    v-model="customCash"
                    v-bind="money"
                ></money>
            </div>
        </template>
        <div class="flex justify-around gap-2 mt-3 _js-wrp-action">
            <button
                type="button"
                :data-order_id="orderId"
                :class="[
                    'pointer-events-auto relative text-white bg-blue-700 py-2 shadow-lg px-2 rounded font-bold flex-1',
                    !payment ? `opacity-50` : '',
                    roleId > 1 && driverId === 0
                        ? `opacity-50 _js-tooltip-nodriver-${orderId}`
                        : '',
                    roleId > 1 && driverId > 0 && payment === ''
                        ? `opacity-50 _js-tooltip-nopayment-${orderId}`
                        : '',
                    roleId > 1 && payment && userId !== driverId
                        ? `opacity-50 _js-tooltip-nopermission-${orderId}`
                        : '',
                    payment &&
                    ((roleId > 1 && userId === driverId) || roleId === 1)
                        ? '_js-btn-selesai'
                        : '',
                ]"
            >
                SELESAI
            </button>
            <button
                type="button"
                :data-popup-batal="'popup-batal-' + orderId"
                :class="[
                    '_js-btn-popup-batal pointer-events-auto relative text-white bg-red-500 py-2 shadow-lg px-2 rounded font-bold flex-1',
                ]"
            >
                BATAL
            </button>
        </div>

        <div :id="'popup-batal-' + orderId" class="hidden">
            <button
                type="button"
                :data-created_at_hours="createdAtHours"
                :data-created_at_minutes="createdAtMinutes"
                :data-order_id="orderId"
                :class="[
                    '_js-btn-ganti-tanggal pointer-events-auto relative text-white bg-indigo-500 py-2 my-1.5 mx-0.5 shadow-lg px-2 rounded font-bold',
                ]"
            >
                GANTI TANGGAL
            </button>
            <button
                type="button"
                :data-order_id="orderId"
                :class="[
                    '_js-btn-batal pointer-events-auto relative text-white bg-red-500 py-2 my-1.5 mx-0.5 shadow-lg px-2 rounded font-bold',
                ]"
            >
                BATAL JOB
            </button>
            <button
                type="button"
                :class="[
                    '_js-btn-close-popup-batal pointer-events-auto relative text-white bg-gray-100 py-2 my-1.5 mx-0.5 shadow-lg px-2 rounded font-bold',
                ]"
            >
                ❌
            </button>
        </div>
    </div>
</template>

<script>
import { Money } from "v-money";
export default {
    components: { Money },
    props: {
        driverId: Number,
        roleId: Number,
        userId: Number,
        orderId: Number,
        createdAtHours: String,
        createdAtMinutes: String,
    },
    data: function () {
        return {
            tooltipNoDriver: null,
            tooltipNoPayment: null,
            tooltipNoPermission: null,
            payment: "",
            customCash: 0,
            money: {
                decimal: ",",
                thousands: ".",
                prefix: "Rp",
                suffix: "",
                precision: 0,
                masked: false /* doesn't work with directive */,
            },
        };
    },
    mounted() {
        console.log("payment", this.payment);
        console.log("driverId", this.driverId);
        this.tooltipNoPermission = tippy(
            `._js-tooltip-nopermission-${this.orderId}`,
            {
                content: "Maaf anda tidak memiliki akses 🙏",
            }
        );
        this.tooltipNoPayment = tippy(
            `._js-tooltip-nopayment-${this.orderId}`,
            {
                content: "Pilih pembayaran terlebih dahulu",
            }
        );
        this.tooltipNoDriver = tippy(`._js-tooltip-nodriver-${this.orderId}`, {
            content: "Pilih driver terlebih dahulu",
        });
    },
    updated() {
        console.log("payment", this.payment);
        console.log("driverId", this.driverId);
        if (this.tooltipNoDriver) {
            this.tooltipNoDriver.forEach((el) => {
                el.destroy();
                el.disable();
            });
        }
        if (this.tooltipNoPayment) {
            this.tooltipNoPayment.forEach((el) => {
                el.destroy();
                el.disable();
            });
        }
        if (this.tooltipNoPermission) {
            this.tooltipNoPermission.forEach((el) => {
                el.destroy();
                el.disable();
            });
        }
        this.tooltipNoPermission = tippy(
            `._js-tooltip-nopermission-${this.orderId}`,
            {
                content: "Maaf anda tidak memiliki akses 🙏",
            }
        );
        this.tooltipNoPayment = tippy(
            `._js-tooltip-nopayment-${this.orderId}`,
            {
                content: "Pilih pembayaran terlebih dahulu",
            }
        );
        this.tooltipNoDriver = tippy(`._js-tooltip-nodriver-${this.orderId}`, {
            content: "Pilih driver terlebih dahulu",
        });
    },
    methods: {
        safeParseInt(val) {
            const parsed = parseInt(val);
            if (isNaN(parsed)) {
                return 0;
            }
            return parsed;
        },
    },
    watch: {
        payment: function (newValue, oldValue) {
            this.customCash = 0;
        },
    },
};
</script>
