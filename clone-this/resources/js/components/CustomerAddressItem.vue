<template>
    <div
        :class="[
            'text-black w-full whitespace-normal p-1 border-gray-300',
            { 'border-t': withBorder },
        ]"
    >
        <div class="mt-1 mx-0.5 mb-1">
            👤
            <span v-html="highlightKeyword(option.name, keyword)" />
            <strong v-if="option.addresses_count > 1" class="text-xs">
                ({{ option.addresses_count }} Alamat)
            </strong>
        </div>
        <div class="mx-0.5 mb-1 text-xs text-gray-600">
            📱
            <span v-html="highlightKeyword(option.phone, keyword)" />
        </div>
        <div class="mb-1 mx-0.5 text-sm text-gray-600">
            🏠
            <span class="font-black text-black">{{ option.label }}:</span>
            {{ option.address }}
        </div>
        <div
            class="mb-1 mx-0.5 text-sm text-gray-600 font-bold"
            :style="{ color: randomColorsWithContrast[option.store_id] }"
        >
            🛍 <span class="font-black">Toko</span> {{ option.store_name }}
        </div>
        <div
            v-if="option.note_special"
            class="mb-1 mx-0.5 text-sm font-bold text-gray-600"
        >
            📝 {{ option.note_special }}
        </div>
        <div
            v-if="option.deposit != 0"
            class="mb-1 mx-0.5 text-lg font-bold"
            :style="{
                color:
                    option.deposit > 0
                        ? 'limegreen'
                        : option.deposit < 0
                        ? 'red'
                        : '',
            }"
        >
            🌿
            {{ option.deposit < 0 ? "-" : "" }} Rp
            {{ Math.abs(option.deposit).toLocaleString("id") }}
            {{ option.deposit < 0 ? " (KURANG BAYAR)" : " (SISA DEPOSIT)" }}
        </div>
    </div>
</template>

<script>
export default {
    props: {
        option: Object,
        keyword: String,
        withBorder: Boolean,
        // randomColorsWithContrast: Array,
    },
    data: function () {
        return {
            randomColorsWithContrast: [
                "#000000",
                "#dc2626",
                "#d97706",
                "#65a30d",
                "#059669",
                "#0891b2",
                "#2563eb",
                "#7c3aed",
                "#be123c",
                "#450a0a",
                "#713f12",
                "#365314",
                "#164e63",
                "#4c1d95",
                "#701a75",
                "#881337",
                "#020617",
            ],
        };
    },
    methods: {
        highlightKeyword(words, keyword) {
            if (!keyword) {
                return words;
            }

            const escapedKeyword = keyword.replace(
                /[-\/\\^$*+?.()|[\]{}]/g,
                "\\$&"
            );
            const regex = new RegExp(`(${escapedKeyword})`, "gi");

            return words.replace(
                regex,
                '<span class="bg-yellow-200">$1</span>'
            );
        },
    },
};
</script>
