<template>
    <div>
        <select
            v-if="orderPaymentMethodAsk === 'non-cash'"
            :data-order_id="orderId"
            :class="[
                '_js-input-bank-confirmed-' + orderId,
                '_js-input-bank-confirmed pointer-events-auto relative block w-full mt-4 rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50',
            ]"
            :disabled="safeParseInt(orderStatusId) !== 4"
            v-model="bankConfirmed"
        >
            <option value="">Pilih rekening..</option>
            <option
                v-for="(bank, index) in banksParsed"
                :key="bank.id"
                :value="bank.id"
                :selected="safeParseInt(bankId) == safeParseInt(bank.id)"
            >
                {{ bank.bank_name }} / {{ bank.account_number }} /
                {{ bank.holder_name }}
            </option>
            <option value="invoice">🧾 AR (Account Receivable)</option>
        </select>
        <textarea
            v-if="safeParseInt(orderStatusId) === 4"
            id="confirm_note"
            name="confirm_note"
            placeholder="Catatan konfirmasi..."
            :class="[
                '_js-input-confirm-note-' + orderId,
                'mt-3 block w-full pointer-events-auto rounded-md border-gray-300 shadow-sm focus:border-blue-300 focus:ring focus:ring-red-200 focus:ring-opacity-50',
            ]"
            rows="2"
        ></textarea>
        <button
            v-if="safeParseInt(orderStatusId) === 4"
            type="button"
            :data-order_id="orderId"
            :disabled="orderPaymentMethodAsk === 'non-cash' && !bankConfirmed"
            :class="[
                '_js-btn-confirm mt-3 pointer-events-auto relative text-white bg-green-500 py-2 shadow-lg px-2 rounded font-bold w-full',
                orderPaymentMethodAsk === 'non-cash' && bankConfirmed == ''
                    ? 'opacity-50'
                    : '',
            ]"
        >
            CONFIRM
        </button>
    </div>
</template>

<script>
export default {
    props: {
        bankId: Number,
        banks: Array,
        orderPaymentMethodAsk: String,
        roleId: Number,
        orderId: Number,
        orderStatusId: Number,
    },
    computed: {
        banksParsed: function () {
            return this.banks ? parseJson(this.banks) : null;
        },
    },
    data: function () {
        return {
            bankConfirmed: "",
        };
    },
};
</script>
