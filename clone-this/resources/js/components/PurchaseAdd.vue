<template>
    <div class="flex flex-col gap-5">
        <label class="relative block" for="transaction_date">
            <div
                :ref="`scrollview-transaction_date`"
                id="input_vendor"
                class="absolute left-0 -top-16"
            ></div>
            <div class="font-bold text-lg text-gray-900">
                Tanggal <span class="text-red-600">*</span>
            </div>
            <input
                id="transaction_date"
                name="transaction_date"
                type="date"
                required
                :class="[
                    'block w-full mt-1 rounded-md shadow-sm focus:ring focus:ring-opacity-50',
                    {
                        'border-gray-300 focus:border-blue-300 focus:ring-blue-200':
                            !temp.errors.hasOwnProperty(`transaction_date`),
                    },
                    {
                        'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                            temp.errors.hasOwnProperty(`transaction_date`),
                    },
                ]"
                @input="
                    (value) => {
                        removeError(`transaction_date`);
                    }
                "
                v-model="input.transaction_date"
            />
            <p
                v-if="temp.errors.hasOwnProperty(`transaction_date`)"
                class="text-sm text-red-600"
            >
                {{ temp.errors[`transaction_date`][0] }}
            </p>
        </label>
        <label class="relative block" for="store_id">
            <div
                :ref="`scrollview-store_id`"
                id="input_store_id"
                class="absolute left-0 -top-16"
            ></div>
            <div class="font-bold text-lg text-gray-900">
                Toko <span class="text-red-600">*</span>
            </div>
            <select
                id="store_id"
                name="store_id"
                required
                :class="[
                    'block w-full mt-1 rounded-md shadow-sm focus:ring focus:ring-opacity-50',
                    {
                        'border-gray-300 focus:border-blue-300 focus:ring-blue-200':
                            !temp.errors.hasOwnProperty(`store_id`),
                    },
                    {
                        'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                            temp.errors.hasOwnProperty(`store_id`),
                    },
                ]"
                v-model="input.store_id"
                @change="
                    (value) => {
                        removeError(`store_id`);
                    }
                "
            >
                <option value="">Pilih toko..</option>
                <option
                    v-for="store in stores"
                    :key="parseInt(store.id)"
                    :value="parseInt(store.id)"
                >
                    {{ store.name }}
                </option>
            </select>
            <p
                v-if="temp.errors.hasOwnProperty(`store_id`)"
                class="text-sm text-red-600"
            >
                {{ temp.errors[`store_id`][0] }}
            </p>
        </label>

        <label class="relative block" for="vendor_no">
            <div
                :ref="`scrollview-vendor_no`"
                id="input_vendor_no"
                class="absolute left-0 -top-16"
            ></div>
            <div class="font-bold text-lg text-gray-900">
                Vendor <span class="text-red-600">*</span>
            </div>
            <select
                id="vendor_no"
                name="vendor_no"
                required
                :class="[
                    'block w-full mt-1 rounded-md shadow-sm focus:ring focus:ring-opacity-50',
                    {
                        'border-gray-300 focus:border-blue-300 focus:ring-blue-200':
                            !temp.errors.hasOwnProperty(`vendor_no`),
                    },
                    {
                        'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                            temp.errors.hasOwnProperty(`vendor_no`),
                    },
                ]"
                v-model="input.vendor_no"
                @change="
                    (value) => {
                        removeError(`vendor_no`);
                    }
                "
            >
                <option value="">Pilih vendor..</option>
                <option
                    v-for="vendor in optionsVendor"
                    :key="vendor.vendorNo"
                    :value="vendor.vendorNo"
                >
                    {{ vendor.name }} ⎯ {{ vendor.lookupSubText }}
                </option>
            </select>
            <p
                v-if="temp.errors.hasOwnProperty(`vendor_no`)"
                class="text-sm text-red-600"
            >
                {{ temp.errors[`vendor_no`][0] }}
            </p>
        </label>

        <label class="relative block" for="no_faktur">
            <div
                :ref="`scrollview-no_faktur`"
                id="input_vendor"
                class="absolute left-0 -top-16"
            ></div>
            <div class="font-bold text-lg text-gray-900">
                No Faktur <span class="text-red-600">*</span>
            </div>
            <input
                id="no_faktur"
                name="no_faktur"
                type="text"
                required
                :class="[
                    'block w-full mt-1 rounded-md shadow-sm focus:ring focus:ring-opacity-50',
                    {
                        'border-gray-300 focus:border-blue-300 focus:ring-blue-200':
                            !temp.errors.hasOwnProperty(`no_faktur`),
                    },
                    {
                        'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                            temp.errors.hasOwnProperty(`no_faktur`),
                    },
                ]"
                v-model="input.no_faktur"
                @input="
                    (value) => {
                        removeError(`no_faktur`);
                    }
                "
            />
            <p
                v-if="temp.errors.hasOwnProperty(`no_faktur`)"
                class="text-sm text-red-600"
            >
                {{ temp.errors[`no_faktur`][0] }}
            </p>
        </label>

        <!-- <label class="relative block" for="payment">
            <div
                :ref="`scrollview-payment`"
                id="input_payment"
                class="absolute left-0 -top-16"
            ></div>
            <div class="font-bold text-lg text-gray-900">
                Pembayaran <span class="text-red-600">*</span>
            </div>
            <select
                id="payment"
                name="payment"
                required
                :class="[
                    'block w-full mt-1 rounded-md shadow-sm focus:ring focus:ring-opacity-50',
                    {
                        'border-gray-300 focus:border-blue-300 focus:ring-blue-200':
                            !temp.errors.hasOwnProperty(`payment`),
                    },
                    {
                        'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                            temp.errors.hasOwnProperty(`payment`),
                    },
                ]"
                v-model="input.payment"
            >
                <option value="">Pilih pembayaran..</option>
                <option value="net 7">Non Tunai</option>
                <option value="Tunai">Tunai</option>
            </select>
            <p
                v-if="temp.errors.hasOwnProperty(`payment`)"
                class="text-sm text-red-600"
            >
                {{ temp.errors[`payment`][0] }}
            </p>
        </label> -->

        <hr class="mt-2" />
        <div class="flex flex-col gap-2">
            <div class="mt-1 text-lg font-bold text-gray-900 flex items-center">
                Produk
                <span class="text-gray-400 text-xs ml-1"
                    >{{ countProductQty }} Item /
                    {{ countProductVariants }} Variant</span
                >
                <span class="ml-auto text-sm"
                    >TOTAL (Rp{{ countProductTotalPrice }})</span
                >
            </div>
            <div
                class="block p-3 border-2 mb-2 border-blue-200 rounded-lg bg-blue-50 relative"
                v-for="(product, index) in input.products"
                :key="product.id"
            >
                <!-- BUTTON REMOVE PRODUCT -->
                <button
                    type="button"
                    :class="[
                        'absolute z-10 top-1.5 right-1.5 text-red-600',
                        {
                            'opacity-50 pointer-events-none':
                                temp.isLoadingSubmit,
                        },
                    ]"
                    @click="onClickRemoveProduct(index)"
                    :disabled="temp.isLoadingSubmit"
                >
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke-width="1.5"
                        stroke="currentColor"
                        class="h-5 w-5 stroke-2"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            d="M6 18 18 6M6 6l12 12"
                        />
                    </svg>
                </button>

                <!-- PRODUCT_ID -->
                <label :for="`product_id_${index}`" class="block relative">
                    <div
                        :id="'input_product_' + index"
                        :ref="`scrollview-products.${index}.product_id`"
                        class="absolute left-0 -top-16"
                    />
                    <div class="font-bold text-gray-900">
                        Nama Produk <span class="text-red-600">*</span>
                    </div>
                    <v-select
                        :ref="`input-products.${index}.product_id`"
                        :class="[
                            'block w-full mt-1 rounded-md shadow-sm focus:ring focus:ring-opacity-50',
                            {
                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200':
                                    !temp.errors.hasOwnProperty(
                                        `products.${index}.product_id`
                                    ),
                            },
                            {
                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                    temp.errors.hasOwnProperty(
                                        `products.${index}.product_id`
                                    ),
                            },
                        ]"
                        :name="'product_id_' + index"
                        required
                        :filterBy="productFilterBy"
                        :clearable="false"
                        placeholder="Pilih produk.."
                        :options="optionsProducts"
                        :transition="''"
                        :value="product.product_id"
                        :selectOnTab="true"
                        @search="(search) => onSearchProduct(product, search)"
                        @input="
                            (value) => {
                                updateProductValue(product, value);
                                removeError(`products.${index}.product_id`);
                            }
                        "
                        @open="
                            document
                                .getElementById('input_product_' + index)
                                .scrollIntoView()
                        "
                        :disabled="temp.isLoadingSubmit"
                    >
                        <template slot="no-options">
                            {{
                                product.temp.product_keyword.length >= 2
                                    ? "Maaf, produk tidak ditemukan."
                                    : ""
                            }}
                        </template>
                        <template slot="option" slot-scope="option">
                            <product-item
                                :option="option"
                                :keyword="product.temp.product_keyword"
                                :withBorder="true"
                            />
                        </template>
                        <template slot="selected-option" slot-scope="option">
                            <product-item
                                :option="product.temp.product_value"
                                :keyword="product.temp.product_keyword"
                            />
                        </template>
                    </v-select>
                    <p
                        v-if="
                            temp.errors.hasOwnProperty(
                                `products.${index}.product_id`
                            )
                        "
                        class="text-sm text-red-600"
                    >
                        {{ temp.errors[`products.${index}.product_id`][0] }}
                    </p>
                </label>

                <!-- QTY -->
                <label class="block mt-3 mb-2 relative" for="qty">
                    <div
                        :ref="`scrollview-products.${index}.qty`"
                        class="absolute left-0 -top-16"
                    />
                    <div class="font-bold text-gray-900">
                        Qty <span class="text-red-600">*</span>
                    </div>
                    <input
                        :ref="`input-products.${index}.qty`"
                        type="number"
                        min="1"
                        name="qty"
                        required
                        :class="[
                            'block w-full mt-1 rounded-md shadow-sm focus:ring text-right focus:ring-opacity-50',
                            {
                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200':
                                    !temp.errors.hasOwnProperty(
                                        `products.${index}.qty`
                                    ),
                            },
                            {
                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                    temp.errors.hasOwnProperty(
                                        `products.${index}.qty`
                                    ),
                            },
                        ]"
                        @input="() => removeError(`products.${index}.qty`)"
                        v-model="product.qty"
                        placeholder="0"
                        :disabled="temp.isLoadingSubmit"
                    />
                    <p
                        v-if="
                            temp.errors.hasOwnProperty(`products.${index}.qty`)
                        "
                        class="text-sm text-red-600"
                    >
                        {{ temp.errors[`products.${index}.qty`][0] }}
                    </p>
                </label>

                <!-- Price -->
                <label class="block mt-2.5 mb-1 relative" for="price">
                    <div
                        :ref="`scrollview-products.${index}.price`"
                        class="absolute left-0 -top-16"
                    />
                    <div class="font-bold text-gray-900">
                        Harga @Item <span class="text-red-600">*</span>
                    </div>
                    <money
                        :ref="`input-products.${index}.price`"
                        name="price"
                        required
                        :class="[
                            'block w-full mt-1 rounded-md shadow-sm focus:ring text-right focus:ring-opacity-50',
                            {
                                'border-gray-300 focus:border-blue-300 focus:ring-blue-200':
                                    !temp.errors.hasOwnProperty(
                                        `products.${index}.price`
                                    ),
                            },
                            {
                                'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                                    temp.errors.hasOwnProperty(
                                        `products.${index}.price`
                                    ),
                            },
                        ]"
                        @input="() => removeError(`products.${index}.price`)"
                        v-bind="money"
                        v-model="product.price"
                        placeholder="0"
                        :disabled="temp.isLoadingSubmit"
                    />
                    <p
                        v-if="
                            temp.errors.hasOwnProperty(
                                `products.${index}.price`
                            )
                        "
                        class="text-sm text-red-600"
                    >
                        {{ temp.errors[`products.${index}.price`][0] }}
                    </p>
                </label>
            </div>

            <!-- BUTTON ADD PRODUCT -->
            <button
                type="button"
                :disabled="temp.isLoadingSubmit"
                :class="[
                    'w-full mt-0 px-4 py-2 text-sm font-bold tracking-widest text-white uppercase bg-blue-500 rounded-md shadow-lg focus:outline-none',
                    {
                        'opacity-50 pointer-events-none': temp.isLoadingSubmit,
                    },
                ]"
                @click="onClickAddProduct"
            >
                + Tambah Produk
            </button>
        </div>

        <div
            class="relative flex items-center justify-center overflow-hidden bg-black rounded-md"
            style="aspect-ratio: 1 / 1"
        >
            <!-- <svg
                class="w-10 h-10 text-white animate-spin"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
            >
                <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                ></circle>
                <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
            </svg> -->
            <div class="absolute inset-0 z-10 flex items-center">
                <video id="webcam" autoplay playsinline class="w-full"></video>
                <canvas id="canvas" class="hidden"></canvas>
                <audio
                    id="snapSound"
                    src="/audio/demo_audio_snap.wav"
                    preload="auto"
                ></audio>
            </div>
            <div class="absolute left-0 right-0 z-20 bottom-20">
                <div
                    class="relative flex-shrink-0 _js-wrp-img-capture-default"
                    style="display: none"
                >
                    <img src="" class="h-full _js-img-capture" />
                    <button
                        type="button"
                        class="absolute z-20 bg-white rounded-full _js-btn-delete-photo -top-3 -right-3"
                    >
                        <svg
                            xmlns="http://www.w3.org/2000/svg"
                            class="w-6 h-6 text-red-500"
                            viewBox="0 0 20 20"
                            fill="currentColor"
                        >
                            <path
                                fill-rule="evenodd"
                                d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z"
                                clip-rule="evenodd"
                            />
                        </svg>
                    </button>
                </div>
                <div
                    class="flex h-16 gap-4 pt-3 pr-3 overflow-x-auto _js-img-list scrollbar-hide"
                ></div>
            </div>
            <button
                type="button"
                class="absolute z-20 transform -translate-x-1/2 border-2 border-white rounded-full _js-btn-capture-camera group w-14 h-14 left-1/2 bottom-3"
            ></button>
            <button
                type="button"
                class="absolute z-20 flex items-center justify-center bg-black bg-opacity-50 rounded-full _js-btn-flip-camera right-3 bottom-5 w-11 h-11"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    class="w-6 h-6 text-white"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                >
                    <path
                        fill-rule="evenodd"
                        d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z"
                        clip-rule="evenodd"
                    />
                </svg>
            </button>
        </div>

        <label class="relative block" for="note">
            <div id="input_vendor" class="absolute -top-16"></div>
            <div class="font-bold text-lg text-gray-900">Catatan</div>
            <textarea
                id="note"
                name="note"
                class="block w-full border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
                rows="2"
                v-model="input.note"
            ></textarea>
        </label>

        <button
            @click="onClickSubmit"
            type="button"
            :class="[
                'px-4 py-3 rounded-md font-bold text-base shadow-xl text-white uppercase tracking-widest hover:opacity-90 active:opacity-90 focus:outline-none transition ease-in-out duration-150 w-full bg-red-600 relative mt-3 mb-5',
                temp.isLoadingSubmit && 'opacity-50 pointer-events-none',
            ]"
        >
            <svg
                v-if="temp.isLoadingSubmit"
                class="absolute top-0 h-full ml-auto text-white animate-spin w-7 left-5"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
            >
                <circle
                    class="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    stroke-width="4"
                ></circle>
                <path
                    class="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                ></path>
            </svg>
            <svg
                v-if="!temp.isLoadingSubmit"
                class="w-7 h-full ml-auto absolute -top-0.5 left-5 _js-btn-icon"
                xmlns="http://www.w3.org/2000/svg"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
            >
                <path
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    stroke-width="2"
                    d="M8 7H5a2 2 0 00-2 2v9a2 2 0 002 2h14a2 2 0 002-2V9a2 2 0 00-2-2h-3m-1 4l-3 3m0 0l-3-3m3 3V4"
                />
            </svg>
            {{ temp.isLoadingSubmit ? "SAVING PURCHASE" : "SAVE PURCHASE" }}
        </button>
    </div>
</template>

<script>
import "vue-select/dist/vue-select.css";
import { Money } from "v-money";
import axios from "axios";
import ProductItem from "./ProductItem";
export default {
    components: { Money, ProductItem },
    props: {
        url_base_temp: String,
        stores: Array,
        products: Array,
        current_store_id: Number,
        vendors: Object,
    },
    data: function () {
        return {
            document: document,
            money: {
                decimal: ",",
                thousands: ".",
                prefix: "Rp",
                suffix: "",
                precision: 0,
                masked: false /* doesn't work with directive */,
            },
            input: {
                store_id: parseInt(this.current_store_id),
                transaction_date: new Date().toISOString().split("T")[0],
                payment: "net 7",
                vendor_no: "",
                vendor_name: "",
                no_faktur: "",
                products: [this.getDefaultInputProduct()],
                photoreceipts: [],
                note: "",
            },
            // optionsVendor: this.vendors?.data,
            optionsVendor: this.vendors?.data?.filter((vendor) => {
                const store = this.stores.find(
                    (store) => store.id == this.current_store_id
                );
                let filterStore = true;
                if (store) {
                    filterStore =
                        vendor?.vendorBranchName === store.name ||
                        vendor?.vendorBranchName === "[Semua Cabang]";
                }
                const filterSuspended = !vendor?.suspended;

                return filterStore && filterSuspended;
            }),
            optionsProducts: this.products?.map((obj) => {
                const local_price =
                    obj.local_price && parseInt(obj.local_price) > 0
                        ? obj.local_price
                        : obj.price;
                obj["price"] =
                    obj.special_price && parseInt(obj.special_price) > 0
                        ? obj.special_price
                        : local_price;
                obj["label"] = obj.code;
                // obj["is_available"] =
                //     obj.special_is_available ?? obj.is_available;
                obj["is_available"] = 1;
                return obj;
            }),
            temp: this.getDefaultTemp(),
        };
    },
    computed: {
        countProductVariants() {
            const uniqueProductIds = new Set(
                this.input.products.map((product) => product.product_id)
            );
            return uniqueProductIds.size;
        },
        countProductQty() {
            return this.input.products.reduce(
                (acc, item) => acc + (parseInt(item.qty) || 0),
                0
            );
        },
        countProductTotalPrice() {
            const ttl = this.input.products.reduce(
                (acc, item) =>
                    acc + (parseInt(item.price) * parseInt(item.qty) || 0),
                0
            );
            return ttl.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ".");
        },
    },
    mounted() {
        this.initWebcam();
        axios
            .get(`/acc/vendor/list`)
            .then((res) => {
                if (
                    Array.isArray(res?.data?.data) &&
                    res.data.data.length > 0
                ) {
                    // this.optionsVendor = res?.data?.data || [];
                    this.optionsVendor = res.data.data.filter((vendor) => {
                        const store = this.stores.find(
                            (store) => store.id == this.current_store_id
                        );
                        let filterStore = true;
                        if (store) {
                            filterStore =
                                vendor?.vendorBranchName === store.name ||
                                vendor?.vendorBranchName === "[Semua Cabang]";
                        }
                        const filterSuspended = !vendor?.suspended;

                        return filterStore && filterSuspended;
                    });
                }
                // this.input.latlng = res.data;
            })
            .catch((err) => {
                console.log("🚀 ~ err:", err);
                mdtoast("UPDATE VENDOR LIST FAILED", {
                    duration: 1500,
                    type: mdtoast.ERROR,
                });
            })
            .finally(() => {
                // this.temp.isLoadingLatlng = false;
            });
    },
    methods: {
        moment: moment,
        initWebcam() {
            const ini = this;
            const webcamElement = document.getElementById("webcam");
            const canvasElement = document.getElementById("canvas");
            const snapSoundElement = document.getElementById("snapSound");
            const webcam = new Webcam(
                webcamElement,
                "user",
                canvasElement,
                snapSoundElement
            );
            webcam
                .start()
                .then((result) => {
                    console.log("webcam started");
                    webcam.flip();
                    webcam.start();
                })
                .catch((err) => {
                    console.log(err);
                });
            document.addEventListener("visibilitychange", (event) => {
                if (document.hidden) {
                    console.log("not visible");
                    webcam.stop();
                } else {
                    console.log("is visible");
                    webcam
                        .start()
                        .then((result) => {
                            console.log("webcam started");
                            webcam.flip();
                            webcam.start();
                        })
                        .catch((err) => {
                            console.log(err);
                        });
                }
            });
            $("._js-btn-flip-camera").on("click", () => {
                webcam.flip();
                webcam.start();
            });
            $("._js-btn-capture-camera").on("click", () => {
                const localId = Math.floor(Math.random() * 100) + 1;
                let photo = webcam.snap();
                $("._js-wrp-img-capture-default")
                    .find("._js-img-capture")
                    .prop("src", photo)
                    .prop("id", "capture_" + localId);
                $("._js-wrp-img-capture-default")
                    .clone()
                    .removeClass("_js-wrp-img-capture-default")
                    .addClass("_js-wrp-img-capture")
                    .prop("id", "img_capture_" + localId)
                    .show()
                    .appendTo("._js-img-list");
                mediumZoom("#capture_" + localId);
                const base64ImageContent = photo.replace(
                    /^data:image\/(png|jpg);base64,/,
                    ""
                );
                const blob = this.base64ToBlob(base64ImageContent, "image/jpg");
                console.log("🚀 ~ blob:", blob);
                this.input.photoreceipts.push({
                    id: `img_capture_${localId}`,
                    photo: blob,
                });
                console.log(
                    "🚀 ~ this.input.photoreceipts:",
                    this.input.photoreceipts
                );
            });
            $("._js-img-list").on(
                "click",
                "._js-btn-delete-photo",
                function () {
                    if (confirm("Hapus foto?")) {
                        const $parentWrpImage = $(this).parents(
                            "._js-wrp-img-capture"
                        );
                        const id = $parentWrpImage.attr("id");
                        ini.input.photoreceipts =
                            ini.input.photoreceipts.filter(
                                (photo) => photo.id !== id
                            );
                        $parentWrpImage.remove();
                        console.log(
                            "🚀 ~ this.input.photoreceipts:",
                            ini.input.photoreceipts
                        );
                    }
                }
            );
        },
        base64ToBlob(base64, mime) {
            mime = mime || "";
            var sliceSize = 1024;
            var byteChars = window.atob(base64);
            var byteArrays = [];

            for (
                var offset = 0, len = byteChars.length;
                offset < len;
                offset += sliceSize
            ) {
                var slice = byteChars.slice(offset, offset + sliceSize);

                var byteNumbers = new Array(slice.length);
                for (var i = 0; i < slice.length; i++) {
                    byteNumbers[i] = slice.charCodeAt(i);
                }

                var byteArray = new Uint8Array(byteNumbers);

                byteArrays.push(byteArray);
            }

            return new Blob(byteArrays, {
                type: mime,
            });
        },
        removeError(key) {
            // console.log("🚀 ~ removeError:", key);
            delete this.temp.errors[key];
        },
        getDefaultInputProduct() {
            return {
                id: this.generateRandomId(),
                product_id: "",
                qty: 1,
                price: 0,
                temp: {
                    product_keyword: "",
                    product_value: "",
                },
            };
        },
        getDefaultTemp() {
            return {
                isLoadingSubmit: false,
                errors: {},
            };
        },
        generateRandomId() {
            return Math.random().toString(36).substr(2, 9);
        },
        onClickRemoveProduct(index) {
            if (confirm("Hapus produk dari keranjang?")) {
                this.input.products.splice(index, 1);
            }
        },
        productFilterBy(option, label, search) {
            return (
                option.code.toLowerCase().includes(search.toLowerCase()) ||
                option.name.toLowerCase().includes(search.toLowerCase())
            );
        },
        onSearchProduct(product, search) {
            product.temp.product_keyword = search;
        },
        updateProductValue(product, selectedValue) {
            console.log("🚀 ~ selectedValue:", selectedValue);
            product.temp.product_value = selectedValue;
            product.product_id = selectedValue?.id;
            product.qty = 1;
            product.price = 0;
        },
        onClickAddProduct() {
            this.input.products.push(this.getDefaultInputProduct());
        },
        onClickSubmit: function () {
            if ($("._js-form")[0].reportValidity()) {
                this.temp.isLoadingSubmit = true;
                const formData = new FormData();
                let photoscount = 1;
                this.input.photoreceipts.forEach((pht) => {
                    formData.append(`photoreceipts_${photoscount}`, pht.photo);
                    photoscount++;
                });
                const ini = this;
                const { photoreceipts, ...input } = this.input;
                formData.append("photoscount", photoscount);
                formData.append("inputs", JSON.stringify(input));
                // console.log(formData.get("photoreceipts_0"));
                // console.log(formData.get("photoreceipts_1"));
                // console.log(formData.get("photoreceipts_2"));
                // console.log(formData.get("photoreceipts_3"));
                // console.log(formData.get("photoreceipts_4"));
                // this.temp.isLoadingSubmit = false;
                // return;
                axios
                    .post(
                        "/manager/purchase-post/" + this.current_store_id,
                        formData,
                        {
                            headers: {
                                "Content-Type": "multipart/form-data",
                            },
                        }
                    )
                    .then((response) => {
                        this.temp.isLoadingSubmit = false;
                        console.log("🚀 ~ response:", response);
                        if (response?.data?.url_redirect) {
                            Swal.fire({
                                title: "Purchase Saved!",
                                text: "Redirecting...",
                                icon: "success",
                                showConfirmButton: false,
                                // confirmButtonText: "Tutup",
                            });
                            window.location.href = response?.data?.url_redirect;
                        } else {
                            mdtoast("TERJADI KESALAHAN!", {
                                duration: 1500,
                                type: mdtoast.ERROR,
                            });
                        }
                    })
                    .catch((error) => {
                        this.temp.isLoadingSubmit = false;
                        console.log("🚀 ~ error:", error);
                        const errorsData = error?.response?.data;
                        console.log("🚀 ~ errorsData:", errorsData);
                        const errors = error?.response?.data?.errors;
                        // console.log("🚀 ~ errors inputs:", errors);
                        if (!errors || Object.keys(errors).length === 0) {
                            mdtoast(
                                errorsData?.message ?? "TERJADI KESALAHAN",
                                {
                                    duration: 1500,
                                    type: mdtoast.ERROR,
                                }
                            );
                            return;
                        }
                        this.temp.errors = errors ?? {};
                        const firstErrorKey = Object.keys(errors)[0];
                        // console.log("🚀 ~ firstErrorKey:", firstErrorKey);
                        const firstErrorMessage = errors[firstErrorKey][0];
                        ini.$nextTick(() => {
                            let firstErrorScrollView =
                                ini.$refs[`scrollview-${firstErrorKey}`];
                            if (Array.isArray(firstErrorScrollView)) {
                                firstErrorScrollView = firstErrorScrollView[0];
                            }
                            // console.log(
                            //     "🚀 ~ firstErrorScrollView:",
                            //     firstErrorScrollView
                            // );
                            let firstErrorInput =
                                ini.$refs[`input-${firstErrorKey}`];
                            if (Array.isArray(firstErrorInput)) {
                                firstErrorInput = firstErrorInput[0];
                            }
                            if (firstErrorInput?.$el) {
                                firstErrorInput = firstErrorInput.$el;
                            }
                            // console.log("🚀 ~ firstErrorInput:", firstErrorInput);
                            if (firstErrorScrollView) {
                                firstErrorScrollView.scrollIntoView();
                            }
                            if (firstErrorInput) {
                                setTimeout(() => {
                                    firstErrorInput.focus();
                                }, 60);
                            }
                            if (!firstErrorScrollView || !firstErrorInput) {
                                mdtoast(firstErrorMessage, {
                                    duration: 1500,
                                    type: mdtoast.ERROR,
                                });
                            }
                        });
                    })
                    .finally(() => {
                        this.temp.isLoadingSubmit = false;
                    });
            }
        },
    },
    watch: {
        "input.store_id": function (newValue, oldValue) {
            const store = this.stores.find((s) => s.id === parseInt(newValue));
            const url = new URL(
                this.url_base_temp.replace("xxx", store.slug),
                window.location.origin
            );
            const params = new URLSearchParams(window.location.search);
            url.search = params.toString();
            window.location.replace(url.toString());
        },
        "input.vendor_no": function (newValue, oldValue) {
            const vendor = this.optionsVendor.find(
                (i) => i.vendorNo === newValue
            );
            this.input.vendor_name = vendor?.name || "";
        },
    },
};
</script>
