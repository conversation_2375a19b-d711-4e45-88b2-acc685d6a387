<template>
    <label class="relative block" :for="alias">
        <div
            :ref="`scrollview-${alias}`"
            :id="`input_${alias}`"
            class="absolute left-0 -top-16"
        ></div>
        <div class="font-bold text-lg text-gray-900">
            {{ label }} <span class="text-red-600">*</span>
        </div>
        <select
            :id="alias"
            :name="alias"
            required
            :class="[
                'block w-full mt-1 rounded-md shadow-sm focus:ring focus:ring-opacity-50',
                {
                    'border-gray-300 focus:border-blue-300 focus:ring-blue-200':
                        !error,
                },
                {
                    'border-red-600 text-red-700 bg-red-200 focus:border-red-500 focus:ring-red-400':
                        error,
                },
            ]"
            v-model="localStoreId"
        >
            <option value="">Pilih toko..</option>
            <option
                v-for="option in options"
                :key="parseInt(option.id)"
                :value="parseInt(option.id)"
            >
                {{ option.name }}
            </option>
        </select>
        <p v-if="error" class="text-sm text-red-600">
            {{ error }}
        </p>
    </label>
</template>

<script>
export default {
    props: {
        options: {
            type: Array,
            required: false,
            default: [],
        },
        storeId: {
            type: Number,
            required: true,
        },
        error: {
            type: String,
            required: false,
            default: "",
        },
        alias: {
            type: String,
            default: "store_id",
        },
        label: {
            type: String,
            default: "Toko",
        },
    },
    data() {
        return {
            localStoreId: this.storeId,
        };
    },
    watch: {
        localStoreId(newValue) {
            this.$emit("update:storeId", newValue);
        },
        storeId(newValue) {
            this.localStoreId = newValue;
        },
    },
    emits: ["update:storeId"],
};
</script>
