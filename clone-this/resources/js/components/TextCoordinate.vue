<template>
  <div>
    <div class="d-flex align-items-center relative">
      <input
        :class="['SharpText hide-controls', { SharpTextReadOnly: readonly }]"
        :placeholder="placeholder"
        :type="type[inputType]"
        :value="value"
        @input="handleChanged"
        @change="handleChanged"
        @blur="handleChanged"
        :readonly="readonly"
        :disabled="isDisabled"
      />
      <div class="spin" v-if="isLoading"></div>
    </div>
    <div
      v-if="isInvalid"
      class="SharpForm__form-requirement d-block input-error"
    >
      Input is invalid!
    </div>
  </div>
</template>
<script>
import axios from "axios";
export default {
  props: {
    value: String,
    inputType: String, // custom added props (given in field definition)
    placeholder: String, // custom added props (given in field definition)
    readonly: Boolean,
  },
  data() {
    return {
      type: {
        text: "text",
        phone: "tel",
        color: "color",
      },
      timeoutGetCoordinate: null,
      isInvalid: false,
      isLoading: false,
      isDisabled: false,
    };
  },
  methods: {
    getUrlOnly(input) {
      if (!input) return "";

      // const urlRegex = /(((https?:\/\/)|(www\.))[^\s]+)/g;
      // const urlRegex = /^(http[s]?:\/\/.*?\/[a-zA-Z-_]+.*)$/;
      const urlRegex = /(https?:\/\/[^ ]*)/;
      return input.match(urlRegex)[1];
      // return message.replace(urlRegex, function (url) {
      //     let hyperlink = url;
      //     if (!hyperlink.match('^https?:\/\/')) {
      //     hyperlink = 'http://' + hyperlink;
      //     }
      //     // return '<a href="' + hyperlink + '" target="_blank" rel="noopener noreferrer">' + url + '</a>'
      //     return hyperlink;
      // });
    },
    handleChanged(e) {
      let val = e.target.value;
      this.$emit("input", val); // emit input when the value change, form data is updated

      if (!val) {
        this.isInvalid = false;
      }
      clearTimeout(this.timeoutGetCoordinate);
      this.timeoutGetCoordinate = setTimeout(async () => {
        const regexLatLng =
          /^[-+]?([1-8]?\d(\.\d+)?|90(\.0+)?),\s*[-+]?(180(\.0+)?|((1[0-7]\d)|([1-9]?\d))(\.\d+)?)$/g;
        const passLatLng = regexLatLng.test(val);

        if (!passLatLng) {
          const url = this.getUrlOnly(val);
          const regexGmapUrl = /(\google.com+)/g;
          const regexGmapShare = /(\goo.gl+)/g;
          const regexGPage = /(\g.page+)/g;
          const passGmapShare = regexGmapShare.test(url);
          const passGmapUrl = regexGmapUrl.test(url);
          const passGPage = regexGPage.test(url);

          if (passGmapUrl || passGmapShare) {
            this.isLoading = true;
            this.isDisabled = true;
            const thisVue = this;
            axios
              .get("/manager/ajax/get-coordinate-gmap", {
                params: {
                  url: val,
                },
              })
              .then(function (res) {
                if (res && res.data) {
                  console.log("get-coordinate-gmap res", res.data);
                  thisVue.$emit("input", res.data);
                  thisVue.isLoading = false;
                  thisVue.isDisabled = false;
                  thisVue.isInvalid = false;
                } else {
                  console.log("res", res);
                  thisVue.isLoading = false;
                  thisVue.isDisabled = false;
                  thisVue.isInvalid = true;
                }
              })
              .catch(function (err) {
                console.log("err", err);
                thisVue.isLoading = false;
                thisVue.isDisabled = false;
                thisVue.isInvalid = true;
              });
            // $.ajax({
            //   url: "{{ route('get-coordinate-gmap') }}",
            //   data: {
            //     url: url,
            //   },
            //   type: "GET",
            //   error: function (err) {
            //     console.log("err", err);
            //     $("._js-loading-coordinate").hide();
            //     $this.prop("disabled", false);
            //     setInputDefault($this);
            //     mdtoast("TERJADI KESALAHAN", {
            //       duration: 1500,
            //       type: mdtoast.ERROR,
            //     });
            //   },
            //   success: function (res) {
            //     console.log("get-coordinate-gmap res", res);
            //     // const coordinate = getCoordinateFromUrl(res);
            //     // console.log('coordinate', coordinate);
            //     $this.val(res);
            //     $("._js-loading-coordinate").hide();
            //     $this.prop("disabled", false);
            //     setInputDefault($this);
            //   },
            // });
            // } else if (passGmapUrl) {
            //     console.log('passGmapUrl');
            //     const coordinate = await getCoordinateFromUrl(this.getUrlOnly(val));
            //     $this.val(coordinate);
            //     setInputDefault($this);
          } else {
            this.isInvalid = true;
          }
        } else {
          this.isInvalid = false;
        }
      }, 500);
    },
  },
  watch: {
    price: function (val, oldVal) {
      // console.log('new: %s, old: %s', val, oldVal);
      this.$emit("input", val);
    },
  },
};
</script>

<style type="text/css" scoped>
@keyframes spinner {
  0% {
    transform: translate3d(-50%, -50%, 0) rotate(0deg);
  }
  100% {
    transform: translate3d(-50%, -50%, 0) rotate(360deg);
  }
}
.relative {
  position: relative;
}
.input-error {
  max-height: 12.5rem;
  background: rgba(231, 29, 50, 0.1);
  margin: 0;
  padding: 0.5rem 1rem;
}
.spin {
  position: absolute;
  /* top: 50%; */
  right: 20px;
  /* transform: translateY(-50%); */
  z-index: 10;
  pointer-events: none;
}
.spin::before {
  animation: 1.5s linear infinite spinner;
  animation-play-state: inherit;
  border: solid 5px #cfd0d1;
  border-bottom-color: #314255;
  border-radius: 50%;
  content: "";
  height: 30px;
  width: 30px;
  position: absolute;
  top: 10%;
  left: 10%;
  transform: translate3d(-50%, -50%, 0);
  will-change: transform;
}
.SharpTextReadOnly {
  opacity: 0.5;
}
</style>
