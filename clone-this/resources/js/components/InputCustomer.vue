<template>
    <div>
        <!-- <input
            :id="id"
            :name="id"
            :value="value"
            @input="onInput"
            class="input-field"
        /> -->
        <select
            :id="id"
            :name="id"
            :value="value"
            @input="onInput"
            required
            class="block w-full mt-1 border-gray-300 rounded-md _js-input-pelanggan focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
        >
            <option value="">Cari nama / no. wa / alamat ...</option>
            <option value="123">123</option>
        </select>

        <!-- <select
            id="oldnew"
            name="oldnew"
            required
            v-model="oldnew"
            class="block w-full mt-1 border-gray-300 rounded-md shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50"
        >
            <option value="">Pilih tipe job..</option>
            <option value="old">Pelanggan Lama</option>
            <option value="new">Pelanggan Baru</option>
            <option value="marketing">Bagi <PERSON>ur</option>
            <option value="asdt">ASDT</option>
            <option value="konsumsitoko">Konsumsi Toko</option>
        </select> -->
    </div>
</template>

<script>
import "vue-select/dist/vue-select.css";
export default {
    name: "InputCustomer",
    props: {
        value: {
            type: [String, Number],
            required: true,
        },
        id: {
            type: String,
            default: "",
        },
        // type: {
        //     type: String,
        //     default: "text",
        // },
        // placeholder: {
        //     type: String,
        //     default: "",
        // },
        // disabled: {
        //     type: Boolean,
        //     default: false,
        // },
    },
    mounted() {
        const randomColorsWithContrast = [
            "#000000",
            "#dc2626",
            "#d97706",
            "#65a30d",
            "#059669",
            "#0891b2",
            "#2563eb",
            "#7c3aed",
            "#be123c",
            "#450a0a",
            "#713f12",
            "#365314",
            "#164e63",
            "#4c1d95",
            "#701a75",
            "#881337",
            "#020617",
        ];
    },
    methods: {
        onInput(event) {
            // Emit the input value to the parent component
            this.$emit("input", event.target.value);
        },
    },
};
</script>

<style scoped>
.text-input {
    margin-bottom: 1rem;
}

label {
    display: block;
    margin-bottom: 0.5rem;
}

.input-field {
    padding: 0.5rem;
    width: 100%;
    font-size: 1rem;
    border: 1px solid #ccc;
    border-radius: 4px;
}
</style>
