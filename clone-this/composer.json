{"name": "laravel/laravel", "type": "project", "description": "The Laravel Framework.", "keywords": ["framework", "laravel"], "license": "MIT", "require": {"php": "^8.2", "barryvdh/laravel-dompdf": "^2.0", "code16/sharp": "^5.3", "doctrine/dbal": "^3.4", "fruitcake/laravel-cors": "^2.0", "guzzlehttp/guzzle": "^7.0.1", "laravel/framework": "^9.0", "laravel/sanctum": "^3.0", "laravel/telescope": "^4.0", "laravel/tinker": "^2.5", "phpoffice/phpspreadsheet": "^2.0", "predis/predis": "^2.2", "silviolleite/laravelpwa": "^2.0", "spatie/laravel-image-optimizer": "^1.6", "sqits/laravel-userstamps": "^0.0.9"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.6", "spatie/laravel-ignition": "^1.0", "fakerphp/faker": "^1.9.1", "laravel/breeze": "^1.0", "mockery/mockery": "^1.4.2", "nunomaduro/collision": "^6.0", "phpunit/phpunit": "^9.3.3"}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true}, "extra": {"laravel": {"dont-discover": []}}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/helpers.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "minimum-stability": "dev", "prefer-stable": true, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan vendor:publish --provider=Code16\\Sharp\\SharpServiceProvider --tag=assets --force", "@php artisan package:discover --ansi"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"], "post-update-cmd": ["@php artisan telescope:publish --ansi"]}}