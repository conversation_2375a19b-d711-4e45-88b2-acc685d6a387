const mix = require("laravel-mix");

/*
 |--------------------------------------------------------------------------
 | Mix Asset Management
 |--------------------------------------------------------------------------
 |
 | Mix provides a clean, fluent API for defining some Webpack build steps
 | for your Laravel applications. By default, we are compiling the CSS
 | file for the application as well as bundling up all the JS files.
 |
 */

mix.js("resources/js/app.js", "public/js").postCss(
    "resources/css/app.css",
    "public/css",
    [require("postcss-import"), require("tailwindcss"), require("autoprefixer")]
)
.version();

mix.sass("resources/sass/inject.scss", "public/css").version();
mix.sass("resources/sass/manager.scss", "public/css").version();
mix.js("resources/js/sharp-plugin.js", "public/js").version();
// mix.copy("public/js/sharp-plugin.js", "js/sharp-plugin.js");
// mix.copy(
//     "resources/sharp/MenuViewComposer.php",
//     "/vendor/code16/sharp/src/Http/Composers/MenuViewComposer.php"
// );
// mix.copy(
//     "resources/sharp/layout.blade.php",
//     "/vendor/code16/sharp/resources/views/layout.blade.php"
// );
// mix.copy(
//     "resources/sharp/_menu.blade.php",
//     "/vendor/code16/sharp/resources/views/partials/_menu-item.blade.php"
// );
mix.copyDirectory("public/css", "css");
mix.copyDirectory("public/js", "js");
mix.copyDirectory("public/vendor/sharp", "vendor/sharp");
mix.disableNotifications();
