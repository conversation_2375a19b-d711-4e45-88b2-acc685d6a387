<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTableNotifWa extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        Schema::create('notifications', function (Blueprint $table) {
            $table->id();
            $table->morphs('notifable');
            $table->enum('type', ['message', 'forward'])->default('message');
            $table->string('queue')->default('default');
            $table->string('from')->nullable();
            $table->string('to');
            $table->text('message')->nullable();
            $table->text('footer')->nullable();
            $table->json('forward_message')->nullable();
            $table->enum('status', ['waiting', 'processing', 'success', 'failed'])->default('waiting');
            $table->timestamp('sent_at')->nullable();
            $table->text('note')->nullable();
            $table->timestamps();
            $table->userstamps();
        });
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        Schema::dropIfExists('notifications');
    }
}