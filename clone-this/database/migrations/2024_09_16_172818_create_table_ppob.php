<?php

use App\Models\Category;
use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

class CreateTablePpob extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (!Schema::hasTable('ppob_customer_datas')) {
            Schema::create('ppob_customer_datas', function (Blueprint $table) {
                $table->id();
                $table->string('key')->index();
                $table->json('inquiry_data')->nullable();
                $table->unsignedBigInteger('product_id');
                $table->foreign('product_id')->references('id')->on('products');
                $table->unsignedBigInteger('customer_id');
                $table->foreign('customer_id')->references('id')->on('customers');
                // $table->timestamps();
            });
        }

        if (!Schema::hasTable('ppob_response_datas')) {
            Schema::create('ppob_response_datas', function (Blueprint $table) {
                $table->id();
                $table->string('ppob_ref_id')->index();
                $table->json('response_data');
                $table->string('error_message')->nullable();
                $table->unsignedBigInteger('product_id');
                $table->foreign('product_id')->references('id')->on('products');
                $table->unsignedBigInteger('customer_id');
                $table->foreign('customer_id')->references('id')->on('customers');
                // $table->unsignedBigInteger('order_product_id');
                // $table->foreign('order_product_id')->references('id')->on('order_product');
                $table->timestamps();
                // $table->softDeletes();
                // $table->userstamps();
                // $table->softUserstamps();
            });
        }

        if (Schema::hasTable('order_product')) {
            if (!Schema::hasColumn('order_product', 'ppob_key')) {
                Schema::table('order_product', function (Blueprint $table) {
                    $table->string('ppob_key')->nullable();
                    $table->string('ppob_product_code')->nullable();
                    $table->integer('ppob_nominal')->nullable();
                    $table->integer('ppob_price')->nullable();
                    $table->string('ppob_ref_id')->nullable()->index();
                    $table->string('ppob_status')->nullable();
                    $table->timestamp('notif_sent_at')->nullable();
                    $table->unsignedBigInteger('ppob_response_data_id')->nullable();
                    $table->foreign('ppob_response_data_id')->references('id')->on('ppob_response_datas');
                });
            }
        }

        // $category_ppob = Category::where('slug', 'ppob')->first();
        // if (!$category_ppob) {
        //     $category_ppob = Category::create([
        //         'slug' => 'ppob',
        //         'name' => 'PPOB',
        //     ]);
        // }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('order_product', 'ppob_key')) {
            Schema::table('order_product', function (Blueprint $table) {
                $table->dropColumn('ppob_key');
                $table->dropColumn('ppob_product_code');
                $table->dropColumn('ppob_nominal');
                $table->dropColumn('ppob_price');
                $table->dropColumn('ppob_ref_id');
                $table->dropColumn('ppob_status');
                $table->dropColumn('notif_sent_at');
                $table->dropForeign(['ppob_response_data_id']);
                $table->dropColumn('ppob_response_data_id');
            });
        }
        Schema::dropIfExists('ppob_response_datas');
        Schema::dropIfExists('ppob_customer_datas');
    }
}