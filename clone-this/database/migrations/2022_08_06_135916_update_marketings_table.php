<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateMarketingsTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('areas')) {
            if (!Schema::hasColumn('areas', 'count')) {
                Schema::table('areas', function (Blueprint $table) {
                    $table->integer('count')->default(0)->after('latlng');
                });
            }
            if (!Schema::hasColumn('areas', 'distance_store_area')) {
                Schema::table('areas', function (Blueprint $table) {
                    $table->integer('distance_store_area')->nullable()->after('latlng'); // in meter
                });
            }
            if (!Schema::hasColumn('areas', 'store_id')) {
                Schema::table('areas', function (Blueprint $table) {
                    $table->unsignedBigInteger('store_id')->nullable()->after('id');
                    $table->foreign('store_id')->references('id')->on('stores');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('areas', 'store_id')) {
            Schema::table('areas', function (Blueprint $table) {
                $table->dropForeign(['store_id']);
                $table->dropColumn('store_id');
            });
        }
        if (Schema::hasColumn('areas', 'distance_store_area')) {
            Schema::table('areas', function (Blueprint $table) {
                $table->dropColumn('distance_store_area');
            });
        }
        if (Schema::hasColumn('areas', 'count')) {
            Schema::table('areas', function (Blueprint $table) {
                $table->dropColumn('count');
            });
        }
    }
}