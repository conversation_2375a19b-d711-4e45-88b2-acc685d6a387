<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateTablepruchase extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('purchases')) {
            if (!Schema::hasColumn('purchases', 'bill_number')) {
                Schema::table('purchases', function (Blueprint $table) {
                    $table->renameColumn('vendor_acc_id', 'vendor_acc_no');
                    $table->string('bill_number');
                    $table->string('payment')->default('net 7');
                    $table->text('note')->nullable();
                    $table->date('transaction_date');
                });
            }
        }

        if (Schema::hasTable('product_purchase')) {
            if (!Schema::hasColumn('product_purchase', 'warehouse_acc_name')) {
                Schema::table('product_purchase', function (Blueprint $table) {
                    $table->string('warehouse_acc_name')->nullable();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('product_purchase', 'warehouse_acc_name')) {
            Schema::table('product_purchase', function (Blueprint $table) {
                $table->dropColumn('warehouse_acc_name');
            });
        }

        if (Schema::hasColumn('purchases', 'bill_number')) {
            Schema::table('purchases', function (Blueprint $table) {
                $table->dropColumn('bill_number');
                $table->renameColumn('vendor_acc_no', 'vendor_acc_id');
                $table->dropColumn('payment');
                $table->dropColumn('note');
                $table->dropColumn('transaction_date');
            });
        }
    }
}
