<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateAddressAccurateTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('addresses')) {
            if (Schema::hasColumn('addresses', 'accurate_customer_id')) {
                Schema::table('addresses', function (Blueprint $table) {
                    $sm = Schema::getConnection()->getDoctrineSchemaManager();
                    $indexesFound = $sm->listTableIndexes('addresses');
                    if (array_key_exists("accurate_customer_id", $indexesFound)) {
                        $table->dropUnique(['accurate_customer_id']);
                    }
                    if (array_key_exists("accurate_customer_code", $indexesFound)) {
                        $table->dropUnique(['accurate_customer_code']);
                    }
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        //
    }
}
