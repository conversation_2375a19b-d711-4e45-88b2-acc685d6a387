<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateTableNotificationLog extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('notification_logs')) {
            if (!Schema::hasColumn('notification_logs', 'feedback_title')) {
                Schema::table('notification_logs', function (Blueprint $table) {
                    $table->string('feedback_title')->nullable();
                    $table->text('feedback_message')->nullable();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('notification_logs', 'feedback_title')) {
            Schema::table('notification_logs', function (Blueprint $table) {
                $table->dropColumn('feedback_title');
                $table->dropColumn('feedback_message');
            });
        }
    }
}
