<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class AddOptionsColumnOnCustomerTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('customers')) {
            if (!Schema::hasColumn('customers', 'options')) {
                Schema::table('customers', function (Blueprint $table) {
                    $table->json('options')->nullable();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('customers', 'options')) {
            Schema::table('customers', function (Blueprint $table) {
                $table->dropColumn('options');
            });
        }
    }
}
