<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class CreateTablesBelanjakantoronline extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {

        if (!Schema::hasColumn('customers', 'is_company')) {
            Schema::table('customers', function (Blueprint $table) {
                $table->boolean('is_company')->default(0);
                $table->string('cp_name')->nullable();
                $table->string('cp_phone')->nullable();
            });
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {

        if (Schema::hasColumn('customers', 'is_company')) {
            Schema::table('customers', function (Blueprint $table) {
                $table->dropColumn('is_company');
                $table->dropColumn('cp_name');
                $table->dropColumn('cp_phone');
            });
        }
    }
}