<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class EditOrderTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('orders')) {
            if (!Schema::hasColumn('orders', 'is_urgent')) {
                Schema::table('orders', function (Blueprint $table) {
                    $table->boolean('is_urgent')->default(0)->after('id');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('orders', 'is_urgent')) {
            Schema::table('orders', function (Blueprint $table) {
                $table->dropColumn('is_urgent');
            });
        }
    }
}