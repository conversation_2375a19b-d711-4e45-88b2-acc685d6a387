<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateAddressesTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('addresses')) {
            if (!Schema::hasColumn('addresses', 'is_need_marketing')) {
                Schema::table('addresses', function (Blueprint $table) {
                    $table->boolean('is_need_marketing')->default(0)->after('latlng');
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('addresses', 'is_need_marketing')) {
            Schema::table('addresses', function (Blueprint $table) {
                $table->dropColumn('is_need_marketing');
            });
        }
    }
}