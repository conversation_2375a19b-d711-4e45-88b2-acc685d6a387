<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateRoleAddressTable extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('addresses')) {
            if (!Schema::hasColumn('addresses', 'synced_at')) {
                Schema::table('addresses', function (Blueprint $table) {
                    $table->string('accurate_code')->unique()->nullable();
                    $table->timestamp('synced_at')->nullable();
                });
            }
        }

        if (Schema::hasTable('users')) {
            if (!Schema::hasColumn('users', 'is_partner')) {
                Schema::table('users', function (Blueprint $table) {
                    $table->boolean('is_partner')->default(0);
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('users', 'is_partner')) {
            Schema::table('users', function (Blueprint $table) {
                $table->dropColumn('is_partner');
            });
        }

        if (Schema::hasColumn('addresses', 'synced_at')) {
            Schema::table('addresses', function (Blueprint $table) {
                $table->dropColumn('accurate_code');
                $table->dropColumn('synced_at');
            });
        }
    }
}
