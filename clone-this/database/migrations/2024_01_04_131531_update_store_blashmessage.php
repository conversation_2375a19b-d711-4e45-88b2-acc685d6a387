<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

class UpdateStoreBlashmessage extends Migration
{
    /**
     * Run the migrations.
     *
     * @return void
     */
    public function up()
    {
        if (Schema::hasTable('stores')) {
            if (!Schema::hasColumn('stores', 'message_blash')) {
                Schema::table('stores', function (Blueprint $table) {
                    $table->text('message_blash')->nullable();
                });
            }
        }

        if (Schema::hasTable('customers')) {
            if (!Schema::hasColumn('customers', 'blash_updated_at')) {
                Schema::table('customers', function (Blueprint $table) {
                    $table->timestamp('blash_updated_at')->nullable();
                });
            }
        }
    }

    /**
     * Reverse the migrations.
     *
     * @return void
     */
    public function down()
    {
        if (Schema::hasColumn('customers', 'blash_updated_at')) {
            Schema::table('customers', function (Blueprint $table) {
                $table->dropColumn('blash_updated_at');
            });
        }

        if (Schema::hasColumn('stores', 'message_blash')) {
            Schema::table('stores', function (Blueprint $table) {
                $table->dropColumn('message_blash');
            });
        }
    }
}