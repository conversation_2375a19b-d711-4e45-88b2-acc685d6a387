<?php

namespace Database\Seeders;

use App\Models\CostCategory;
use Illuminate\Database\Seeder;

class OperatingCostTablesSeeder extends Seeder
{
    public function run()
    {
        $parent_categories = [
            [
                'title'      => 'SDM',
                'children' => [
                    [
                        'title' => 'GP',
                    ],
                    [
                        'title' => 'B-Bro',
                    ],
                    [
                        'title' => 'In-Job',
                    ],
                ]
            ],
            [
                'title'      => 'Armada',
                'children' => [
                    [
                        'title' => 'BBM',
                    ],
                    [
                        'title' => 'Ban',
                    ],
                    [
                        'title' => 'Tambal Ban',
                    ],
                    [
                        'title' => 'Oli',
                    ],
                    [
                        'title' => 'Perbaikan',
                    ],
                    [
                        'title' => 'STNK',
                    ],
                ]
            ],
            [
                'title'      => 'Bulanan Toko',
                'children' => [
                    [
                        'title' => 'Pulsa Listrik',
                    ],
                    [
                        'title' => 'Paket Data',
                    ],
                    [
                        'title' => 'Internet',
                    ],
                    [
                        'title' => 'Fotocopy & ATK',
                    ],
                    [
                        'title' => 'Sampah & Keamanan',
                    ],
                ]
            ],
        ];

        foreach ($parent_categories as $value) {
            $cc = CostCategory::create([
                'title' => $value['title'],
            ]);

            foreach ($value['children'] as $val) {
                CostCategory::create([
                    'title' => $val['title'],
                    'parent_id' => $cc->id,
                    'is_root' => false,
                ]);
            }
        }
    }
}