<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
// use Faker\Factory as Faker;
// use Intervention\Image\ImageManagerStatic as Image;
// use Illuminate\Support\Facades\File;
// use Illuminate\Filesystem\Filesystem;
use App\Models\Status;
use App\Models\Order;
use App\Models\OrderProduct;
use App\Models\Feedback;
// use App\Models\Deposit;
use App\Helper\Helper;

class OrdersTablesSeeder extends Seeder
{
    public function run()
    {
        $statuses = [
          [
            'code' => 'order',
            'name' => 'Job Bebas',
            'color' => 'black',
            'icon' => 'fas fa-circle'
          ],
          [
            'code' => 'assigned',
            'name' => 'Job Diambil',
            'color' => 'magenta',
            'icon' => 'fas fa-circle'
          ],
          [
            'code' => 'progress',
            'name' => 'Job Berjalan',
            'color' => 'cyan',
            'icon' => 'fas fa-circle'
          ],
          [
            'code' => 'completed',
            'name' => 'Job Terkirim',
            'color' => 'blue',
            'icon' => 'fas fa-circle'
          ],
          [
            'code' => 'canceled',
            'name' => 'Job Batal',
            'color' => 'red',
            'icon' => 'fas fa-circle'
          ],
          [
            'code' => 'paid',
            'name' => 'Job Selesai',
            'color' => 'limegreen',
            'icon' => 'fas fa-circle'
          ],
          [
            'code' => 'invoice-paid',
            'name' => 'Invoice Paid',
            'color' => 'green',
            'icon' => 'fas fa-circle'
          ],
        ];
        foreach ($statuses as $value) {
            Status::create($value);
        }

        $jsonUrl = 'database/data/orders.json';
        $jsonString = file_get_contents(base_path($jsonUrl));
        $orders = json_decode($jsonString, true);
        foreach ($orders as $value) {
            Order::create($value);
        }

        $order_products = [
          [
            'order_id' => 1,
            'product_id' => 1,
            'qty' => 1,
            'price' => 18500
          ],
          [
            'order_id' => 1,
            'product_id' => 2,
            'qty' => 2,
            'price' => 15000
          ],
          [
            'order_id' => 2,
            'product_id' => 3,
            'qty' => 2,
            'price' => 16000
          ],
          [
            'order_id' => 3,
            'product_id' => 4,
            'qty' => 1,
            'price' => 35000
          ],
          [
            'order_id' => 3,
            'product_id' => 5,
            'qty' => 1,
            'price' => 35000
          ],
          [
            'order_id' => 3,
            'product_id' => 6,
            'qty' => 1,
            'price' => 35000
          ],
          [
            'order_id' => 4,
            'product_id' => 7,
            'qty' => 2,
            'price' => 55000
          ],
          [
            'order_id' => 5,
            'product_id' => 9,
            'qty' => 4,
            'price' => 34000
          ],
        ];
        foreach ($order_products as $value) {
            OrderProduct::create($value);
        }

        foreach (Order::all() as $order) {
          $helper = new Helper;
          $helper->generateDepositHistory($order->customer_id);
        }

        $feedbacks = [
          [
            'order_id' => 4,
            'service_rating' => 5,
            'delivery_rating' => 5,
            'note' => "pengantaran sangat cepat dan memuaskan"
          ],
        ];
        foreach ($feedbacks as $value) {
            Feedback::create($value);
        }
    }
}