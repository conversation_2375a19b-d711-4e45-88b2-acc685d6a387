<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
// use Faker\Factory as Faker;
// use Intervention\Image\ImageManagerStatic as Image;
// use Illuminate\Support\Facades\File;
// use Illuminate\Filesystem\Filesystem;
use App\Models\Category;
use App\Models\Product;
// use App\Helper\Helper;

class PpobTablesSeeder extends Seeder
{
  public function run()
  {

    $category = Category::firstOrCreate(
      ['slug' => 'ppob'],
      ['name' => 'PPOB']
    );

    $ppob_products = [
      [
        'slug' => 'pln-token',
        'name' => 'PLN Token Listrik',
        'code' => 'PLN_TKN',
        'price' => 1500,
        'unit' => 'ppob',
      ],
      [
        'slug' => 'air-pdam',
        'name' => 'Air PDAM',
        'code' => 'AIR_PDAM',
        'price' => 1500,
        'unit' => 'ppob',
      ],
      [
        'slug' => 'emoney',
        'name' => 'Uang Elektronik eMoney',
        'code' => 'EMONEY',
        'price' => 1500,
        'unit' => 'ppob',
      ],
    ];
    foreach ($ppob_products as $value) {
      $product = Product::firstOrCreate(['code' => $value['code']], $value);
      $product->categories()->sync($category->id);
      $product->stores()->sync([1, 8]);
    }
  }
}