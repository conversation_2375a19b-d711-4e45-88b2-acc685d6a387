(window.webpackJsonp=window.webpackJsonp||[]).push([[3],{"+lRy":function(t,e){},0:function(t,e,n){n("9HjO"),n("+lRy"),t.exports=n("K0p+")},1:function(t,e){},"9HjO":function(t,e,n){"use strict";n.r(e);n("qxpy"),n("rmf8"),n("u8M4"),n("eOqu"),n("V4S9"),n("3PMH"),n("Si7d"),n("H0pb"),n("mjWP");var r=n("XuX8"),a=n.n(r),i=n("L2JU"),o=n("jE9Z"),s=n("rX62"),l=n.n(s),c=n("dV7z"),u=n("7pj7"),d=n.n(u),h=n("SJdT"),f=n.n(h),p=n("j4aL"),m=n.n(p),v=n("3M0M"),y=n.n(v),b=n("PtZe"),g=n.n(b),_=n("stYL"),w=n.n(_),S=n("IvRd"),C=n.n(S),x={fr:m.a,ru:y.a,es:g.a,en:w.a,de:C.a};var O={name:"SharpActionBar",props:{ready:{type:Boolean,default:!0},container:Boolean,rightClass:String}},k=n("KHd+"),j=Object(k.a)(O,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return t.ready?n("div",{staticClass:"SharpActionBar"},[n("div",{staticClass:"SharpActionBar__bar"},[n("div",{staticClass:"container"},[n("div",{staticClass:"row mx-n2"},[n("div",{staticClass:"col left px-2 my-1 my-sm-0"},[t._t("left")],2),t._v(" "),n("div",{staticClass:"col right px-2 my-1 my-sm-0",class:t.rightClass},[t._t("right")],2)])])]),t._v(" "),n("div",{class:{container:t.container}},[n("div",{staticClass:"row"},[n("div",{staticClass:"col"},[n("div",{staticClass:"SharpActionBar__extras"},[t._t("extras")],2)]),t._v(" "),t.$slots["extras-right"]?[n("div",{staticClass:"col-auto"},[n("div",{staticClass:"SharpActionBar__extras"},[t._t("extras-right")],2)])]:t._e()],2)])]):t._e()}),[],!1,null,null,null);j.options.__file="ActionBar.vue";var L=j.exports,P={props:{type:String,outline:Boolean,danger:Boolean,small:Boolean,href:String},computed:{classes:function(){return[this.type?"SharpButton--".concat(this.type):"",{"SharpButton--secondary":this.outline,"SharpButton--danger":this.danger,"SharpButton--sm":this.small}]},tag:function(){return this.href?"a":"button"}},methods:{focus:function(){this.$el.focus()}}},E=Object(k.a)(P,(function(){var t=this.$createElement;return(this._self._c||t)(this.tag,this._g({tag:"component",staticClass:"SharpButton",class:this.classes,attrs:{href:this.href}},this.$listeners),[this._t("default")],2)}),[],!1,null,null,null);E.options.__file="Button.vue";var D=E.exports,$={name:"SharpCard",props:{light:Boolean,hasClose:Boolean},computed:{cardClasses:function(){return{"SharpModule--light":this.light}}}},A=Object(k.a)($,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpModule",class:t.cardClasses},[n("div",{staticClass:"SharpModule__inner"},[n("div",{staticClass:"SharpModule__content"},[t._t("default")],2),t._v(" "),t.hasClose?[n("button",{staticClass:"SharpModule__close-button",attrs:{type:"button"},on:{click:function(e){return t.$emit("close-click")}}},[n("svg",{staticClass:"SharpModule__close-icon",attrs:{"aria-label":"close",width:"10",height:"10",viewBox:"0 0 10 10","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M9.8 8.6L8.4 10 5 6.4 1.4 10 0 8.6 3.6 5 .1 1.4 1.5 0 5 3.6 8.6 0 10 1.4 6.4 5z"}})])])]:t._e()],2)])}),[],!1,null,null,null);A.options.__file="Card.vue";var B=A.exports,I=function(){},F={props:{transitionClass:String},data:function(){return{active:0,width:0,enteringElm:null,leavingElm:null,afterEnterCallback:I}},computed:{enterActiveClass:function(){return"".concat(this.transitionClass," collapse-enter-active")}},methods:{increase:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:I;this.active=(this.active+1)%2,this.afterEnterCallback=t},beforeLeave:function(t){},leave:function(t){var e=this;setTimeout((function(){return t.style.width="".concat(e.width,"px")}),100)},afterLeave:function(t){t.style.width="",this.enteringElm.style.visibility="",this.afterEnterCallback()},beforeEnter:function(t){},enter:function(t){t.style.visibility="hidden",this.width=t.scrollWidth,this.enteringElm=t},afterEnter:function(t){}}},T=Object(k.a)(F,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("transition-group",{attrs:{"leave-active-class":t.transitionClass,"enter-active-class":t.enterActiveClass},on:{"before-enter":t.beforeEnter,enter:t.enter,"after-enter":t.afterEnter,leave:t.leave,"after-leave":t.afterLeave}},[n("div",{directives:[{name:"show",rawName:"v-show",value:0===t.active,expression:"active===0"}],key:"0",staticStyle:{position:"absolute"}},[t._t("frame-0",null,{next:t.increase})],2),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:1===t.active,expression:"active===1"}],key:"1",staticStyle:{position:"absolute"}},[t._t("frame-1",null,{next:t.increase})],2)])}),[],!1,null,null,null);T.options.__file="Collapse.vue";var M=T.exports,z={name:"SharpNavItem",props:{current:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},link:String},data:function(){return{}}},V=Object(k.a)(z,(function(){var t=this.$createElement,e=this._self._c||t;return e("li",{staticClass:"SharpLeftNav__item",class:{"SharpLeftNav__item--active":this.current,"SharpLeftNav__item--disabled":this.disabled},attrs:{role:"menuitem"}},[this.disabled?[e("span",{staticClass:"SharpLeftNav__item-link"},[this._t("default")],2)]:[e("a",{staticClass:"SharpLeftNav__item-link",attrs:{href:this.link}},[this._t("default")],2)]],2)}),[],!1,null,null,null);V.options.__file="NavItem.vue";var N=V.exports,R={name:"SharpCollapsibleItem",props:{label:String},data:function(){return{expanded:!1,ready:!1}},computed:{navItems:function(){return this.$slots.default.map((function(t){return t.componentInstance})).filter((function(t){return t&&t.$options.name===N.name}))}},methods:{toggle:function(){this.expanded=!this.expanded}},mounted:function(){var t=this;this.expanded=this.navItems.some((function(t){return t.current})),this.$nextTick((function(e){return t.ready=!0}))}},K=Object(k.a)(R,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("li",{directives:[{name:"show",rawName:"v-show",value:t.ready,expression:"ready"}],staticClass:"SharpLeftNav__item SharpLeftNav__item--has-children",class:{"SharpLeftNav__item--expanded":t.expanded},attrs:{tabindex:"0"},on:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.toggle(e)}}},[n("a",{staticClass:"SharpLeftNav__item-link",on:{click:t.toggle}},[t._t("label",[t._v("\n            "+t._s(t.label)+"\n        ")]),t._v(" "),n("div",{staticClass:"SharpLeftNav__item-icon"},[n("svg",{staticClass:"SharpLeftNav__icon",attrs:{width:"10",height:"5",viewBox:"0 0 10 5","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M10 0L5 5 0 0z"}})])])],2),t._v(" "),n("ul",{staticClass:"SharpLeftNav__list SharpLeftNav__list--nested",attrs:{role:"menu","aria-hidden":"true"}},[t._t("default")],2)])}),[],!1,null,null,null);K.options.__file="CollapsibleItem.vue";var q=K.exports,U=n("o0o1"),H=n.n(U),G={name:"SharpPagination",inheritAttrs:!1,components:{BPagination:n("JtJI").a},props:{totalRows:Number,perPage:Number,minPageEndButtons:{type:Number,default:0}},computed:{hideGotoEndButtons:function(){return Math.ceil(this.totalRows/Math.max(this.perPage,1))<this.minPageEndButtons}}},W=Object(k.a)(G,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("b-pagination",t._g(t._b({staticClass:"SharpPagination",attrs:{"total-rows":t.totalRows,"per-page":t.perPage,"hide-goto-end-buttons":t.hideGotoEndButtons},scopedSlots:t._u([{key:"first-text",fn:function(){return[n("i",{staticClass:"fas fa-angle-double-left",attrs:{"aria-hidden":"true"}})]},proxy:!0},{key:"prev-text",fn:function(){return[n("i",{staticClass:"fas fa-angle-left",attrs:{"aria-hidden":"true"}})]},proxy:!0},{key:"next-text",fn:function(){return[n("i",{staticClass:"fas fa-angle-right",attrs:{"aria-hidden":"true"}})]},proxy:!0},{key:"last-text",fn:function(){return[n("i",{staticClass:"fas fa-angle-double-right",attrs:{"aria-hidden":"true"}})]},proxy:!0}])},"b-pagination",t.$attrs,!1),t.$listeners))}),[],!1,null,null,null);W.options.__file="Pagination.vue";var Y=W.exports;function Q(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var X={props:{columns:Array,row:{type:Object,default:function(){return{}}},url:String,header:Boolean},computed:{hasLink:function(){return!!this.url}},methods:{colClasses:function(t){return["col-".concat(t.sizeXS),"col-md-".concat(t.size)].concat(Q(t.hideOnXS?["d-none d-md-flex"]:[]))}}},J=Object(k.a)(X,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpDataList__row container",class:{"SharpDataList__row--header":t.header,"SharpDataList__row--disabled":!t.header&&!t.hasLink}},[n("div",{staticClass:"SharpDataList__cols"},[n("div",{staticClass:"row mx-n2 mx-md-n3"},[t._l(t.columns,(function(e){return[n("div",{staticClass:"px-2 px-md-3",class:[t.header?"SharpDataList__th":"SharpDataList__td",t.colClasses(e)]},[t._t("cell",[e.html?[n("div",{staticClass:"SharpDataList__td-html-container",domProps:{innerHTML:t._s(t.row[e.key])}})]:[t._v("\n                            "+t._s(t.row[e.key])+"\n                        ")]],{row:t.row,column:e})],2)]}))],2),t._v(" "),t.hasLink?[n("a",{staticClass:"SharpDataList__row-link",attrs:{href:t.url}})]:t._e()],2),t._v(" "),t.$slots.append?[n("div",{staticClass:"SharpDataList__row-append align-self-center"},[t._t("append")],2)]:[n("div",{staticClass:"SharpDataList__row-spacer"})]],2)}),[],!1,null,null,null);J.options.__file="DataListRow.vue";var Z=J.exports,tt=n("FRYs"),et=n.n(tt);function nt(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function rt(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var at={components:{Pagination:Y,DataListRow:Z,Draggable:et.a},props:{items:Array,columns:Array,paginated:Boolean,totalCount:Number,pageSize:Number,page:Number,reorderActive:Boolean,sort:String,dir:String,hideHeader:Boolean},data:function(){return{reorderedItems:null,appendWidth:0}},watch:{reorderActive:function(t){this.handleReorderActiveChanged(t)}},computed:{hasPagination:function(){return!!this.paginated&&this.totalCount/this.pageSize>1},draggableOptions:function(){return{disabled:!this.reorderActive}},currentItems:function(){return this.reorderActive?this.reorderedItems:this.items},isEmpty:function(){return 0===(this.items||[]).length},styles:function(){return{"--append-width":this.appendWidth?"".concat(this.appendWidth,"px"):null}}},methods:{handleItemsChanged:function(t){this.reorderedItems=t,this.$emit("change",t)},handleSortClicked:function(t){this.$emit("sort-change",{prop:t,dir:this.sort===t&&"asc"===this.dir?"desc":"asc"})},handlePageChanged:function(t){this.$emit("page-change",t)},handleReorderActiveChanged:function(t){this.reorderedItems=t?rt(this.items):null},getAppendWidth:function(t){if(!t)return 0;var e=t.querySelector(".SharpDataList__row-append");return e?e.offsetWidth:0},updateLayout:function(){var t,e=this;return(t=H.a.mark((function t(){var n,r;return H.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.appendWidth=0,t.next=3,e.$nextTick();case 3:n=e.getAppendWidth(e.$refs.head),r=e.getAppendWidth(e.$refs.body),e.appendWidth=Math.max(n,r);case 6:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){nt(i,r,a,o,s,"next",t)}function s(t){nt(i,r,a,o,s,"throw",t)}o(void 0)}))})()}},mounted:function(){this.updateLayout(),window.addEventListener("resize",this.updateLayout)},destroyed:function(){window.removeEventListener("resize",this.updateLayout)}},it=Object(k.a)(at,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpDataList",class:{"SharpDataList--reordering":t.reorderActive},style:t.styles},[t.isEmpty?[n("div",{staticClass:"SharpDataList__empty p-3"},[t._t("empty")],2)]:[n("div",{staticClass:"SharpDataList__table SharpDataList__table--border"},[t.hideHeader?t._e():[n("div",{ref:"head",staticClass:"SharpDataList__thead"},[n("DataListRow",{attrs:{columns:t.columns,header:""},scopedSlots:t._u([{key:"cell",fn:function(e){var r=e.column;return[n("span",[t._v(t._s(r.label))]),t._v(" "),r.sortable?[n("svg",{staticClass:"SharpDataList__caret",class:{"SharpDataList__caret--selected":t.sort===r.key,"SharpDataList__caret--ascending":t.sort===r.key&&"asc"===t.dir},attrs:{width:"10",height:"5",viewBox:"0 0 10 5","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M10 0L5 5 0 0z"}})]),t._v(" "),n("a",{staticClass:"SharpDataList__sort-link",attrs:{href:""},on:{click:function(e){return e.preventDefault(),t.handleSortClicked(r.key)}}})]:t._e()]}},t.$slots["append-head"]?{key:"append",fn:function(){return[t._t("append-head")]},proxy:!0}:null],null,!0)})],1)],t._v(" "),n("div",{ref:"body",staticClass:"SharpDataList__tbody"},[n("Draggable",{attrs:{options:t.draggableOptions,value:t.reorderedItems},on:{input:t.handleItemsChanged}},[t._l(t.currentItems,(function(e){return[t._t("item",[n("DataListRow",{attrs:{columns:t.columns,row:e}})],{item:e})]}))],2)],1)],2)],t._v(" "),t.hasPagination?[n("div",{staticClass:"SharpDataList__pagination-container"},[n("Pagination",{attrs:{"total-rows":t.totalCount,"per-page":t.pageSize,"min-page-end-buttons":3,limit:7,value:t.page},on:{change:t.handlePageChanged}})],1)]:t._e()],2)}),[],!1,null,null,null);it.options.__file="DataList.vue";var ot=it.exports,st={name:"DropdownArrow"},lt=Object(k.a)(st,(function(){var t=this.$createElement,e=this._self._c||t;return e("svg",{attrs:{width:"10",height:"5",viewBox:"0 0 10 5","fill-rule":"evenodd"}},[e("path",{attrs:{d:"M10 0L5 5 0 0z"}})])}),[],!1,null,null,null);lt.options.__file="Arrow.vue";var ct=lt.exports;function ut(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}var dt={name:"SharpDropdown",components:{DropdownArrow:ct},provide:function(){return{$dropdown:this}},props:{text:String,showArrow:{type:Boolean,default:!0},disabled:Boolean},data:function(){return{opened:!1,isAbove:!1}},watch:{opened:function(t){var e=this;t&&this.$nextTick((function(t){return e.$emit("opened")}))}},methods:{handleFocus:function(){var t,e=this;return(t=H.a.mark((function t(){return H.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.disabled){t.next=2;break}return t.abrupt("return");case 2:return e.opened=!0,t.next=5,e.$nextTick();case 5:e.adjustPosition();case 6:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){ut(i,r,a,o,s,"next",t)}function s(t){ut(i,r,a,o,s,"throw",t)}o(void 0)}))})()},handleBlur:function(){this.opened=!1,this.isAbove=!1},toggleIfFocused:function(t){this.opened&&(this.$el.blur(),t.preventDefault())},adjustPosition:function(){var t=this.$refs.list.getBoundingClientRect().bottom;this.isAbove=t>window.innerHeight}}},ht=Object(k.a)(dt,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpDropdown",class:{"SharpDropdown--open":t.opened,"SharpDropdown--disabled":t.disabled,"SharpDropdown--above":t.isAbove,"SharpDropdown--no-arrow":!t.showArrow},attrs:{tabindex:t.disabled?-1:0},on:{focus:t.handleFocus,blur:t.handleBlur}},[n("div",{staticClass:"SharpDropdown__text",on:{mousedown:t.toggleIfFocused}},[t._t("text",[t._v(t._s(t.text))])],2),t._v(" "),t.showArrow?n("dropdown-arrow",{staticClass:"SharpDropdown__arrow"}):t._e(),t._v(" "),t.disabled?t._e():n("div",[n("ul",{ref:"list",staticClass:"SharpDropdown__list"},[t._t("default")],2)])],1)}),[],!1,null,null,null);ht.options.__file="Dropdown.vue";var ft=ht.exports,pt={name:"SharpDropdownItem",inject:["$dropdown"],props:{customClass:[String,Object]},methods:{handleClick:function(t){this.$emit("click",t),this.$dropdown.$el.blur()}}},mt=Object(k.a)(pt,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("li",{staticClass:"SharpDropdown__item"},[n("a",{staticClass:"SharpDropdown__link",class:t.customClass,attrs:{href:""},on:{mousedown:function(t){t.preventDefault()},click:function(e){return e.preventDefault(),t.handleClick(e)}}},[t._t("default")],2)])}),[],!1,null,null,null);mt.options.__file="DropdownItem.vue";var vt=mt.exports,yt={name:"SharpDropdownSeparator"},bt=Object(k.a)(yt,(function(){var t=this.$createElement;return(this._self._c||t)("li",{staticClass:"SharpDropdown__separator"})}),[],!1,null,null,null);bt.options.__file="DropdownSeparator.vue";var gt=bt.exports;function _t(t,e,n){var r=e.value;!n.children.length||n.children.every((function(t){return function(t){return t.text?0===t.text.trim().length:t.elm instanceof HTMLElement?"none"===t.elm.style.display:!t.tag}(t)}))?t.classList.add(r):t.classList.remove(r)}var wt=function(t,e){var n=e.value;n&&t.setAttribute("maxlength",n)};function St(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Ct={name:"SharpGrid",props:{rows:{type:Array,required:!0},rowClass:{type:Function,default:function(){return null}},colClass:{type:Function,default:function(){return null}}},methods:{colClasses:function(t){var e,n=t.size,r=t.sizeXS,a=!!n;return[(e={},St(e,"col-".concat(r),r),St(e,"col-md-".concat(n),a),St(e,"col-md",!a),e),this.colClass(t)]}},directives:{"empty-class":{inserted:_t,componentUpdated:_t}}},xt=Object(k.a)(Ct,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpGrid"},[t._l(t.rows,(function(e){return[n("div",{staticClass:"SharpGrid__row row",class:t.rowClass(e)},[t._l(e,(function(e){return[n("div",{directives:[{name:"empty-class",rawName:"v-empty-class",value:"SharpGrid__col--empty",expression:"'SharpGrid__col--empty'"}],staticClass:"SharpGrid__col",class:t.colClasses(e)},[t._t("default",null,{itemLayout:e})],2)]}))],2)]}))],2)}),[],!1,null,null,null);xt.options.__file="Grid.vue";var Ot=xt.exports,kt={name:"SharpItemVisual",props:{item:Object,iconClass:String,imageClass:String},computed:{iconClasses:function(){var t=[this.item.icon];return this.iconClass&&t.push(this.iconClass),t}}},jt=Object(k.a)(kt,(function(){var t=this.$createElement,e=this._self._c||t;return e("span",[this.item.icon?[e("i",{staticClass:"fa",class:this.iconClasses})]:this.item.image?[e("img",{attrs:{src:this.item.image}})]:this._e()],2)}),[],!1,null,null,null);jt.options.__file="ItemVisual.vue";var Lt=jt.exports,Pt={name:"SharpLoading",props:{visible:Boolean,small:Boolean},computed:{classes:function(){return{"SharpLoading--small":this.small}}}},Et=Object(k.a)(Pt,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",{directives:[{name:"show",rawName:"v-show",value:this.visible,expression:"visible"}],staticClass:"SharpLoading",class:this.classes},[e("div",{staticClass:"SharpLoading__content"},[e("svg",{staticClass:"SharpLoading__svg",attrs:{viewBox:"-75 -75 150 150"}},[e("circle",{attrs:{cx:"0",cy:"0",r:"37.5"}})])])])}),[],!1,null,null,null);Et.options.__file="Loading.vue";var Dt=Et.exports,$t={components:{Loading:Dt},props:{visible:Boolean}},At=Object(k.a)($t,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",{directives:[{name:"show",rawName:"v-show",value:this.visible,expression:"visible"}],staticClass:"SharpLoading__overlay"},[e("Loading",this._b({attrs:{visible:""}},"Loading",this.$attrs,!1))],1)}),[],!1,null,null,null);At.options.__file="LoadingOverlay.vue";var Bt=At.exports,It={methods:{getMergedIdentifier:function(t,e){for(var n=this.$parent;n&&null==n[t];)n=n.$parent;var r="";return n&&(r=n[t]+"."),"".concat(r).concat(e)}}},Ft={mixins:[It],props:{errorIdentifier:{type:[String,Number],required:!0}},computed:{mergedErrorIdentifier:function(){return this.getMergedIdentifier("mergedErrorIdentifier",this.errorIdentifier)}}},Tt={inject:["$field"],props:{focused:Boolean},watch:{focused:function(t){t&&this.$focusableElm.focus()}},data:function(){return{$focusableElm:null}},methods:{setFocusable:function(t){var e=this;this.$focusableElm=t,this.$field&&this.$focusableElm.addEventListener("blur",(function(t){e.$field.$emit("blur")}))}}},Mt=n("vDqi"),zt=n.n(Mt),Vt=n("eqyj"),Nt=n.n(Vt);var Rt,Kt=(Rt=document.head.querySelector("meta[name=base-url]"))?"/".concat(Rt.content):"/sharp",qt="".concat(Kt,"/api"),Ut="".concat(qt,"/upload"),Ht=n("Qyje"),Gt=n.n(Ht);function Wt(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Yt(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=[],r=!0,a=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(r=(o=s.next()).done)&&(n.push(o.value),!e||n.length!==e);r=!0);}catch(t){a=!0,i=t}finally{try{r||null==s.return||s.return()}finally{if(a)throw i}}return n}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function Qt(t){return Object.entries(t).reduce((function(t,e){var n=Yt(e,2);return function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Wt(t,e,n[e])}))}return t}({},t,Wt({},n[0],function(t){return Array.isArray(t)&&!t.length?null:t}(n[1])))}),t||{})}var Xt=zt.a.create({baseURL:qt,paramsSerializer:function(t){var e=Qt(t);return Gt.a.stringify(e,{strictNullHandling:!0})}});function Jt(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.params;return Xt.getUri({url:"".concat(qt,"/").concat(t.replace(/^\//,"")),params:n})}function Zt(t){return window.i18n[t]||""}var te=n("qLf8"),ee=n.n(te);function ne(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){re(t,e,n[e])}))}return t}function re(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var ae={caseSensitive:!1,include:[],minMatchCharLength:1,shouldSort:!0,tokenize:!0,matchAllTokens:!1,findAllMatches:!1,id:null,keys:["value"],location:0,threshold:0,distance:0,maxPatternLength:64};var ie=n("wd/R"),oe=n.n(ie);function se(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=[],r=!0,a=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(r=(o=s.next()).done)&&(n.push(o.value),!e||n.length!==e);r=!0);}catch(t){a=!0,i=t}finally{try{r||null==s.return||s.return()}finally{if(a)throw i}}return n}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function le(t){return Gt.a.stringify(t,{addQueryPrefix:!0,skipNulls:!0})}function ce(t){return Gt.a.parse(t,{ignoreQueryPrefix:!0,strictNullHandling:!0})}function ue(t){return"SHARP : ".concat(t)}function de(t){for(var e,n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];(e=console).log.apply(e,[ue(t)].concat(r))}function he(t){for(var e,n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];(e=console).error.apply(e,[ue(t)].concat(r))}var fe=n("zuWl"),pe=n.n(fe);function me(t){if("fr"===t)return{KB:"Ko",MB:"Mo"}}function ve(t){var e=(navigator.language||"").slice(0,2)||"en",n=Math.max(pe()(t,{output:"exponent"}),1),r=Math.max(t,128),a=pe()(r,{standard:"jedec",round:2,exponent:n,locale:!0,symbols:me(e)});return t<128?"< ".concat(a):a}var ye={state:{loading:!1,dialogs:[]},mutations:{setLoading:function(t,e){t.loading=!!e},setDialogs:function(t,e){t.dialogs=e}},getters:{isLoading:function(t){return!!t.loading}},actions:{setLoading:function(t,e){(0,t.commit)("setLoading",e)},setDialogs:function(t,e){(0,t.commit)("setDialogs",e)}}},be=null;function ge(){return be||(be=new i.a.Store(ye))}function _e(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){we(t,e,n[e])}))}return t}function we(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Se(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function Ce(t,e){if(null==t)return{};var n,r,a=function(t,e){if(null==t)return{};var n,r,a={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(a[n]=t[n]);return a}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(a[n]=t[n])}return a}var xe=0;function Oe(t){var e=t.text,n=t.okCallback,r=void 0===n?function(){}:n,a=t.okCloseOnly,i=t.isError,o=Ce(t,["text","okCallback","okCloseOnly","isError"]),s=xe++;ge().dispatch("setDialogs",[].concat(Se(ge().state.dialogs),[{id:s,props:_e({},o,{okOnly:a,noCloseOnBackdrop:a,noCloseOnEsc:a,visible:!0,isError:i}),okCallback:r,hiddenCallback:function(){ge().dispatch("setDialogs",ge().state.dialogs.filter((function(t){return t.id!==s})))},text:e}]))}function ke(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.title,r=Ce(e,["title"]);return Oe(_e({okCloseOnly:!0,text:t,title:n},r))}function je(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.title,r=Ce(e,["title"]);return Oe(_e({text:t,title:n},r))}function Le(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Pe(t,e,n[e])}))}return t}function Pe(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Ee={"x-access-from":"ui"};function De(t){var e=new URL(t,location.origin),n=Le(Le({},ce(e.search)),Ee);return e.pathname+le(n)}var $e=[],Ae=null;function Be(t){return!Ae||t?Ae=new o.a({mode:"history",routes:$e,base:"".concat(Kt,"/"),parseQuery:ce,stringifyQuery:le}):Ae}function Ie(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=e.normalized,r=void 0===n||n,a=Be().resolve(t),i=a.href;return r?De(i):i}function Fe(t){var e=t[t.length-2];return e?De(e.url):null}function Te(t){var e=new URL(t,location.origin).pathname.replace(new RegExp("^".concat(Kt)),"");return Be().resolve(e).route}function Me(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=[],r=!0,a=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(r=(o=s.next()).done)&&(n.push(o.value),!e||n.length!==e);r=!0);}catch(t){a=!0,i=t}finally{try{r||null==s.return||s.return()}finally{if(a)throw i}}return n}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}var ze=/^custom-(.+)$/;function Ve(t){return ze.test(t)}function Ne(t){var e=Me(t.match(ze)||[],2),n=(e[0],e[1]),r=n?a.a.options.components["SharpCustomField_".concat(n)]:null;return r||he("unknown custom field type '".concat(t,"', make sure you register it correctly (https://sharp.code16.fr/docs/guide/custom-form-fields.html#register-the-custom-field)")),r}function Re(t){return Ie({name:"form",params:{entityKey:t.entityKey,instanceId:t.instanceId}})}function Ke(t){return Ie({name:"show",params:{entityKey:t.entityKey,instanceId:t.instanceId}})}var qe={computed:{language:function(){return document.documentElement.lang}},methods:{l:Zt}},Ue=function(t){return{methods:{lSub:function(e){return Zt("".concat(t,".").concat(e))}}}},He=n("sEfC"),Ge=n.n(He),We=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"sm";return{data:function(){return{isViewportSmall:!1}},methods:{$_responsiveUpdate:function(){var t=this.$_testElm.offsetWidth;this.isViewportSmall=!!t}},created:function(){var e="viewport-down-".concat(t);this.$_testElm=document.getElementById(e),this.$_testElm||(this.$_testElm=document.createElement("div"),this.$_testElm.id=e,this.$_testElm.classList.add("d-".concat(t,"-none")),document.body.appendChild(this.$_testElm)),this.$_responsiveUpdate(),this.$_debouncedRespnsiveUpdate=Ge()(this.$_responsiveUpdate,300),window.addEventListener("resize",this.$_debouncedRespnsiveUpdate)},destroyed:function(){window.removeEventListener("resize",this.$_debouncedRespnsiveUpdate)}}},Ye={mixins:[It],props:{configIdentifier:{type:String,required:!0}},computed:{mergedConfigIdentifier:function(){return this.getMergedIdentifier("mergedConfigIdentifier",this.configIdentifier)}}};function Qe(t){return new Promise((function(e){var n=new FileReader;n.addEventListener("loadend",(function(){e(JSON.parse(n.result))})),n.readAsText(t)}))}function Xe(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}var Je={inject:["axiosInstance"],methods:{installInterceptors:function(){var t=this;this.axiosInstance.interceptors.request.use((function(e){return t.$store.dispatch("setLoading",!0),e}),(function(t){return Promise.reject(t)})),this.axiosInstance.interceptors.response.use((function(e){return t.$store.dispatch("setLoading",!1),e}),function(){var e,n=(e=H.a.mark((function e(n){var r,a,i,o,s,l;return H.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(r=n.response,a=n.config.method,t.$store.dispatch("setLoading",!1),!(r.data instanceof Blob&&"application/json"===r.data.type)){e.next=6;break}return e.next=5,Qe(r.data);case 5:r.data=e.sent;case 6:if(i=r.data,o=r.status,s=i.message||Zt("modals.".concat(o,".message"))||Zt("modals.error.message"),l=Zt("modals.".concat(o,".title"))||Zt("modals.error.title"),(404!==o||"get"!==a)&&422!==o){e.next=11;break}return e.abrupt("return",Promise.reject(n));case 11:return ke(s,401===o||419===o?{title:l,isError:!0,okCallback:function(){location.reload()}}:{title:l,isError:!0}),e.abrupt("return",Promise.reject(n));case 13:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(t){Xe(i,r,a,o,s,"next",t)}function s(t){Xe(i,r,a,o,s,"throw",t)}o(void 0)}))});return function(t){return n.apply(this,arguments)}}())}},created:function(){this.synchronous||(this.installInterceptors(),this.$store.dispatch("setLoading",!0))}},Ze={mixins:[Je],inject:["axiosInstance"],data:function(){return{data:null,layout:null}},methods:{get:function(){var t=this;return this.axiosInstance.get(this.apiPath,{params:this.apiParams}).then((function(e){return t.mount(e.data),t.handleNotifications(e.data),Promise.resolve(e)})).catch((function(t){return Promise.reject(t)}))},post:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this.apiPath,e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.data,n=arguments.length>2?arguments[2]:void 0;return this.axiosInstance.post(t,e,n).then((function(t){return Promise.resolve(t)})).catch((function(t){return Promise.reject(t)}))},showNotification:function(t){var e=t.level,n=t.title,r=t.message,a=t.autoHide;this.$notify({title:n,type:e,text:r,duration:a?4e3:-1})},handleNotifications:function(){var t=this,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};Array.isArray(e.notifications)&&setTimeout((function(){return e.notifications.forEach(t.showNotification)}),500)}}};function tn(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function en(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){tn(i,r,a,o,s,"next",t)}function s(t){tn(i,r,a,o,s,"throw",t)}o(void 0)}))}}function nn(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){rn(t,e,n[e])}))}return t}function rn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var an={data:function(){return{commandCurrentForm:null,commandViewContent:null}},methods:{transformCommandForm:function(t){return nn({},t,{layout:{tabs:[{columns:[{fields:t.layout}]}]}})},downloadCommandFile:function(t){var e=document.createElement("a");this.$el.appendChild(e),e.href=URL.createObjectURL(t.data),e.download=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t["content-disposition"];if(e&&e.includes("attachment")){var n=/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/,r=n.exec(e);if(null!=r&&r[1])return r[1].replace(/['"]/g,"")}return null}(t.headers),e.click()},handleCommandResponse:function(t){var e=this;return en(H.a.mark((function n(){var r;return H.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if("application/json"!==t.data.type){n.next=7;break}return n.next=3,Qe(t.data);case 3:r=n.sent,e.handleCommandActionRequested(r.action,r),n.next=8;break;case 7:e.downloadCommandFile(t);case 8:case"end":return n.stop()}}),n)})))()},postCommandForm:function(t){var e=this;return en(H.a.mark((function n(){var r,a;return H.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r=t.postFn,n.next=3,e.$refs.commandForm.submit({postFn:r});case 3:return a=n.sent,n.next=6,e.handleCommandResponse(a);case 6:e.commandCurrentForm=null;case 7:case"end":return n.stop()}}),n)})))()},showCommandForm:function(t,e){var n=this;return en(H.a.mark((function r(){var a,i,o,s;return H.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(a=e.postForm,i=e.getFormData,!t.fetch_initial_data){r.next=7;break}return r.next=4,i();case 4:r.t0=r.sent,r.next=8;break;case 7:r.t0={};case 8:o=r.t0,s=function(){return n.postCommandForm({postFn:a})},n.commandCurrentForm=n.transformCommandForm(nn({},t.form,{data:o})),n.$refs.commandForm.$on("submit",s),n.$refs.commandForm.$once("close",(function(){n.$refs.commandForm.$off("submit",s),n.commandCurrentForm=null}));case 13:case"end":return r.stop()}}),r)})))()},sendCommand:function(t,e){var n=this;return en(H.a.mark((function r(){var a,i,o,s,l,c;return H.a.wrap((function(r){for(;;)switch(r.prev=r.next){case 0:if(a=e.postCommand,i=e.getFormData,o=e.postForm,s=t.form,l=t.confirmation,!s){r.next=4;break}return r.abrupt("return",n.showCommandForm(t,{postForm:o,getFormData:i}));case 4:if(!l){r.next=7;break}return r.next=7,new Promise((function(t){je(l,{title:Zt("modals.command.confirm.title"),okCallback:t})}));case 7:return r.prev=7,r.next=10,a();case 10:return c=r.sent,r.next=13,n.handleCommandResponse(c);case 13:r.next=18;break;case 15:r.prev=15,r.t0=r.catch(7),console.error(r.t0);case 18:case"end":return r.stop()}}),r,null,[[7,15]])})))()},addCommandActionHandlers:function(t){this.commandHandlers=nn({},this.commandHandlers,{},t)},handleCommandActionRequested:function(t,e){var n=this.commandHandlers[t];n&&n(e)},handleReloadCommand:function(){this.init()},handleInfoCommand:function(t){ke(t.message,{title:Zt("modals.command.info.title")})},handleViewCommand:function(t){this.commandViewContent=t.html},handleLinkCommand:function(t){window.location.href=t.link},handleCommandViewPanelClosed:function(){this.commandViewContent=null}},created:function(){this.addCommandActionHandlers({reload:this.handleReloadCommand,info:this.handleInfoCommand,link:this.handleLinkCommand,view:this.handleViewCommand})},mounted:function(){this.$refs.commandForm||console.error("withCommands: CommandForm not found")}},on={name:"SharpModal",mixins:[qe],components:{BModal:n("aqyy").a},inheritAttrs:!1,props:{visible:Boolean,cancelTitle:String,title:String,okTitle:String,okOnly:Boolean,isError:Boolean,static:Boolean},methods:{show:function(){this.$refs.modal.show()},hide:function(){this.$refs.modal.hide()},handleVisiblityChanged:function(t){this.$emit("update:visible",t)}}},sn=Object(k.a)(on,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("b-modal",t._g(t._b({ref:"modal",attrs:{title:t.title,visible:t.visible,"cancel-title":t.cancelTitle||t.l("modals.cancel_button"),"ok-title":t.okTitle||t.l("modals.ok_button"),"ok-only":t.okOnly,static:t.static,"modal-class":["SharpModal",{"SharpModal--error":t.isError}],"no-enforce-focus":""},on:{change:t.handleVisiblityChanged},scopedSlots:t._u([{key:"modal-header",fn:function(){return[n("div",[n("h5",{staticClass:"SharpModal__heading"},[t._t("title",[t._v(t._s(t.title))])],2),t._v(" "),t.okOnly?t._e():n("button",{staticClass:"SharpModal__close",attrs:{type:"button"},on:{click:t.hide}},[n("svg",{staticClass:"SharpModal__close-icon",attrs:{width:"10",height:"10",viewBox:"0 0 10 10","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M9.8 8.6L8.4 10 5 6.4 1.4 10 0 8.6 3.6 5 .1 1.4 1.5 0 5 3.6 8.6 0 10 1.4 6.4 5z"}})])])])]},proxy:!0}],null,!0)},"b-modal",t.$attrs,!1),t.$listeners),[t._v(" "),t._t("default")],2)}),[],!1,null,null,null);sn.options.__file="Modal.vue";var ln=sn.exports,cn=n("jl8+"),un=n.n(cn);function dn(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var hn={name:"SharpMultiselect",functional:!0,render:function(t,e){var n=e.data,r=e.children,a=void 0===r?[]:r,i=e.slots;e.props.placeholder||(n.attrs.placeholder=Zt("form.multiselect.placeholder")),n.attrs.showPointer=!1;var o=i().caret;return t({extends:un.a,computed:{isSingleLabelVisible:function(){return 0===this.singleValue||un.a.computed.isSingleLabelVisible.call(this)}},mounted:function(){var t=this;this.$el.addEventListener("blur",(function(){return t.deactivate()}))}},n,[o?t("template",{slot:"caret"},o):t(ct,{class:"multiselect__select",slot:"caret"}),t("template",{slot:"maxElements"},Zt("form.multiselect.max_text"))].concat(dn(a)))}},fn=Object(k.a)(hn,void 0,void 0,!1,null,null,null);fn.options.__file="Multiselect.vue";var pn=fn.exports,mn={props:{value:String,placeholder:String,active:Boolean},data:function(){return{localActive:!!this.active}},watch:{active:function(t){this.localActive=t}},computed:{clearVisible:function(){return this.value&&this.value.length>0}},methods:{handleClearButtonClicked:function(){this.$emit("clear"),this.$emit("input",""),this.$refs.input.focus()},handleInput:function(t){this.$emit("input",t.target.value)},handleFocused:function(){this.localActive=!0,this.$emit("focus"),this.$emit("update:active",!0)},handleBlur:function(){this.localActive=!1,this.$emit("blur"),this.$emit("update:active",!1)},handleSubmitted:function(){this.$emit("submit")}}},vn=Object(k.a)(mn,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpActionBar__search SharpSearch SharpSearch--lg",class:{"SharpSearch--active":t.localActive},attrs:{role:"search"}},[n("form",{staticClass:"h-100",on:{submit:function(e){return e.preventDefault(),t.handleSubmitted(e)}}},[n("label",{staticClass:"SharpSearch__label",attrs:{id:"ab-search-label",for:"ab-search-input"}},[t._v(t._s(t.placeholder))]),t._v(" "),n("input",{ref:"input",staticClass:"SharpSearch__input w-100",attrs:{placeholder:t.placeholder,type:"text",id:"ab-search-input",role:"search","aria-labelledby":"ab-search-label"},domProps:{value:t.value},on:{input:t.handleInput,focus:t.handleFocused,blur:t.handleBlur}}),t._v(" "),n("svg",{staticClass:"SharpSearch__magnifier",attrs:{width:"16",height:"16",viewBox:"0 0 16 16","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M6 2c2.2 0 4 1.8 4 4s-1.8 4-4 4-4-1.8-4-4 1.8-4 4-4zm0-2C2.7 0 0 2.7 0 6s2.7 6 6 6 6-2.7 6-6-2.7-6-6-6zM16 13.8L13.8 16l-3.6-3.6 2.2-2.2z"}}),t._v(" "),n("path",{attrs:{d:"M16 13.8L13.8 16l-3.6-3.6 2.2-2.2z"}})]),t._v(" "),n("svg",{staticClass:"SharpSearch__close",class:{"SharpSearch__close--hidden":!t.clearVisible},attrs:{width:"16",height:"16",viewBox:"0 0 16 16","fill-rule":"evenodd"},on:{click:t.handleClearButtonClicked}},[n("path",{attrs:{d:"M8 0C3.6 0 0 3.6 0 8s3.6 8 8 8 8-3.6 8-8-3.6-8-8-8zm3.5 10.1l-1.4 1.4L8 9.4l-2.1 2.1-1.4-1.4L6.6 8 4.5 5.9l1.4-1.4L8 6.6l2.1-2.1 1.4 1.4L9.4 8l2.1 2.1z"}})])])])}),[],!1,null,null,null);vn.options.__file="Search.vue";var yn=vn.exports,bn={name:"SharpStateIcon",props:{color:{required:!0,type:String}},computed:{isClass:function(){return/^sharp_/.test(this.color)},className:function(){return this.isClass?this.color:null},style:function(){return this.isClass?null:{background:this.color}}}},gn=Object(k.a)(bn,(function(){var t=this.$createElement;return(this._self._c||t)("span",{staticClass:"StateIcon",class:this.className,style:this.style})}),[],!1,null,null,null);gn.options.__file="StateIcon.vue";var _n=gn.exports;function wn(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}var Sn={name:"SharpBTab",extends:n("YZAB").a,provide:function(){return{$tab:this}},data:function(){return{errors:{}}},computed:{hasError:function(){return Object.keys(this.errors).length>0}},watch:{localActive:{immediate:!0,handler:function(t){var e,n=this;return(e=H.a.mark((function e(){return H.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t){e.next=4;break}return e.next=3,n.$nextTick();case 3:n.$emit("active");case 4:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(t){wn(i,r,a,o,s,"next",t)}function s(t){wn(i,r,a,o,s,"throw",t)}o(void 0)}))})()}}},methods:{setError:function(t){this.$set(this.errors,t,!0)},clearError:function(t){this.$delete(this.errors,t)}},created:function(){var t=this;this.$on("error",(function(e){return t.setError(e)})),this.$on("clear",(function(e){return t.clearError(e)}))}},Cn=Object(k.a)(Sn,void 0,void 0,!1,null,null,null);Cn.options.__file="Tab.vue";var xn=Cn.exports,On=n("+QIf"),kn=n("WEOK");function jn(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}var Ln={name:"SharpBTabs",mixins:[We("sm")],extends:On.a,components:{BCollapse:kn.a,DropdownArrow:ct},data:function(){return{expanded:!0,hasNavOverflow:!1,extraNavGhostWidth:0}},watch:{currentTab:function(){this.hasNavOverflow&&(this.expanded=!1)}},computed:{collapseActivated:function(){return this.isViewportSmall&&(this.hasNavOverflow||this.tabs.length>2)},tabsHaveError:function(){return this.tabs.some((function(t){return t.hasError}))},dropdownButtonClasses:function(){return this.tabs[this.currentTab].hasError?"error-dot":this.tabsHaveError?"error-dot--partial":""},classes:function(){return{"SharpTabs--collapse":this.collapseActivated,"SharpTabs--nav-overflow":this.hasNavOverflow}}},methods:{linkClasses:function(t){return{"SharpTabs__nav-link--has-error":t.hasError,"SharpTabs__nav-link--active":t.localActive,"SharpTabs__nav-link--disabled":t.disabled}},handleCollapseClicked:function(){this.expanded=!this.expanded},layout:function(){var t,e=this;return(t=H.a.mark((function t(){var n;return H.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.hasNavOverflow=!1,t.next=3,e.$nextTick();case 3:n=e.$refs.nav,e.hasNavOverflow=n.scrollWidth>n.offsetWidth;case 5:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){jn(i,r,a,o,s,"next",t)}function s(t){jn(i,r,a,o,s,"throw",t)}o(void 0)}))})()}},mounted:function(){this.layout(),this.debouncedLayout=Ge()(this.layout,150),window.addEventListener("resize",this.debouncedLayout)},destroyed:function(){window.removeEventListener("resize",this.debouncedLayout)}},Pn=Object(k.a)(Ln,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpTabs",class:t.classes,attrs:{id:t.id||null}},[n("div",{staticClass:"SharpTabs__inner mb-3",class:{"m-sm-0":!t.hasNavOverflow}},[t.hasNavOverflow?[t._t("nav-prepend")]:t._e(),t._v(" "),n("button",{staticClass:"SharpTabs__collapse-btn SharpButton SharpButton--secondary mb-1",class:{"d-none":!t.hasNavOverflow},on:{click:t.handleCollapseClicked}},[t.tabs[t.currentTab]?[n("span",{class:t.dropdownButtonClasses},[t._v(t._s(t.tabs[t.currentTab].title))])]:t._e(),t._v(" "),n("DropdownArrow",{staticClass:"ml-1",style:t.expanded&&"transform: rotate(180deg)"})],2),t._v(" "),n("b-collapse",{class:{"d-block":!t.hasNavOverflow},attrs:{id:"tabs",visible:t.expanded}},[n("div",{ref:"nav",staticClass:"SharpTabs__nav mb-0 mb-sm-3",attrs:{role:"tablist","aria-setsize":t.tabs.length,"aria-posinset":t.currentTab+1}},[t.hasNavOverflow?t._e():[t._t("nav-prepend")],t._v(" "),t._l(t.tabs,(function(e){return[n("a",{staticClass:"SharpTabs__nav-link",class:t.linkClasses(e),attrs:{href:e.href,role:"tab","aria-selected":e.localActive?"true":"false","aria-controls":e.id||null,id:e.controlledBy||null},domProps:{innerHTML:t._s(e.title)},on:{click:function(n){return n.preventDefault(),n.stopPropagation(),t.clickTab(e)},keydown:[function(n){return!n.type.indexOf("key")&&t._k(n.keyCode,"space",32,n.key,[" ","Spacebar"])?null:(n.preventDefault(),n.stopPropagation(),t.clickTab(e))},function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"left",37,e.key,["Left","ArrowLeft"])||"button"in e&&0!==e.button?null:t.previousTab(e)},function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"up",38,e.key,["Up","ArrowUp"])?null:t.previousTab(e)},function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"right",39,e.key,["Right","ArrowRight"])||"button"in e&&2!==e.button?null:t.nextTab(e)},function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"down",40,e.key,["Down","ArrowDown"])?null:t.nextTab(e)}]}})]}))],2)])],2),t._v(" "),n("div",{ref:"tabsContainer",staticClass:"tab-content"},[t._t("default"),t._v(" "),t.tabs&&t.tabs.length?t._e():t._t("empty")],2)])}),[],!1,null,null,null);Pn.options.__file="Tabs.vue";var En=Pn.exports,Dn={name:"SharpTabbedLayout",props:{layout:Object},provide:function(){if(!this.showTabs)return{$tab:!1}},components:{Tabs:En,Tab:xn},computed:{showTabs:function(){return this.layout.tabbed&&this.layout.tabs.length>1}}},$n=Object(k.a)(Dn,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpTabbedLayout"},[t.showTabs?[n("Tabs",{scopedSlots:t._u([{key:"nav-prepend",fn:function(){return[t._t("nav-prepend")]},proxy:!0}],null,!0)},[t._v(" "),t._l(t.layout.tabs,(function(e,r){return[n("Tab",{key:"tab-"+r,attrs:{title:e.title}},[t._t("default",null,{tab:e})],2)]}))],2)]:[n("div",{staticClass:"mb-3"},[t._t("nav-prepend")],2),t._v(" "),t._l(t.layout.tabs,(function(e){return[t._t("default",null,{tab:e})]}))]],2)}),[],!1,null,null,null);$n.options.__file="TabbedLayout.vue";var An=$n.exports;function Bn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var In={name:"SharpTemplate",components:{RenderedTemplate:{functional:!0,props:{name:String,template:String,templateProps:Array,templateData:Object},render:function(t,e){var n=e.props,r=e.data,a=n.name,i=n.template,o=n.templateProps,s=n.templateData;return t({name:"SharpTemplate".concat(a),template:"<div>".concat(i,"</div>"),props:o},function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Bn(t,e,n[e])}))}return t}({},r,{props:s}))}}},props:{name:String,templateData:Object,template:String},computed:{templateProps:function(){return Object.keys(this.templateData||{})}}},Fn=Object(k.a)(In,(function(){var t=this.$createElement;return(this._self._c||t)("rendered-template",{staticClass:"SharpTemplate",attrs:{name:this.name,template:this.template,"template-data":this.templateData,"template-props":this.templateProps}})}),[],!1,null,null,null);Fn.options.__file="TemplateRenderer.vue";var Tn=Fn.exports,Mn={props:{name:String}},zn=(n("ynHM"),Object(k.a)(Mn,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"unknown-field"},[e("span",[this._v("Unknown field «"+this._s(this.name)+"»")])])}),[],!1,null,"d6c36028",null));zn.options.__file="UnknownField.vue";var Vn=zn.exports;function Nn(t){var e,n,r,a=t.url,i=t.method,o=t.locale,s=t.searchAttribute,l=t.query,c=t.dataWrapper,u=t.fieldKey,d="get"===i.toLowerCase(),h=(r=l,(n=s)in(e={locale:o})?Object.defineProperty(e,n,{value:r,enumerable:!0,configurable:!0,writable:!0}):e[n]=r,e);return zt()({url:a,method:i,params:d?h:void 0,data:d?void 0:h}).then((function(t){var e,n,r;return function(t){var e=t.results,n=t.dataWrapper,r=t.fieldKey,a=t.url;if(e){if(n&&!e.hasOwnProperty(n))return he("Autocomplete (".concat(r,'): dataWrapper "').concat(n,'" seems to be invalid :')),he('- search url "'.concat(a,'"')),he("- results",e),!1;if(!n&&!Array.isArray(e))return he("Autocomplete (".concat(r,"): search results response is not an array, please use setDataWrapper() if results are wrapped inside an object (https://sharp.code16.fr/docs/guide/form-fields/autocomplete.html#setdatawrapper)")),he('- search url "'.concat(a,'"')),he("- response",e),!1}return!0}({results:t.data,dataWrapper:c,fieldKey:u,url:a})?c?null!==(e=null===(n=t.data)||void 0===n?void 0:n[c])&&void 0!==e?e:[]:null!==(r=t.data)&&void 0!==r?r:[]:[]}))}var Rn={inject:{$form:{default:function(){return new a.a}}},props:{locale:String,localized:Boolean},computed:{locales:function(){return this.$form.locales},isLocalized:function(){return this.$form.localized&&this.localized}}};function Kn(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){qn(t,e,n[e])}))}return t}function qn(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Un(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=[],r=!0,a=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(r=(o=s.next()).done)&&(n.push(o.value),!e||n.length!==e);r=!0);}catch(t){a=!0,i=t}finally{try{r||null==s.return||s.return()}finally{if(a)throw i}}return n}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function Hn(t){return(Hn="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}var Gn=["text","textarea"];function Wn(t){return Gn.includes(t.type)}function Yn(t){var e,n=Kn({},t.localeObject,qn({},t.locale,t.value));return e=n,Object.entries(e).every((function(t){var e=Un(t,2);return e[0],function(t){return null===t||""===t}(e[1])}))?null:n}function Qn(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=[],r=!0,a=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(r=(o=s.next()).done)&&(n.push(o.value),!e||n.length!==e);r=!0);}catch(t){a=!0,i=t}finally{try{r||null==s.return||s.return()}finally{if(a)throw i}}return n}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}var Xn={mixins:[Rn],computed:{localizedSearchKeys:function(){var t=this;return this.localized?this.searchKeys.map((function(e){var n=e;return t.localizedDataKeys.includes(e)&&(n+=".".concat(t.locale)),n})):this.searchKeys},localizedDataKeys:function(){var t=this;return Array.isArray(this.localValues)&&this.localValues.length?Object.keys(this.localValues[0]).filter((function(e){return t.isLocaleObject(t.localValues[0][e])})):[]}},methods:{isLocaleObject:function(t){return this.locales&&function(t,e){return!(!t||"object"!==Hn(t))&&e.every((function(e){return e in t}))}(t,this.locales)},localizedTemplateData:function(t){var e=this;return this.localized?Object.entries(t).reduce((function(t,n){var r=Qn(n,2),a=r[0],i=r[1];return t[a]=e.isLocaleObject(i)?i[e.locale]:i,t}),{}):t}}},Jn=n("mwIZ"),Zn=n.n(Jn),tr=n("7wO+"),er=n.n(tr);function nr(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){rr(t,e,n[e])}))}return t}function rr(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function ar(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var ir=/{{([\s\S]+?)}}/g;function or(t,e){return(t||[]).find((function(t){return t.name===e}))}function sr(t){var e=t.dynamicOptions,n=void 0===e?{}:e,r=t.attributeValue;return"map"===n.type?n.path:"template"===n.type?ar(r.matchAll(ir)).map((function(t){return t[1].trim()})):[]}function lr(t){var e=t.dynamicOptions,n=t.attributeValue,r=t.contextData,a=t.contextSources;return"map"===e.type?function(t){var e=t.map,n=t.path,r=t.contextData,a=n.map((function(t){return r[t]}));return Zn()(e,a)}({map:n,path:e.path,contextData:r}):"template"===e.type?function(t){var e=t.template,n=t.sources,r=t.contextData,a=er()(e,{interpolate:ir,evaluate:!1,escape:!1}),i=(n||[]).reduce((function(t,e){return nr({},t,rr({},e,null))}),{});return a(nr({},i,{},r))}({template:n,sources:a,contextData:r}):n}function cr(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){ur(t,e,n[e])}))}return t}function ur(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function dr(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=[],r=!0,a=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(r=(o=s.next()).done)&&(n.push(o.value),!e||n.length!==e);r=!0);}catch(t){a=!0,i=t}finally{try{r||null==s.return||s.return()}finally{if(a)throw i}}return n}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function hr(t,e,n){var r=Object.entries(t||{}).reduce((function(t,r){var a=dr(r,2),i=a[0],o=function(t,e,n){var r=n.dynamicAttributes,a=n.contextData,i=or(r,t),o=sr({dynamicOptions:i,attributeValue:e});return i?function(t){var e=t.contextSources,n=t.contextData;return e.filter((function(t){var e=n[t];return null==e||""===e}))}({contextSources:o,contextData:a}).length>0?{isEmpty:null==i.default,value:i.default}:{value:lr({dynamicOptions:i,attributeValue:e,contextData:a,contextSources:o})}:{value:e}}(i,a[1],{dynamicAttributes:e,contextData:n}),s=t.resolvedEmptyAttributes||[];return o.isEmpty&&s.push(i),cr({},t,{resolvedEmptyAttributes:s,attributes:cr({},t.attributes,ur({},i,o.value))})}),{});return{attributes:r.attributes,resolvedEmptyAttributes:r.resolvedEmptyAttributes}}function fr(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function pr(t,e,n){return Object.values(t).filter((function(t){return n=e,r=t.dynamicAttributes,a=t,(r||[]).some((function(t){return sr({dynamicOptions:t,attributeValue:a[t.name]}).includes(n)}));var n,r,a})).reduce((function(t,e){return function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){fr(t,e,n[e])}))}return t}({},t,fr({},e.key,n?n(e,null):null))}),{})}function mr(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.dependantAttributes,a=t.$attrs.dynamicAttributes,i=(r||[]).some((function(t){return or(a,t)}));i?t.$nextTick(e):e()}function vr(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){yr(t,e,n[e])}))}return t}function yr(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function br(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=[],r=!0,a=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(r=(o=s.next()).done)&&(n.push(o.value),!e||n.length!==e);r=!0);}catch(t){a=!0,i=t}finally{try{r||null==s.return||s.return()}finally{if(a)throw i}}return n}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function gr(t,e){var n=function(t,e){return Object.entries(t).reduce((function(t,n){var r=br(n,2),a=r[0],i=r[1];return vr({},t,yr({},a,function(t,e){return"autocomplete"===t&&e?e.id:e}(e[a].type,i)))}),{})}(e,t);return Object.entries(t).reduce((function(t,e){var r=br(e,2),a=r[0],i=r[1],o=hr(i,i.dynamicAttributes,n),s=o.attributes,l=o.resolvedEmptyAttributes;return vr({},t,yr({},a,vr({},s,{readOnly:s.readOnly||l.length>0})))}),{})}function _r(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}var wr={name:"SharpAutocomplete",components:{Multiselect:un.a,TemplateRenderer:Tn,Loading:Dt},mixins:[qe,Xn],props:{fieldKey:String,value:[String,Number,Object,Array],mode:String,localValues:{type:Array,default:function(){return[]}},placeholder:{type:String,default:function(){return Zt("form.multiselect.placeholder")}},remoteEndpoint:String,remoteMethod:String,remoteSearchAttribute:{type:String,default:"query"},itemIdAttribute:{type:String,default:"id"},searchMinChars:{type:Number,default:1},searchKeys:{type:Array,default:function(){return["value"]}},dataWrapper:String,readOnly:Boolean,listItemTemplate:String,resultItemTemplate:String,noResultItem:Boolean,multiple:Boolean,hideSelected:Boolean,searchable:{type:Boolean,default:!0},allowEmpty:{type:Boolean,default:!0},clearOnSelect:Boolean,preserveSearch:{type:Boolean,default:!0},showPointer:{type:Boolean,default:!0},dynamicAttributes:Array},data:function(){return{ready:!1,query:"",suggestions:this.localValues,opened:!1,isLoading:!1}},watch:{localValues:function(){this.isRemote||this.updateLocalSuggestions(this.query)}},computed:{isRemote:function(){return"remote"===this.mode},hideDropdown:function(){return this.isQueryTooShort},isQueryTooShort:function(){return this.isRemote&&this.query.length<this.searchMinChars},clearButtonVisible:function(){return!!this.value&&!this.opened},classes:function(){return[{"SharpAutocomplete--remote":this.isRemote},{"SharpAutocomplete--disabled":this.readOnly}]},overlayVisible:function(){var t=!!this.fieldKey;return this.value&&t}},methods:{updateSuggestions:function(t){this.query=t,this.isQueryTooShort||(this.isRemote?(this.isLoading=!0,this.updateRemoteSuggestions(t)):this.updateLocalSuggestions(t))},updateLocalSuggestions:function(t){this.suggestions=t.length>=this.searchMinChars?function(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.searchKeys,a=new ee.a(t,ne({},ae,{keys:r}));return a.search(e)}(this.localValues,t,{searchKeys:this.searchKeys}):this.localValues},updateRemoteSuggestions:Ge()((function(t){var e=this;return Nn({url:this.remoteEndpoint,method:this.remoteMethod,locale:this.locale,searchAttribute:this.remoteSearchAttribute,dataWrapper:this.dataWrapper,fieldKey:this.fieldKey,query:t}).then((function(t){e.suggestions=t})).finally((function(){e.isLoading=!1}))}),200),handleSelect:function(t){this.$emit("input",t)},handleDropdownClose:function(){this.opened=!1,this.$emit("close")},handleDropdownOpen:function(){this.opened=!0,this.$emit("open")},handleClearButtonClicked:function(){var t=this;this.$emit("input",null),this.$nextTick((function(){t.$refs.multiselect.activate()}))},itemMatchValue:function(t){return t[this.itemIdAttribute]==this.value[this.itemIdAttribute]},findLocalValue:function(){return this.value&&null!=this.value[this.itemIdAttribute]?this.localValues.some(this.itemMatchValue)?this.localValues.find(this.itemMatchValue):(error("Autocomplete (key: ".concat(this.fieldKey,") can't find local value matching : ").concat(JSON.stringify(this.value))),null):null},setDefault:function(){var t,e=this;return(t=H.a.mark((function t(){return H.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return e.$emit("input",e.findLocalValue(),{force:!0}),t.next=3,e.$nextTick();case 3:e.ready=!0;case 4:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){_r(i,r,a,o,s,"next",t)}function s(t){_r(i,r,a,o,s,"throw",t)}o(void 0)}))})()}},created:function(){"local"!==this.mode||this.searchKeys||function(t){for(var e,n=arguments.length,r=new Array(n>1?n-1:0),a=1;a<n;a++)r[a-1]=arguments[a];(e=console).warn.apply(e,[ue(t)].concat(r))}("Autocomplete (key: ".concat(this.fieldKey,") has local mode but no searchKeys, default set to ['value']")),this.isRemote?this.ready=!0:mr(this,this.setDefault,{dependantAttributes:["localValues"]})}},Sr=Object(k.a)(wr,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpAutocomplete",class:t.classes},[t.ready?[n("Multiselect",{ref:"multiselect",staticClass:"SharpAutocomplete__multiselect",class:{"SharpAutocomplete__multiselect--hide-dropdown":t.hideDropdown},attrs:{value:t.value,options:t.suggestions,"track-by":t.itemIdAttribute,"internal-search":!1,placeholder:t.placeholder,loading:t.isLoading,multiple:t.multiple,disabled:t.readOnly,"hide-selected":t.hideSelected,"allow-empty":t.allowEmpty,"preserve-search":t.preserveSearch,"show-pointer":t.showPointer,searchable:t.searchable},on:{"search-change":function(e){return t.updateSuggestions(e)},select:t.handleSelect,input:function(e){return t.$emit("multiselect-input",e)},close:t.handleDropdownClose,open:t.handleDropdownOpen},scopedSlots:t._u([{key:"clear",fn:function(){return[t.clearButtonVisible?[n("button",{staticClass:"SharpAutocomplete__result-item__close-button",attrs:{type:"button"},on:{click:t.handleClearButtonClicked}},[n("svg",{staticClass:"SharpAutocomplete__result-item__close-icon",attrs:{"aria-label":"close",width:"10",height:"10",viewBox:"0 0 10 10","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M9.8 8.6L8.4 10 5 6.4 1.4 10 0 8.6 3.6 5 .1 1.4 1.5 0 5 3.6 8.6 0 10 1.4 6.4 5z"}})])])]:t._e()]},proxy:!0},{key:"singleLabel",fn:function(e){var r=e.option;return[n("TemplateRenderer",{attrs:{name:"ResultItem",template:t.resultItemTemplate,"template-data":t.localizedTemplateData(r)}})]}},{key:"option",fn:function(e){var r=e.option;return[n("TemplateRenderer",{attrs:{name:"ListItem",template:t.listItemTemplate,"template-data":t.localizedTemplateData(r)}})]}},{key:"loading",fn:function(){return[n("Loading",{attrs:{visible:t.isLoading,small:""}})]},proxy:!0},{key:"noResult",fn:function(){return[t._v("\n                "+t._s(t.l("form.autocomplete.no_results_text"))+"\n            ")]},proxy:!0}],null,!1,1321374210)}),t._v(" "),t.overlayVisible?[n("div",{staticClass:"SharpAutocomplete__overlay multiselect"},[n("div",{staticClass:"multiselect__tags"},[n("TemplateRenderer",{attrs:{name:"ResultItem",template:t.resultItemTemplate,"template-data":t.localizedTemplateData(t.value)}})],1)])]:t._e()]:t._e()],2)}),[],!1,null,null,null);Sr.options.__file="Autocomplete.vue";var Cr=Sr.exports,xr={name:"SharpTextarea",props:{value:{type:String},placeholder:String,readOnly:Boolean,maxLength:Number,rows:Number},data:function(){return{}},methods:{handleInput:function(t){this.$emit("input",t.target.value)}},directives:{maxlength:wt}},Or=Object(k.a)(xr,(function(){var t=this,e=t.$createElement;return(t._self._c||e)("textarea",{directives:[{name:"maxlength",rawName:"v-maxlength",value:t.maxLength,expression:"maxLength"}],staticClass:"SharpTextarea",attrs:{rows:t.rows,placeholder:t.placeholder,disabled:t.readOnly},domProps:{value:t.value},on:{input:t.handleInput}},[t._v(t._s(t.value))])}),[],!1,null,null,null);Or.options.__file="Textarea.vue";var kr=Or.exports,jr={name:"SharpText",mixins:[Tt],props:{value:[String,Number],placeholder:String,readOnly:Boolean,maxLength:Number,inputType:{type:String,default:"text"}},data:function(){return{}},methods:{handleInput:function(t){this.$emit("input",t.target.value)}},mounted:function(){this.setFocusable(this.$refs.input)},directives:{maxlength:wt}},Lr=Object(k.a)(jr,(function(){var t=this.$createElement;return(this._self._c||t)("input",{directives:[{name:"maxlength",rawName:"v-maxlength",value:this.maxLength,expression:"maxLength"}],ref:"input",staticClass:"SharpText",attrs:{type:this.inputType,placeholder:this.placeholder,disabled:this.readOnly},domProps:{value:this.value},on:{input:this.handleInput}})}),[],!1,null,null,null);Lr.options.__file="Text.vue";var Pr=Lr.exports,Er=n("6/sb"),Dr=n.n(Er),$r=n("d90G"),Ar=n("lcPr"),Br=n.n(Ar),Ir={props:{compactThumbnail:Boolean},computed:{modifiers:function(){return{compacted:this.compactThumbnail}}}},Fr={props:{modifiers:{type:Object,default:function(){}}},computed:{modifiersClasses:function(){return{"SharpUpload--compacted":this.modifiers.compacted}}}};function Tr(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Mr(t,e,n[e])}))}return t}function Mr(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var zr={name:"SharpVueClip",extends:$r.a,components:{Modal:ln,VueCropper:Br.a},inject:["axiosInstance","$form","$field"],mixins:[qe,Fr],props:{downloadId:String,pendingKey:String,ratioX:Number,ratioY:Number,value:Object,croppableFileTypes:Array,readOnly:Boolean},data:function(){return{showEditModal:!1,croppedImg:null,resized:!1,allowCrop:!1,isNew:!this.value,canDownload:!!this.value}},watch:{value:function(t){t||(this.files=[])},"file.status":function(t){t in this.statusFunction&&this[this.statusFunction[t]]()}},computed:{file:function(){return this.files[0]},originalImageSrc:function(){return this.file&&(this.file.thumbnail||this.file.dataUrl)},imageSrc:function(){return this.croppedImg||this.originalImageSrc},size:function(){return null!=this.file.size?ve(this.file.size):null},operationFinished:function(){return{crop:this.hasInitialCrop?!!this.croppedImg:null}},operations:function(){return Object.keys(this.operationFinished)},activeOperationsCount:function(){var t=this;return this.operations.filter((function(e){return null!==t.operationFinished[e]})).length},operationFinishedCount:function(){var t=this;return this.operations.filter((function(e){return t.operationFinished[e]})).length},progress:function(){var t=this.file?this.file.progress:100,e=1-.05*(this.activeOperationsCount-this.operationFinishedCount);return Math.floor(t)*e},inProgress:function(){return this.file&&"exist"!==this.file.status&&this.progress<100},statusFunction:function(){return{error:"onStatusError",success:"onStatusSuccess",added:"onStatusAdded"}},fileName:function(){var t=this.file.name.split("/");return t.length?t[t.length-1]:""},fileExtension:function(){var t=this.fileName.split(".").pop();return t?".".concat(t):null},downloadUrl:function(){return t={entityKey:this.$form.entityKey,instanceId:this.$form.instanceId,fieldKey:this.downloadId,fileName:this.fileName},e=t.entityKey,n=t.instanceId,r=t.fieldKey,a=t.fileName,Jt("form/download/".concat(r,"/").concat(e,"/").concat(n),{params:{fileName:a}});var t,e,n,r,a},showThumbnail:function(){return this.imageSrc},hasInitialCrop:function(){return!(!this.ratioX||!this.ratioY)&&this.isCroppable},isCroppable:function(){return!this.croppableFileTypes||this.croppableFileTypes.includes(this.fileExtension)}},methods:{setPending:function(t){var e;null===(e=this.$form)||void 0===e||e.setUploading(this.pendingKey,t)},onStatusAdded:function(){this.$emit("reset"),this.setPending(!0)},onStatusError:function(){var t=this.file.errorMessage;this.remove(),this.$emit("error",t)},onStatusSuccess:function(){var t=this,e={};try{e=JSON.parse(this.file.xhrResponse.responseText)}catch(t){console.log(t)}e.uploaded=!0,this.$emit("success",e),this.$emit("input",e),this.setPending(!1),this.allowCrop=!0,this.$nextTick((function(e){t.isCropperReady()&&t.onCropperReady()}))},remove:function(){this.canDownload=!1,this.removeFile(this.file),this.files.splice(0,1),this.setPending(!1),this.resetEdit(),this.$emit("input",null),this.$emit("reset"),this.$emit("removed")},resetEdit:function(){this.croppedImg=null,this.resized=!1},onEditButtonClick:function(){this.$emit("active"),this.showEditModal=!0,this.allowCrop=!0},handleImageLoaded:function(){this.isNew&&this.$emit("image-updated")},onEditModalShown:function(){var t=this;this.resized||this.$nextTick((function(){var e=t.$refs.cropper.cropper;e.resize(),e.reset(),t.resized=!0}))},onEditModalHidden:function(){var t=this;this.$emit("inactive"),setTimeout((function(){return t.$refs.cropper.cropper.reset()}),300)},onEditModalOk:function(){this.updateCroppedImage(),this.updateCropData()},isCropperReady:function(){return this.$refs.cropper&&this.$refs.cropper.cropper.ready},onCropperReady:function(){this.hasInitialCrop&&(this.updateCroppedImage(),this.updateCropData())},updateCropData:function(){var t=this.getCropData(),e=this.getImageData(),n=e.naturalWidth,r=e.naturalHeight;Math.abs(t.rotate)%180&&(n=e.naturalHeight,r=e.naturalWidth);var a={width:t.width/n,height:t.height/r,x:t.x/n,y:t.y/r,rotate:-1*t.rotate};if(this.allowCrop){var i=Tr({},this.value,{cropData:a});this.$emit("input",i),this.$emit("updated",i)}},updateCroppedImage:function(){this.allowCrop&&(this.isNew=!0,this.croppedImg=this.$refs.cropper.getCroppedCanvas().toDataURL())},getCropData:function(){return this.$refs.cropper.getData(!0)},getImageData:function(){return this.$refs.cropper.getImageData()},rotate:function(t){!function(t,e){var n=t.getCropBoxData(),r=t.getContainerData();n.width=2,n.height=2,n.top=0;var a=r.width/2-1;n.left=a,t.setCropBoxData(n),t.rotate(e);var i=t.getCanvasData(),o=i.height,s=r.height,l=s/o,c=i.width*l;i.height=s,i.width=c,i.top=0,i.width>=r.width?i.left=0:i.left=(r.width-i.width)/2,t.setCanvasData(i),n.left=0,n.top=0,n.width=i.width,n.height=i.height,t.setCropBoxData(n)}(this.$refs.cropper.cropper,t)}},created:function(){this.options.thumbnailWidth=null,this.options.thumbnailHeight=null,this.options.maxFiles=1,this.value&&(this.addedFile(Tr({},this.value,{upload:{}})),this.file.thumbnail=this.value.thumbnail,this.file.status="exist")},beforeDestroy:function(){this.setPending(!1),this.uploader._uploader.destroy()}},Vr=Object(k.a)(zr,(function(){var t,e=this,n=e.$createElement,r=e._self._c||n;return r("div",{staticClass:"SharpUpload",class:[{"SharpUpload--empty":!e.file,"SharpUpload--disabled":e.readOnly},e.modifiersClasses]},[r("div",{staticClass:"SharpUpload__inner"},[r("div",{staticClass:"SharpUpload__content"},[r("form",{directives:[{name:"show",rawName:"v-show",value:!e.file,expression:"!file"}],staticClass:"dropzone"},[r("button",{staticClass:"dz-message SharpButton SharpButton--ghost SharpUpload__upload-button",attrs:{type:"button",disabled:e.readOnly}},[e._v("\n                    "+e._s(e.l("form.upload.browse_button"))+"\n                ")])]),e._v(" "),e.file?[r("div",{staticClass:"SharpUpload__container",class:{row:e.showThumbnail}},[e.showThumbnail?r("div",{staticClass:"SharpUpload__thumbnail",class:[e.modifiers.compacted?"col-4 col-sm-3 col-xl-2":"col-4 col-md-4"]},[r("img",{attrs:{src:e.imageSrc},on:{load:e.handleImageLoaded}})]):e._e(),e._v(" "),r("div",{staticClass:"SharpUpload__infos",class:(t={},t[e.modifiers.compacted?"col-8 col-sm-9 col-xl-10":"col-8 col-md-8"]=e.showThumbnail,t)},[r("div",{staticClass:"mb-3 text-truncate"},[r("label",{staticClass:"SharpUpload__filename"},[e._v(e._s(e.fileName))]),e._v(" "),r("div",{staticClass:"SharpUpload__info mt-2"},[e.size?[r("span",{staticClass:"mr-2"},[e._v(e._s(e.size))])]:e._e(),e._v(" "),e.canDownload?[r("a",{staticClass:"SharpUpload__download-link",attrs:{href:e.downloadUrl,download:e.fileName}},[r("i",{staticClass:"fas fa-download"}),e._v("\n                                        "+e._s(e.l("form.upload.download_link"))+"\n                                    ")])]:e._e()],2),e._v(" "),r("transition",{attrs:{name:"SharpUpload__progress"}},[r("div",{directives:[{name:"show",rawName:"v-show",value:e.inProgress,expression:"inProgress"}],staticClass:"SharpUpload__progress mt-2"},[r("div",{staticClass:"SharpUpload__progress-bar",style:{width:e.progress+"%"},attrs:{role:"progressbar","aria-valuenow":e.progress,"aria-valuemin":"0","aria-valuemax":"100"}})])])],1),e._v(" "),r("div",{directives:[{name:"show",rawName:"v-show",value:!e.readOnly,expression:"!readOnly"}]},[r("button",{directives:[{name:"show",rawName:"v-show",value:!!e.originalImageSrc&&!e.inProgress,expression:"!!originalImageSrc && !inProgress"}],staticClass:"SharpButton SharpButton--sm SharpButton--secondary",attrs:{type:"button",disabled:!e.isCroppable},on:{click:e.onEditButtonClick}},[e._v("\n                                "+e._s(e.l("form.upload.edit_button"))+"\n                            ")]),e._v(" "),r("button",{staticClass:"SharpButton SharpButton--sm SharpButton--secondary SharpButton--danger SharpUpload__remove-button",attrs:{type:"button",disabled:e.readOnly},on:{click:function(t){return e.remove()}}},[e._v("\n                                "+e._s(e.l("form.upload.remove_button"))+"\n                            ")])])]),e._v(" "),r("button",{directives:[{name:"show",rawName:"v-show",value:!e.readOnly,expression:"!readOnly"}],staticClass:"SharpUpload__close-button",attrs:{type:"button"},on:{click:function(t){return e.remove()}}},[r("svg",{staticClass:"SharpUpload__close-icon",attrs:{"aria-label":"close",width:"10",height:"10",viewBox:"0 0 10 10","fill-rule":"evenodd"}},[r("path",{attrs:{d:"M9.8 8.6L8.4 10 5 6.4 1.4 10 0 8.6 3.6 5 .1 1.4 1.5 0 5 3.6 8.6 0 10 1.4 6.4 5z"}})])])])]:e._e(),e._v(" "),r("div",{ref:"clip-preview-template",staticClass:"clip-preview-template",staticStyle:{display:"none"}},[r("div")])],2)]),e._v(" "),e.originalImageSrc&&e.isCroppable?[r("Modal",{ref:"modal",attrs:{visible:e.showEditModal,"no-close-on-backdrop":"",title:e.l("modals.cropper.title"),static:""},on:{"update:visible":function(t){e.showEditModal=t},ok:e.onEditModalOk,shown:e.onEditModalShown,hidden:e.onEditModalHidden}},[r("vue-cropper",{ref:"cropper",staticClass:"SharpUpload__modal-vue-cropper",attrs:{"view-mode":2,"drag-mode":"crop","aspect-ratio":e.ratioX/e.ratioY,"auto-crop-area":1,zoomable:!1,guides:!1,background:!0,rotatable:!0,src:e.originalImageSrc,ready:e.onCropperReady,alt:"Source image"}}),e._v(" "),r("div",[r("button",{staticClass:"SharpButton SharpButton--primary",on:{click:function(t){return e.rotate(-90)}}},[r("i",{staticClass:"fas fa-undo"})]),e._v(" "),r("button",{staticClass:"SharpButton SharpButton--primary",on:{click:function(t){return e.rotate(90)}}},[r("i",{staticClass:"fas fa-redo"})])])],1)]:e._e(),e._v(" "),r("a",{ref:"dlLink",staticStyle:{display:"none"}})],2)}),[],!1,null,null,null);Vr.options.__file="VueClip.vue";var Nr=Vr.exports,Rr={headers:{"X-XSRF-TOKEN":Nt.a.read("XSRF-TOKEN")}};function Kr(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var qr=["Backspace","Enter"],Ur=["ArrowLeft","ArrowUp","ArrowDown","ArrowRight","Escape","Tab"],Hr=a.a.extend({mixins:[Ir],components:{VueClip:Nr},props:{downloadId:String,pendingKey:String,id:Number,value:Object,maxImageSize:Number,ratioX:Number,ratioY:Number,croppableFileTypes:Array},data:function(){return{show:this.value,marker:null}},computed:{options:function(){return function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Kr(t,e,n[e])}))}return t}({},Rr,{url:Ut,uploadMultiple:!1,acceptedFiles:{extensions:["image/*"],message:Zt("form.upload.message.bad_extension")},maxFilesize:{limit:this.maxImageSize,message:Zt("form.upload.message.file_too_big")}})},dropzone:function(){return this.$refs.vueclip.uploader._uploader},fileInput:function(){return this.dropzone.hiddenFileInput}},methods:{handleAdded:function(){this.show=!0,this.$emit("added")},inputClick:function(){this.fileInput.click()}},mounted:function(){var t=this;this.$on("delete-intent",(function(){var e=t.$el.querySelector(".SharpUpload__remove-button");e.focus(),e.addEventListener("keydown",(function(e){qr.includes(e.key)?t.$emit("remove"):Ur.includes(e.key)&&t.$emit("escape")}))}))}}),Gr=Object(k.a)(Hr,(function(){var t=this,e=t.$createElement;return(t._self._c||e)("VueClip",{directives:[{name:"show",rawName:"v-show",value:t.show,expression:"show"}],ref:"vueclip",staticClass:"SharpMarkdownUpload",attrs:{"pending-key":t.pendingKey,"download-id":t.downloadId,options:t.options,value:t.value,ratioX:t.ratioX,ratioY:t.ratioY,"croppable-file-types":t.croppableFileTypes,modifiers:t.modifiers,"on-added-file":t.handleAdded},on:{success:function(e){return t.$emit("success",e)},removed:function(e){return t.$emit("remove")},updated:function(e){return t.$emit("update",e)},active:function(e){return t.$emit("active")},inactive:function(e){return t.$emit("inactive")},"image-updated":function(e){return t.$emit("refresh")}}})}),[],!1,null,null,null);Gr.options.__file="MarkdownUpload.vue";var Wr=Gr.exports;function Yr(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Qr=function(t){var e=t.textProp;return{_localizedEditor:{textProp:e},mixins:[Rn],computed:{localizedText:function(){return this.isLocalized?null!==this.value[e]?this.value[e][this.locale]:"":this.value[e]}},methods:{localizedValue:function(t){return function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Yr(t,e,n[e])}))}return t}({},this.value,Yr({},e,this.isLocalized?Yn({localeObject:this.value[e],locale:this.locale,value:t}):t))}}}},Xr={bold:"fas fa-bold",italic:"fas fa-italic",strike:"fas fa-strikethrough",link:"fas fa-link",h1:"fas fa-heading",h2:"fas fa-heading fa-sm",h3:"fas fa-heading fa-xs",quote:"fas fa-quote-right",code:"fas fa-code",ul:"fas fa-list-ul",ol:"fas fa-list-ol",indent:"fas fa-indent","de-indent":"fas fa-outdent",undo:"fas fa-undo",redo:"fas fa-redo",hr:"fas fa-minus",image:"far fa-image"};function Jr(t){return Xr[t]||null}var Zr={bold:{name:"bold",action:Dr.a.toggleBold,className:Jr("bold"),title:"Bold"},italic:{name:"italic",action:Dr.a.toggleItalic,className:Jr("italic"),title:"Italic"},"heading-1":{name:"heading-1",action:Dr.a.toggleHeading1,className:Jr("h1"),title:"Big Heading"},"heading-2":{name:"heading-2",action:Dr.a.toggleHeading2,className:Jr("h2"),title:"Medium Heading"},"heading-3":{name:"heading-3",action:Dr.a.toggleHeading3,className:Jr("h3"),title:"Small Heading"},code:{name:"code",action:Dr.a.toggleCodeBlock,className:Jr("code"),title:"Code"},quote:{name:"quote",action:Dr.a.toggleBlockquote,className:Jr("quote"),title:"Quote"},"unordered-list":{name:"unordered-list",action:Dr.a.toggleUnorderedList,className:Jr("ul"),title:"Generic List"},"ordered-list":{name:"ordered-list",action:Dr.a.toggleOrderedList,className:Jr("ol"),title:"Numbered List"},link:{name:"link",action:Dr.a.drawLink,className:Jr("link"),title:"Create Link"},image:{name:"image",action:Dr.a.drawImage,className:Jr("image"),title:"Insert Image"},"horizontal-rule":{name:"horizontal-rule",action:Dr.a.drawHorizontalRule,className:Jr("hr"),title:"Insert Horizontal Line"},undo:{name:"undo",action:Dr.a.undo,className:Jr("undo"),title:"Undo"},redo:{name:"redo",action:Dr.a.redo,className:Jr("redo"),title:"Redo"}};function ta(t){return function(e,n){t(n)}}function ea(t,e){var n=t.value.substring(0,t.selectionStart||0),r=t.value.substring(t.selectionEnd||0),a=0===n.length||n.match(/\n$/)?"":"\n",i=n+a+e;t.value=i+r,t.selectionStart=i.length,t.selectionEnd=t.selectionStart,t.dispatchEvent(new CustomEvent("change",{bubbles:!0,cancelable:!1})),t.focus()}function na(t){var e,n=t.dataTransfer;if(n&&!function(t){return Array.from(t.types).indexOf("Files")>=0}(n)){var r=sa(n);if(r){t.stopPropagation(),t.preventDefault();var a=null!==(e=t.currentTarget)&&void 0!==e?e:t.target;a instanceof HTMLTextAreaElement&&ea(a,oa(r))}}}function ra(t){var e=t.dataTransfer;e&&(e.dropEffect="copy")}function aa(t){var e;if(t.clipboardData){var n=sa(t.clipboardData);if(n){t.stopPropagation(),t.preventDefault();var r=null!==(e=t.currentTarget)&&void 0!==e?e:t.target;r instanceof HTMLTextAreaElement&&ea(r,oa(n))}}}function ia(t){return(t.textContent||"").trim().replace(/\|/g,"\\|").replace(/\n/g," ")||" "}function oa(t){var e=Array.from(t.querySelectorAll("tr")),n=e.shift();if(!n)return"";var r,a=(r=n,Array.from(r.querySelectorAll("td, th")).map(ia)),i=a.map((function(){return"--"})),o="".concat(a.join(" | "),"\n").concat(i.join(" | "),"\n"),s=e.map((function(t){return Array.from(t.querySelectorAll("td")).map(ia).join(" | ")})).join("\n");return"\n".concat(o).concat(s,"\n\n")}function sa(t){if(-1===Array.from(t.types).indexOf("text/html"))return null;var e=t.getData("text/html");if(!/<table/i.test(e))return null;var n=function(t){var e=document.createElement("div");return e.innerHTML=t,e.querySelector("table")}(e);return!n||n.closest("[data-paste-markdown-skip]")?null:n}function la(t){return(la="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function ca(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){ua(t,e,n[e])}))}return t}function ua(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function da(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function ha(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){da(i,r,a,o,s,"next",t)}function s(t){da(i,r,a,o,s,"throw",t)}o(void 0)}))}}var fa=function(){},pa=Symbol("fileIdSymbol"),ma={name:"SharpMarkdown",mixins:[Qr({textProp:"text"})],props:{uniqueIdentifier:String,fieldConfigIdentifier:String,value:{type:Object,default:function(){return{}}},placeholder:String,toolbar:Array,height:{type:Number,default:300},innerComponents:Object,readOnly:Boolean},inject:["$tab"],data:function(){return{simplemdeInstances:{},cursorPos:{line:0,ch:0},uploaderId:(this.value.files||[]).length}},watch:{locale:function(){var t=this;return ha(H.a.mark((function e(){return H.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.isLocalized){e.next=4;break}return e.next=3,t.$nextTick();case 3:t.refreshOnExternalChange();case 4:case"end":return e.stop()}}),e)})))()}},computed:{simplemde:function(){return this.isLocalized?this.simplemdeInstances[this.locale]:this.simplemdeInstances},codemirror:function(){return(this.simplemde||{}).codemirror},idSymbol:function(){return pa},filesByName:function(){return this.value.files.reduce((function(t,e){return t[e.name]=e,t}),{})},indexByFileId:function(){var t=this;return this.value.files.reduce((function(e,n,r){return e[n[t.idSymbol]]=r,e}),{})},text:function(){return this.localizedText},transformedToolbar:function(){return(this.toolbar||[]).map((function(t){return"|"!==t?Zr[t]:t}))}},methods:{localizedTextareaRef:function(t){return"textarea_".concat(t)},indexedFiles:function(){var t=this;return(this.value.files||[]).map((function(e,n){return ca(ua({},t.idSymbol,n),e)}))},createUploader:function(t){var e,n=this,r=t.id,a=t.value,i=t.removeOptions,o=new Wr({mixins:[(e=this,{parent:e})],propsData:ca(ca({id:r,value:a},this.innerComponents.upload),{},{downloadId:this.fieldConfigIdentifier,pendingKey:"".concat(this.uniqueIdentifier,".upload.").concat(r)})});return o.$on("success",(function(t){return n.updateUploaderData(o,t)})),o.$on("refresh",(function(){return n.refreshCodemirror()})),o.$on("remove",(function(){return n.removeMarker(o,i)})),o.$on("update",(function(t){return n.updateFileData(o,t)})),o.$on("active",(function(){return n.setMarkerActive(o)})),o.$on("inactive",(function(){return n.setMarkerInactive(o)})),o.$on("escape",(function(){return n.escapeMarker()})),o.$mount(),o},createUserUploader:function(t){var e=this,n=null;return this.lastUploader&&this.lastUploader.$destroy(),(n=this.lastUploader=this.createUploader(t)).inputClick(),new Promise((function(t){n.$on("added",(function(){e.lastUploader=null,t(n)}))}))},refreshCodemirror:function(){console.log("refresh codemirror"),this.codemirror.refresh(),this.codemirror.focus()},removeMarker:function(t){var e=this,n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},r=n.isCMEvent,a=n.relativeFallbackLine,i=t.id,o=t.marker;if(!o.explicitlyCleared){if(!r){o.inclusiveLeft=o.inclusiveRight=!1;var s=o.find(),l=s.from.line,c=l-a;this.codemirror.replaceRange("",{line:c},{line:l+1,ch:0}),o.inclusiveLeft=o.inclusiveRight=!0,o.clear(),this.codemirror.focus()}t.$destroy(),this.value.files=this.value.files.filter((function(t){return t[e.idSymbol]!==i}))}},escapeMarker:function(){this.codemirror.focus()},updateUploaderData:function(t,e){var n=t.id,r=t.marker.find(),a=this.codemirror.getLine(r.from.line);this.codemirror.replaceRange(a.replace(/\(.*?\)/,"(".concat(e.name,")")),r.from,r.to),this.value.files.push(ca(ua({},this.idSymbol,n),e))},setMarkerActive:function(t){var e=t.marker;this.codemirror.addLineClass(e.lines[0],"wrap","SharpMarkdown__line--active")},setMarkerInactive:function(t){var e=t.marker;this.codemirror.removeLineClass(e.lines[0],"wrap","SharpMarkdown__line--active")},updateFileData:function(t,e){var n=t.id,r=this.indexByFileId[n],a=this.value.files[r];this.$set(this.value.files,r,ca(ca({},a),e))},insertUploadImage:function(){var t=this,e=arguments;return ha(H.a.mark((function n(){var r,a,i,o,s,l,c,u,d,h,f,p;return H.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(r=e.length>0&&void 0!==e[0]?e[0]:{},a=r.replaceBySelection,i=r.data,o=r.isInsertion,s=t.codemirror.getSelection(" "),l=t.codemirror.getLine(t.cursorPos.line)||"",c={id:i?t.filesByName[i.name][t.idSymbol]:t.uploaderId++,value:i&&t.filesByName[i.name],removeOptions:{relativeFallbackLine:1}},!o){n.next=10;break}return n.next=7,t.createUserUploader(c);case 7:n.t0=n.sent,n.next=11;break;case 10:n.t0=t.createUploader(c);case 11:return u=n.t0,s&&(t.codemirror.replaceSelection(""),l=t.codemirror.getLine(t.cursorPos.line)),(l.length||0===t.cursorPos.line&&0===t.cursorPos.ch||t.codemirror.findMarksAt({line:t.cursorPos.line-1,ch:0}).length)&&t.codemirror.replaceRange("\n",t.cursorPos),t.codemirror.getInputField().blur(),d=a?s:"![]()",h=o||t.cursorPos.line===t.codemirror.lineCount()-1?1:0,d+="\n".repeat(h),t.codemirror.replaceRange(d,t.cursorPos),t.codemirror.setCursor(t.cursorPos.line-h,0,{scroll:!!o}),f=t.cursorPos,p={line:t.cursorPos.line,ch:t.cursorPos.ch+d.length},u.marker=t.codemirror.markText(f,p,{replacedWith:u.$el,clearWhenEmpty:!1,inclusiveRight:!0,inclusiveLeft:!0,$component:u}),t.codemirror.addLineClass(u.marker.lines[0],"wrap","SharpMarkdown__upload-line"),u.marker.lines[0].on("delete",(function(){return t.removeMarker(u,{isCMEvent:!0,relativeFallbackLine:1})})),n.abrupt("return",u);case 25:case"end":return n.stop()}}),n)})))()},onCursorActivity:function(){this.codemirror&&(this.cursorPos=this.codemirror.getCursor())},onChange:function(){this.codemirror&&this.$emit("input",this.localizedValue(this.codemirror.getValue()))},onBeforeChange:function(t,e){if(e&&e.origin&&e.origin.includes("delete")){var n=t.findMarks(e.from,e.to);console.log(n),n.length&&n.forEach((function(t){t.$component&&(e.cancel(),t.$component.$emit("delete-intent"))}))}},codemirrorOn:function(t,e,n,r){r&&n(t),t.on(e,n)},localizeToolbar:function(t){t.toolbar.forEach((function(t){if("object"===la(t)){var e=t.name.replace(/-/g,"_");t.title=Zt("form.markdown.icons.".concat(e,".title"))}})),t.gui.toolbar.remove(),t.createToolbar()},setReadOnly:function(t){t.codemirror.setOption("readOnly",!0),t.toolbar.forEach((function(t){return"object"===la(t)&&(t.action=fa)}))},bindImageAction:function(t){var e=this;(t.toolbar.find((function(t){return"image"===t.name}))||{}).action=function(){return e.insertUploadImage({isInsertion:!0})}},parse:function(){var t=this,e=[];return this.codemirror.eachLine((function(n){var r=n.text,a=t.codemirror.getLineNumber(n),i=/!\[(.*?)\]\((.*?)\)/g.exec(r);if(i){var o=i.index,s=i[0].length,l=i[1],c=i[2];e.push({range:{start:{ch:o,line:a},end:{ch:o+s,line:a}},data:{name:c,title:l}})}})),e.reverse().forEach((function(e){var n=e.range,r=e.data;t.codemirror.setSelection(n.start,n.end,{scroll:!1}),t.insertUploadImage({replaceBySelection:!0,data:r})})),e},refreshOnExternalChange:function(){var t=this;if(!this.simplemde.parsed){var e=this.parse();this.simplemde.parsed=!0,e.length&&this.$nextTick((function(){return window.scrollTo(0,0)}))}setTimeout((function(){return t.codemirror.refresh()}),50)},createSimpleMDE:function(t){var e=t.element,n=t.initialValue,r=new Dr.a({element:e,initialValue:n,placeholder:this.placeholder,spellChecker:!1,toolbar:this.transformedToolbar,autoDownloadFontAwesome:!1,status:!1});return this.readOnly&&this.setReadOnly(r),this.localizeToolbar(r),this.bindImageAction(r),this.initCM(r.codemirror),r},initCM:function(t){t.setSize("auto",this.height),this.codemirrorOn(t,"cursorActivity",this.onCursorActivity,!0),this.codemirrorOn(t,"change",this.onChange),this.codemirrorOn(t,"beforeChange",this.onBeforeChange),function(t){t.on("dragover",ta(ra)),t.on("drop",ta(na)),t.on("paste",ta(aa))}(t)}},mounted:function(){var t=this;this.isLocalized?this.simplemdeInstances=this.locales.reduce((function(e,n){return ca(ca({},e),{},ua({},n,t.createSimpleMDE({element:t.$refs[t.localizedTextareaRef(n)][0],initialValue:(t.value.text||{})[n]})))}),{}):this.simplemdeInstances=this.createSimpleMDE({element:this.$refs.textarea,initialValue:this.value.text}),this.value.files=this.indexedFiles(),this.$tab?this.$tab.$once("active",(function(){return t.refreshOnExternalChange()})):this.$nextTick((function(){return t.refreshOnExternalChange()}))}},va=Object(k.a)(ma,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpMarkdown",class:{"SharpMarkdown--read-only":t.readOnly}},[n("div",{staticClass:"SharpModule__inner"},[t.isLocalized?t._l(t.locales,(function(e){return n("div",{directives:[{name:"show",rawName:"v-show",value:t.locale===e,expression:"locale === loc"}]},[n("textarea",{ref:t.localizedTextareaRef(e),refInFor:!0,attrs:{id:t.localizedTextareaRef(e)}})])})):[n("textarea",{ref:"textarea"})]],2)])}),[],!1,null,null,null);va.options.__file="Markdown.vue";var ya=va.exports;function ba(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){ga(t,e,n[e])}))}return t}function ga(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var _a={name:"SharpNumber",functional:!0,render:function(t,e){var n=e.props,r=n.step,a=n.min,i=n.max,o=n.showControls,s=e.data;return t(Pr,ba({},s,{class:ba({"hide-controls":!o},s.class),props:ba({inputType:"number"},s.props),attrs:ba({step:r,min:a,max:i},s.attrs)}))}},wa=Object(k.a)(_a,void 0,void 0,!1,null,null,null);wa.options.__file="Number.vue";var Sa=wa.exports;function Ca(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var xa={name:"SharpUpload",components:{VueClip:Nr},mixins:[Ir],inject:["$field"],props:{uniqueIdentifier:String,fieldConfigIdentifier:String,value:Object,fileFilter:Array,maxFileSize:Number,thumbnail:String,croppableFileTypes:Array,ratioX:Number,ratioY:Number,readOnly:Boolean},computed:{options:function(){var t=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Ca(t,e,n[e])}))}return t}({},Rr);return t.url=Ut,t.uploadMultiple=!1,this.fileFilter&&(t.acceptedFiles={extensions:this.fileFilter,message:Zt("form.upload.message.bad_extension")}),this.maxFileSize&&(t.maxFilesize={limit:this.maxFileSize,message:Zt("form.upload.message.file_too_big")}),t}}},Oa=Object(k.a)(xa,(function(){var t=this,e=t.$createElement;return(t._self._c||e)("VueClip",{attrs:{"pending-key":t.uniqueIdentifier,"download-id":t.fieldConfigIdentifier,options:t.options,value:t.value,ratioX:t.ratioX,ratioY:t.ratioY,"croppable-file-types":t.croppableFileTypes,"read-only":t.readOnly,modifiers:t.modifiers},on:{input:function(e){return t.$emit("input",e)},error:function(e){return t.$field.$emit("error",e)},reset:function(e){return t.$field.$emit("clear")}}})}),[],!1,null,null,null);Oa.options.__file="Upload.vue";var ka=Oa.exports,ja={mixins:[Rn],methods:{localizeLabel:function(t){return this.isLocalized?t[this.locale]:t},localizedOptionLabel:function(t){return this.localizeLabel(t.label)}}},La={extends:ja,methods:{localizeLabel:function(t){return this.isLocalized?t[this.locale]||Zt("form.tags.unknown_label"):t},localizedTagLabel:function(t){var e,n,r,a,i=this;return this.isLocalized?(e={locales:this.locales,resolve:function(e){return e===i.locale?t:null}},n=e.locales,r=e.resolve,a=void 0===r?function(){return null}:r,n.reduce((function(t,e){return Kn({},t,qn({},e,a(e)))}),{})):t},localizedCustomLabel:function(t){return this.localizeLabel(t.label)}}};function Pa(t){return(Pa="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function Ea(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),e&&Da(t,e)}function Da(t,e){return(Da=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t})(t,e)}function $a(t){function e(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Date.prototype.toString.call(Reflect.construct(Date,[],(function(){}))),!0}catch(t){return!1}}return function(){var n,r=Ba(t);if(e()){var a=Ba(this).constructor;n=Reflect.construct(r,arguments,a)}else n=r.apply(this,arguments);return Aa(this,n)}}function Aa(t,e){return!e||"object"!==Pa(e)&&"function"!=typeof e?function(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}(t):e}function Ba(t){return(Ba=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)})(t)}function Ia(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function Fa(t,e){for(var n=0;n<e.length;n++){var r=e[n];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}var Ta=function(){function t(e){Ia(this,t),this.id=e.id,this.label=e.label}var e,n,r;return e=t,(n=[{key:"internalId",set:function(t){this._internalId=t},get:function(){return this._internalId}}])&&Fa(e.prototype,n),r&&Fa(e,r),t}(),Ma=function(t){Ea(n,t);var e=$a(n);function n(){return Ia(this,n),e.apply(this,arguments)}return n}(Ta),za=function(t){Ea(n,t);var e=$a(n);function n(){return Ia(this,n),e.apply(this,arguments)}return n}(Ta),Va={name:"SharpTags",mixins:[La],components:{Multiselect:pn},props:{value:Array,options:Array,placeholder:String,maxTagCount:Number,createText:String,creatable:{type:Boolean,default:!0},readOnly:Boolean},data:function(){return{tags:[],lastIndex:0}},computed:{indexedOptions:function(){return this.options.map(this.patchOption)},dynamicPlaceholder:function(){return this.tags.length<(this.maxTagCount||1/0)?this.placeholder:""},ids:function(){return this.tags.map((function(t){return t.internalId}))}},watch:{tags:"onTagsChanged"},methods:{patchOption:function(t,e){var n=new Ma(t);return n.internalId=e,n},patchTag:function(t){var e=this.indexedOptions.find((function(e){return e.id===t.id})),n=new za(e);return n.internalId=e.internalId,n},handleNewTag:function(t){var e=new za({id:null,label:this.localizedTagLabel(t)});e.internalId=this.lastIndex++,this.tags.push(e)},handleInput:function(t){this.tags=t},handleTextInput:function(t){t.length>0&&this.$refs.multiselect.filteredOptions.length>1?this.$refs.multiselect.pointer=1:this.$refs.multiselect.pointer=0},onTagsChanged:function(){this.$emit("input",this.tags.map((function(t){return new za(t)})))}},created:function(){this.lastIndex+=this.options.length,this.tags=(this.value||[]).map(this.patchTag)}},Na=Object(k.a)(Va,(function(){var t=this,e=t.$createElement;return(t._self._c||e)("Multiselect",{ref:"multiselect",staticClass:"SharpTags",attrs:{value:t.tags,options:t.indexedOptions,placeholder:t.dynamicPlaceholder,"tag-placeholder":t.createText,max:t.maxTagCount,taggable:t.creatable,"close-on-select":!1,disabled:t.readOnly,"track-by":"_internalId",label:"label","custom-label":t.localizedCustomLabel,multiple:"",searchable:"","hide-selected":"","show-labels":!1},on:{"search-change":t.handleTextInput,input:t.handleInput,tag:t.handleNewTag}})}),[],!1,null,null,null);Na.options.__file="Tags.vue";var Ra=Na.exports,Ka=n("OCip"),qa=n("oihH"),Ua={name:"SharpDatepicker",extends:n.n(qa).a,computed:{remainingDays:function(){var t=this.days.length+this.blankDays;return t>35?42-t:35-t}},methods:{init:function(){this.value&&this.setValue(this.value),this.showDayCalendar()},clickOutside:function(){}}},Ha=Object(k.a)(Ua,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpDate__datepicker",class:t.wrapperClass},[n("input",{class:t.inputClass,attrs:{type:t.inline?"hidden":"text",name:t.name,id:t.id,placeholder:t.placeholder,"clear-button":t.clearButton,disabled:t.disabledPicker,required:t.required,readonly:""},domProps:{value:t.formattedValue},on:{click:function(e){return t.showCalendar()}}}),t._v(" "),t.clearButton&&t.selectedDate?n("i",{staticClass:"vdp-datepicker__clear-button",on:{click:function(e){return t.clearDate()}}},[t._v("×")]):t._e(),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:t.showDayView,expression:"showDayView"}],staticClass:"SharpDate__calendar",class:{open:t.showDayView},style:t.calendarStyle},[n("header",[n("span",{staticClass:"prev",class:{disabled:t.previousMonthDisabled(t.pageTimestamp)},on:{click:t.previousMonth}},[n("svg",{attrs:{width:"8",height:"12",viewBox:"0 0 8 12","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M7.5 10.6L2.8 6l4.7-4.6L6.1 0 0 6l6.1 6z"}})])]),t._v(" "),n("span",{staticClass:"up",on:{click:t.showMonthCalendar}},[n("span",{staticClass:"SharpDate__cur-month"},[t._v(t._s(t.currMonthName))]),t._v(" "),n("span",{staticClass:"SharpDate__cur-year"},[t._v(t._s(t.currYear))])]),t._v(" "),n("span",{staticClass:"next",class:{disabled:t.nextMonthDisabled(t.pageTimestamp)},on:{click:t.nextMonth}},[n("svg",{attrs:{width:"8",height:"12",viewBox:"0 0 8 12","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M0 10.6L4.7 6 0 1.4 1.4 0l6.1 6-6.1 6z"}})])])]),t._v(" "),n("div",{staticClass:"SharpDate__innerContainer"},[n("div",{staticClass:"SharpDate__rContainer"},[n("div",{staticClass:"SharpDate__weekdays"},t._l(t.daysOfWeek,(function(e){return n("span",{staticClass:"cell day-header"},[t._v(t._s(e))])})),0),t._v(" "),n("div",{staticClass:"SharpDate__days"},[n("div",{staticClass:"SharpDate__dayContainer"},[t._l(t.blankDays,(function(t){return n("span",{staticClass:"cell day blank"})})),t._v(" "),t._l(t.days,(function(e){return n("span",{staticClass:"cell day",class:t.dayClasses(e),on:{click:function(n){return t.selectDate(e)}}},[t._v(t._s(e.date)+"\n                        ")])})),t._v(" "),t._l(t.remainingDays,(function(t){return n("span",{staticClass:"cell day blank"})}))],2)])])])]),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:t.showMonthView,expression:"showMonthView"}],staticClass:"SharpDate__calendar",class:{open:t.showMonthView},style:t.calendarStyle},[n("header",[n("span",{staticClass:"prev",class:{disabled:t.previousYearDisabled(t.pageTimestamp)},on:{click:t.previousYear}},[n("svg",{attrs:{width:"8",height:"12",viewBox:"0 0 8 12","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M7.5 10.6L2.8 6l4.7-4.6L6.1 0 0 6l6.1 6z"}})])]),t._v(" "),n("span",{staticClass:"up",on:{click:t.showYearCalendar}},[n("span",{staticClass:"SharpDate__cur-year"},[t._v(t._s(t.getPageYear()))])]),t._v(" "),n("span",{staticClass:"next",class:{disabled:t.nextYearDisabled(t.pageTimestamp)},on:{click:t.nextYear}},[n("svg",{attrs:{width:"8",height:"12",viewBox:"0 0 8 12","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M0 10.6L4.7 6 0 1.4 1.4 0l6.1 6-6.1 6z"}})])])]),t._v(" "),n("div",{staticClass:"SharpDate__innerContainer"},[n("div",{staticClass:"SharpDate__rContainer"},[n("div",{staticClass:"SharpDate__monthContainer"},t._l(t.months,(function(e){return n("span",{staticClass:"cell month",class:{selected:e.isSelected,disabled:e.isDisabled},on:{click:function(n){return n.stopPropagation(),t.selectMonth(e)}}},[t._v(t._s(e.month))])})),0)])])]),t._v(" "),n("div",{directives:[{name:"show",rawName:"v-show",value:t.showYearView,expression:"showYearView"}],staticClass:"SharpDate__calendar",class:{open:t.showYearView},style:t.calendarStyle},[n("header",[n("span",{staticClass:"prev",class:{disabled:t.previousDecadeDisabled(t.pageTimestamp)},on:{click:t.previousDecade}},[n("svg",{attrs:{width:"8",height:"12",viewBox:"0 0 8 12","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M7.5 10.6L2.8 6l4.7-4.6L6.1 0 0 6l6.1 6z"}})])]),t._v(" "),n("span",{staticClass:"up"},[n("span",{staticClass:"SharpDate__cur-decade"},[t._v(t._s(t.getPageDecade()))])]),t._v(" "),n("span",{staticClass:"next",class:{disabled:t.nextMonthDisabled(t.pageTimestamp)},on:{click:t.nextDecade}},[n("svg",{attrs:{width:"8",height:"12",viewBox:"0 0 8 12","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M0 10.6L4.7 6 0 1.4 1.4 0l6.1 6-6.1 6z"}})])])]),t._v(" "),n("div",{staticClass:"SharpDate__innerContainer"},[n("div",{staticClass:"SharpDate__rContainer"},[n("div",{staticClass:"SharpDate__yearContainer"},t._l(t.years,(function(e){return n("span",{staticClass:"cell year",class:{selected:e.isSelected,disabled:e.isDisabled},on:{click:function(n){return n.stopPropagation(),t.selectYear(e)}}},[t._v(t._s(e.year))])})),0)])])])])}),[],!1,null,null,null);Ha.options.__file="Datepicker.vue";var Ga=Ha.exports,Wa=n("UX8W"),Ya=n.n(Wa);function Qa(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function Xa(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){Qa(i,r,a,o,s,"next",t)}function s(t){Qa(i,r,a,o,s,"throw",t)}o(void 0)}))}}var Ja=Ya.a.methods.renderFormat,Za={name:"SharpTimePicker",mixins:[Ya.a],props:{active:Boolean,min:String,max:String,minMaxFormat:{type:String,default:"HH:mm"}},data:function(){return{showDropdown:!0,isSelection:!1}},watch:{minute:"layout",hour:"layout",active:function(t){t&&this.layout(!1)}},computed:{minMoment:function(){var t=this.min?oe()(this.min,this.minMaxFormat):oe()("0:0",this.minMaxFormat);return{minutes:t.minutes(),hours:t.hours()}},maxMoment:function(){var t=this.max?oe()(this.max,this.minMaxFormat):oe()("23:59",this.minMaxFormat);return{minutes:t.minutes(),hours:t.hours()}},croppedHours:function(){var t=this;return this.hours.filter((function(e){return(e=parseInt(e))>=t.minMoment.hours&&e<=t.maxMoment.hours}))},croppedMinutes:function(){var t=this,e=parseInt(this.hour);return e===this.minMoment.hours?this.minutes.filter((function(e){return(e=parseInt(e))>=t.minMoment.minutes})):e===this.maxMoment.hours?this.minutes.filter((function(e){return(e=parseInt(e))<=t.maxMoment.minutes})):this.minutes}},methods:{select2:function(t,e){this.select(t,e),this.isSelection=!0},renderFormat:function(){Ja.apply(this,arguments)},layout:function(){var t=this,e=arguments;return Xa(H.a.mark((function n(){var r,a,i,o;return H.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return i=!(e.length>0&&void 0!==e[0])||e[0],o=i?"smooth":"auto",n.next=4,t.$nextTick();case 4:null===(r=t.$refs.minutes.querySelector(".active"))||void 0===r||r.scrollIntoView({block:"center",behavior:o}),null===(a=t.$refs.hours.querySelector(".active"))||void 0===a||a.scrollIntoView({block:"center",behavior:o});case 6:case"end":return n.stop()}}),n)})))()}}},ti=Object(k.a)(Za,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("span",{staticClass:"time-picker"},[n("header",[n("span",{staticClass:"hint",domProps:{textContent:t._s(t.hourType)}}),t._v(" "),n("span",{staticClass:"hint",domProps:{textContent:t._s(t.minuteType)}})]),t._v(" "),n("div",{staticClass:"dropdown"},[n("div",{staticClass:"select-list"},[n("ul",{ref:"hours",staticClass:"hours"},[t._l(t.croppedHours,(function(e){return[n("li",{class:{active:t.value&&t.hour===e},on:{click:function(n){return n.stopPropagation(),t.select2("hour",e)}}},[t._v(t._s(e))])]}))],2),t._v(" "),n("ul",{ref:"minutes",staticClass:"minutes"},[t._l(t.croppedMinutes,(function(e){return[n("li",{class:{active:t.value&&t.minute===e},on:{click:function(n){return n.stopPropagation(),t.select2("minute",e)}}},[t._v(t._s(e))])]}))],2)])])])}),[],!1,null,null,null);function ei(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}ti.options.__file="Timepicker.vue";var ni={name:"SharpDate",components:{DatePicker:Ga,TimePicker:ti.exports,BPopover:Ka.a},inject:["$field"],mixins:[Tt,qe],props:{value:{type:[Date,String]},hasDate:{type:Boolean,default:!0},hasTime:{type:Boolean,default:!1},displayFormat:{type:String,default:"DD/MM/YYYY HH:mm"},mondayFirst:Boolean,stepTime:{type:Number,default:30},minTime:String,maxTime:String,readOnly:Boolean},data:function(){return{showPicker:!1,localInputValue:null}},computed:{format:function(){return this.hasTime&&!this.hasDate?"HH:mm":null},dateObject:function(){return this.value?oe()(this.value,this.format).toDate():null},timeObject:function(){return this.value?{HH:oe()(this.value,this.format).format("HH"),mm:oe()(this.value,this.format).format("mm")}:null},inputValue:function(){return"string"==typeof this.localInputValue?this.localInputValue:this.value?oe()(this.value,this.format).format(this.displayFormat):""},popoverBoundary:function(){return document.querySelector("[data-popover-boundary]")}},methods:{popoverTarget:function(){return this.$refs.input},getMoment:function(){return this.value?oe()(this.value,this.format):oe()()},handleDateSelect:function(t){var e=this.getMoment();e.set({year:t.getFullYear(),month:t.getMonth(),date:t.getDate()}),this.$emit("input",e.toDate())},handleTimeSelect:function(t){var e=t.data,n=this.getMoment();n.set({hour:e.HH,minute:e.mm,second:e.ss}),this.getMoment().format("HH:mm")!==n.format("HH:mm")&&this.$emit("input",n.toDate())},handleInput:function(t){var e=oe()(t.target.value,this.displayFormat,!0);this.localInputValue=t.target.value,this.showPicker=!1,e.isValid()?(this.rollback(),this.$emit("input",e.toDate())):this.$field.$emit("error","".concat(Zt("form.date.validation_error.format")," (").concat(this.displayFormat,")"))},increase:function(t){this.translate(t.target,1)},decrease:function(t){this.translate(t.target,-1)},translate:function(t,e){var n,r=this;return(n=H.a.mark((function n(){var a;return H.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(!(a=r.changeOnArrowPressed(t.selectionStart,e))){n.next=5;break}return n.next=4,r.$nextTick();case 4:t.setSelectionRange(a.start,a.end);case 5:case"end":return n.stop()}}),n)})),function(){var t=this,e=arguments;return new Promise((function(r,a){var i=n.apply(t,e);function o(t){ei(i,r,a,o,s,"next",t)}function s(t){ei(i,r,a,o,s,"throw",t)}o(void 0)}))})()},add:function(t,e){var n=this.getMoment();n.add(t,e),this.$emit("input",n.toDate())},nearestMinutesDist:function(t){var e=this.getMoment().minutes();if(e%this.stepTime==0)return t*this.stepTime;var n=t<0?"floor":"ceil";return this.stepTime*Math[n](e/this.stepTime)-e},updateMoment:function(t,e){switch(t){case"H":this.add(e,"hours");break;case"m":this.add(this.nearestMinutesDist(e),"minutes");break;case"s":this.add(e,"seconds");break;case"Y":this.add(e,"years");break;case"M":this.add(e,"months");break;case"D":this.add(e,"days");break;default:return!1}return!0},changeOnArrowPressed:function(t,e){var n=t;if(!this.updateMoment(this.displayFormat[n],e)&&t&&(n--,!this.updateMoment(this.displayFormat[n],e)))return null;var r=this.displayFormat[n];return{start:this.displayFormat.indexOf(r),end:this.displayFormat.lastIndexOf(r)+1}},rollback:function(){this.$field.$emit("clear"),this.localInputValue=null},clear:function(){this.rollback(),this.$emit("input",null)},handleBlur:function(){this.rollback()}},mounted:function(){this.setFocusable(this.$refs.input)}},ri=Object(k.a)(ni,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpDate",class:{"SharpDate--open":t.showPicker}},[n("div",{staticClass:"SharpDate__input-wrapper"},[n("input",{ref:"input",staticClass:"SharpDate__input",attrs:{placeholder:t.displayFormat,disabled:t.readOnly,autocomplete:"off"},domProps:{value:t.inputValue},on:{input:t.handleInput,blur:t.handleBlur,keydown:[function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"up",38,e.key,["Up","ArrowUp"])?null:(e.preventDefault(),t.increase(e))},function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"down",40,e.key,["Down","ArrowDown"])?null:(e.preventDefault(),t.decrease(e))}]}}),t._v(" "),n("button",{ref:"clearButton",staticClass:"SharpDate__clear-button",attrs:{type:"button"},on:{click:function(e){return t.clear()}}},[n("svg",{staticClass:"SharpDate__clear-button-icon",attrs:{"aria-label":"close",width:"10",height:"10",viewBox:"0 0 10 10","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M9.8 8.6L8.4 10 5 6.4 1.4 10 0 8.6 3.6 5 .1 1.4 1.5 0 5 3.6 8.6 0 10 1.4 6.4 5z"}})])])]),t._v(" "),n("b-popover",{attrs:{target:t.popoverTarget,show:t.showPicker,boundary:t.popoverBoundary,"no-fade":"",triggers:"focus",placement:"bottom"},on:{"update:show":function(e){t.showPicker=e}}},[n("div",{staticClass:"SharpDate__picker position-static"},[t.hasDate?[n("DatePicker",{ref:"datepicker",staticClass:"SharpDate__date",attrs:{language:t.language,"monday-first":t.mondayFirst,inline:"",value:t.dateObject},on:{selected:t.handleDateSelect}})]:t._e(),t._v(" "),t.hasTime?[n("TimePicker",{ref:"timepicker",staticClass:"SharpDate__time",attrs:{value:t.timeObject,active:t.showPicker,format:t.displayFormat,"minute-interval":t.stepTime,min:t.minTime,max:t.maxTime},on:{change:t.handleTimeSelect}})]:t._e()],2)])],1)}),[],!1,null,null,null);ri.options.__file="Date.vue";var ai=ri.exports,ii={name:"SharpCheck",props:{value:Boolean,text:String,readOnly:Boolean},methods:{handleCheck:function(t){this.$emit("input",t.target.checked)}}},oi=Object(k.a)(ii,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpCheck"},[n("label",{staticClass:"SharpCheck__label"},[n("input",{staticClass:"SharpCheck__input",attrs:{type:"checkbox",disabled:t.readOnly},domProps:{checked:t.value},on:{change:t.handleCheck}}),t._v(" "),n("span",{staticClass:"SharpCheck__appearance"},[n("svg",{staticClass:"SharpCheck__checkmark",attrs:{width:"12",height:"9",viewBox:"0 0 12 9","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M4.1 6.1L1.4 3.4 0 4.9 4.1 9l7.6-7.6L10.3 0z"}})])]),t._v(" "),n("span",{staticClass:"SharpCheck__label-text"},[t._v(t._s(t.text))])])])}),[],!1,null,null,null);oi.options.__file="Check.vue";var si=oi.exports,li={name:"FieldsLayout",components:{Grid:Ot},props:{layout:{type:Array,required:!0},visible:{type:Object,default:function(){return{}}}},data:function(){return{fieldsetMap:{}}},methods:{isFieldset:function(t){return!!t.legend},isFieldsetVisible:function(t){var e=this;return(t.fields||[]).flat().some((function(t){return e.visible[t.key]}))}}},ci=Object(k.a)(li,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("Grid",{attrs:{rows:t.layout},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.itemLayout;return[t.isFieldset(r)?[n("fieldset",{directives:[{name:"show",rawName:"v-show",value:t.isFieldsetVisible(r),expression:"isFieldsetVisible(fieldLayout)"}],staticClass:"SharpForm__fieldset"},[n("div",{staticClass:"SharpModule__inner"},[n("div",{staticClass:"SharpModule__header"},[n("div",{staticClass:"SharpModule__title"},[t._v(t._s(r.legend))])]),t._v(" "),n("div",{staticClass:"SharpModule__content"},[n("FieldsLayout",{attrs:{layout:r.fields},scopedSlots:t._u([{key:"default",fn:function(e){var n=e.fieldLayout;return[t._t("default",null,{fieldLayout:n})]}}],null,!0)})],1)])])]:[t._t("default",null,{fieldLayout:r})]]}}])})}),[],!1,null,null,null);ci.options.__file="FieldsLayout.vue";var ui=ci.exports,di={name:"SharpListItem",extends:ui,mixins:[Ft]},hi=Object(k.a)(di,void 0,void 0,!1,null,null,null);hi.options.__file="ListItem.vue";var fi=hi.exports;function pi(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){mi(t,e,n[e])}))}return t}function mi(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var vi=function(t){return{_localizedForm:t,methods:{fieldLocalizedValue:function(e,n){var r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:this.data,a=arguments.length>3&&void 0!==arguments[3]?arguments[3]:this.fieldLocale,i=this[t][e];return this.localized&&i.localized&&Wn(i)?Yn({localeObject:r[e],locale:a[e],value:n}):n},defaultFieldLocaleMap:function(t,e){var n=t.fields,r=t.locales;return Object.values(n).filter((function(t){var e;return"list"===t.type?Object.values(null!==(e=t.itemFields)&&void 0!==e?e:{}).some((function(t){return t.localized})):t.localized})).reduce((function(t,n){return pi(pi({},t),{},mi({},n.key,e||r&&r[0]))}),{})}}}};function yi(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=[],r=!0,a=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(r=(o=s.next()).done)&&(n.push(o.value),!e||n.length!==e);r=!0);}catch(t){a=!0,i=t}finally{try{r||null==s.return||s.return()}finally{if(a)throw i}}return n}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function bi(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){gi(t,e,n[e])}))}return t}function gi(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function _i(t,e){if(null==t)return{};var n,r,a=function(t,e){if(null==t)return{};var n,r,a={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(a[n]=t[n]);return a}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(a[n]=t[n])}return a}var wi={name:"SharpList",inject:["$form"],mixins:[qe,vi("itemFields")],components:{Draggable:et.a,ListItem:fi,TemplateRenderer:Tn},props:{fieldKey:String,fieldLayout:Object,value:Array,addable:{type:Boolean,default:!0},sortable:{type:Boolean,default:!1},removable:{type:Boolean,default:!1},addText:{type:String,default:"Ajouter un élément"},itemFields:{type:Object,required:!0},collapsedItemTemplate:String,maxItemCount:Number,itemIdAttribute:String,readOnly:Boolean,locale:[String,Array]},data:function(){return{list:[],dragActive:!1,lastIndex:0}},watch:{list:"handleListChanged",locale:"handleLocaleChanged"},computed:{classes:function(){return{"SharpList--can-sort":this.showSortButton}},disabled:function(){return this.readOnly||this.dragActive},dragOptions:function(){return{disabled:!this.dragActive,handle:".SharpList__overlay-handle"}},showAddButton:function(){return this.addable&&(this.list.length<this.maxItemCount||!this.maxItemCount)&&!this.disabled},showInsertButton:function(){return this.showAddButton&&this.sortable&&!this.disabled},showSortButton:function(){return!this.hasPendingActions&&this.sortable&&this.list.length>1},showRemoveButton:function(){return this.removable&&!this.disabled},dragIndexSymbol:function(){return Symbol("dragIndex")},indexSymbol:function(){return Symbol("index")},hasPendingActions:function(){var t;return null===(t=this.$form)||void 0===t?void 0:t.hasUploadingFields(this.fieldKey)},isReadOnly:function(){return this.readOnly||this.dragActive}},methods:{handleListChanged:function(){this.$emit("locale-change",this.list.map((function(t){return t._fieldsLocale})))},handleLocaleChanged:function(t){var e=this;"string"==typeof t&&this.list.forEach((function(n){Object.assign(n,e.withLocale(null,t))}))},itemData:function(t){t.id,t._fieldsLocale;return _i(t,["id","_fieldsLocale"])},transformedFields:function(t){var e=this.list[t],n=this.itemData(e);return gr(this.itemFields,n)},indexedList:function(){var t=this;return(this.value||[]).map((function(e,n){return t.withLocale(bi(gi({},t.indexSymbol,n),e))}))},createItem:function(){var t;return Object.entries(this.itemFields).reduce((function(t,e){var n=yi(e,2),r=n[0],a=n[1];return t[r]="markdown"===a.type?{}:null,t}),this.withLocale((gi(t={},this.itemIdAttribute,null),gi(t,this.indexSymbol,this.lastIndex++),t)))},insertNewItem:function(t,e){e.target&&e.target.blur(),this.list.splice(t+1,0,this.createItem())},add:function(){this.list.push(this.createItem())},remove:function(t){this.list.splice(t,1)},update:function(t){var e=this;return function(n,r){var a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},i=a.forced,o=bi({},e.list[t]),s=bi(bi({},i?null:pr(e.itemFields,n,(function(){return e.fieldLocalizedValue(n,null,o,o._fieldsLocale)}))),{},gi({},n,e.fieldLocalizedValue(n,r,o,o._fieldsLocale)));Object.assign(e.list[t],s)}},updateLocale:function(t,e,n){this.$set(this.list[t]._fieldsLocale,e,n),this.handleListChanged()},collapsedItemData:function(t){return bi({$index:t[this.dragIndexSymbol]},t)},toggleDrag:function(){var t=this;this.dragActive=!this.dragActive,this.list.forEach((function(e,n){return e[t.dragIndexSymbol]=n}))},withLocale:function(t,e){return bi(bi({},t),{},{_fieldsLocale:this.defaultFieldLocaleMap({fields:this.itemFields,locales:this.$form.locales},e)})},initList:function(){this.list=this.indexedList(),this.lastIndex=this.list.length,this.$emit("input",this.list)}},created:function(){this.localized=this.$form.localized,this.initList()}},Si=Object(k.a)(wi,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpList",class:t.classes},[t.showSortButton?[n("button",{staticClass:"SharpButton SharpButton--ghost SharpList__sort-button",class:{"SharpButton--active":t.dragActive},attrs:{type:"button","data-inactive-text":t.l("form.list.sort_button.inactive"),"data-active-text":t.l("form.list.sort_button.active")},on:{click:t.toggleDrag}},[n("svg",{staticClass:"SharpButton__icon",attrs:{width:"24",height:"22",viewBox:"0 0 24 22","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M20 14V0h-4v14h-4l6 8 6-8zM4 8v14h4V8h4L6 0 0 8z"}})])])]:t._e(),t._v(" "),n("Draggable",{ref:"draggable",attrs:{options:t.dragOptions,list:t.list},scopedSlots:t._u([t.showAddButton?{key:"footer",fn:function(){return[n("button",{key:-1,staticClass:"SharpButton SharpButton--ghost SharpList__add-button",attrs:{type:"button"},on:{click:t.add}},[t._v("\n                "+t._s(t.addText)+"\n            ")])]},proxy:!0}:null],null,!0)},[n("transition-group",{attrs:{name:"expand",tag:"div"}},t._l(t.list,(function(e,r){return n("div",{key:e[t.indexSymbol],staticClass:"SharpList__item",class:{"SharpList__item--collapsed":t.dragActive}},[n("div",{staticClass:"SharpModule__inner"},[n("div",{staticClass:"SharpModule__content"},[t.dragActive&&t.collapsedItemTemplate?[n("TemplateRenderer",{attrs:{name:"CollapsedItem",template:t.collapsedItemTemplate,"template-data":t.collapsedItemData(e)}})]:[n("ListItem",{attrs:{layout:t.fieldLayout.item,"error-identifier":r},scopedSlots:t._u([{key:"default",fn:function(a){var i=a.fieldLayout;return[n("FieldDisplay",{attrs:{"field-key":i.key,"context-fields":t.transformedFields(r),"context-data":e,"error-identifier":i.key,"config-identifier":i.key,"update-data":t.update(r),locale:e._fieldsLocale[i.key],"read-only":t.isReadOnly},on:{"locale-change":function(e,n){return t.updateLocale(r,e,n)}}})]}}],null,!0)}),t._v(" "),t.showRemoveButton?[n("button",{staticClass:"SharpButton SharpButton--danger SharpButton--sm mt-3",on:{click:function(e){return t.remove(r)}}},[t._v("\n                                    "+t._s(t.l("form.list.remove_button"))+"\n                                ")])]:t._e()],t._v(" "),t.dragActive?[n("div",{staticClass:"SharpList__overlay-handle"})]:t._e()],2)]),t._v(" "),t.showInsertButton&&r<t.list.length-1?[n("div",{staticClass:"SharpList__new-item-zone"},[n("button",{staticClass:"SharpButton SharpButton--sm",on:{click:function(e){return t.insertNewItem(r,e)}}},[t._v(t._s(t.l("form.list.insert_button")))])])]:t._e()],2)})),0)],1),t._v(" "),t.readOnly&&!t.list.length?[n("em",{staticClass:"SharpList__empty-alert"},[t._v(t._s(t.l("form.list.empty")))])]:t._e()],2)}),[],!1,null,null,null);Si.options.__file="List.vue";var Ci=Si.exports;function xi(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var Oi={name:"SharpSelect",mixins:[ja],components:{Multiselect:pn,Check:si},props:{value:[Array,String,Number],uniqueIdentifier:String,options:{type:Array,required:!0,default:function(){return[]}},multiple:{type:Boolean,default:!1},display:{type:String,default:"dropdown"},clearable:{type:Boolean,default:!1},showSelectAll:{type:Boolean,default:!0},placeholder:{type:String,default:"-"},maxSelected:Number,readOnly:Boolean,inline:{type:Boolean,default:!0}},data:function(){return{checkboxes:this.value}},watch:{options:function(){this.init()}},computed:{multiselectOptions:function(){return this.options.map((function(t){return t.id}))},optionsLabel:function(){var t=this;return this.options.reduce((function(e,n){return e[n.id]=t.localizedOptionLabel(n),e}),{})},itemClasses:function(){return{"SharpSelect__item--inline":this.inline}},hasClearButton:function(){return this.clearable&&!this.multiple&&null!=this.value}},methods:{lang:Zt,isSelected:function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this.value;return null!=t.id&&null!=e&&"".concat(t.id)==="".concat(e)},isChecked:function(t){var e,n=this;return null===(e=this.value)||void 0===e?void 0:e.some((function(e){return n.isSelected(t,e)}))},remove:function(){this.$emit("input",null)},multiselectLabel:function(t){return this.optionsLabel[t]},handleInput:function(t){this.$emit("input",t)},handleCheckboxChanged:function(t,e){var n,r,a=this;t?this.$emit("input",[].concat(xi(null!==(n=this.value)&&void 0!==n?n:[]),[e.id])):this.$emit("input",(null!==(r=this.value)&&void 0!==r?r:[]).filter((function(t){return!a.isSelected(e,t)})))},handleRadioChanged:function(t){this.$emit("input",t.id)},handleSelectAllClicked:function(){this.$emit("input",this.options.map((function(t){return t.id})))},handleUnselectAllClicked:function(){this.$emit("input",[])},setDefault:function(){!this.clearable&&null==this.value&&this.options.length>0&&this.$emit("input",this.options[0].id,{force:!0})},init:function(){mr(this,this.setDefault,{dependantAttributes:["options"]})}},created:function(){this.init()}},ki=Object(k.a)(Oi,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpSelect",class:[{"SharpSelect--multiple":t.multiple},"SharpSelect--"+t.display]},["dropdown"===t.display?n("Multiselect",{ref:"multiselect",attrs:{value:t.value,searchable:!1,options:t.multiselectOptions,multiple:t.multiple,"hide-selected":t.multiple,"close-on-select":!t.multiple,"custom-label":t.multiselectLabel,placeholder:t.placeholder,disabled:t.readOnly,max:t.maxSelected,"allow-empty":t.clearable},on:{input:t.handleInput,open:function(e){return t.$emit("open")},close:function(e){return t.$emit("close")}},scopedSlots:t._u([t.hasClearButton?{key:"caret",fn:function(){return[n("button",{staticClass:"SharpSelect__clear-button",attrs:{type:"button"},on:{mousedown:function(e){return e.stopPropagation(),e.preventDefault(),t.remove()}}},[n("svg",{staticClass:"SharpSelect__clear-button-icon",attrs:{"aria-label":"close",width:"10",height:"10",viewBox:"0 0 10 10","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M9.8 8.6L8.4 10 5 6.4 1.4 10 0 8.6 3.6 5 .1 1.4 1.5 0 5 3.6 8.6 0 10 1.4 6.4 5z"}})])])]},proxy:!0}:null,{key:"tag",fn:function(e){var r=e.option,a=e.remove;return[n("span",{key:r,staticClass:"multiselect__tag"},[n("span",[t._v(t._s(t.multiselectLabel(r)))]),t._v(" "),n("i",{staticClass:"multiselect__tag-icon",attrs:{"aria-hidden":"true",tabindex:"1"},on:{keypress:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:(e.preventDefault(),a(r))},mousedown:function(t){return t.preventDefault(),t.stopPropagation(),a(r)}}})])]}},{key:"option",fn:function(){return[t._t("option")]},proxy:!0}],null,!0)}):[n("div",{staticClass:"SharpSelect__group",class:{"SharpSelect__group--block":!t.inline}},[t.multiple?[t._l(t.options,(function(e){return[n("div",{key:e.id,staticClass:"SharpSelect__item",class:t.itemClasses},[n("Check",{attrs:{value:t.isChecked(e),text:t.optionsLabel[e.id],"read-only":t.readOnly},on:{input:function(n){return t.handleCheckboxChanged(n,e)}}})],1)]})),t._v(" "),t.showSelectAll?[n("div",{staticClass:"SharpSelect__links mt-3"},[n("div",{staticClass:"row mx-n2"},[n("div",{staticClass:"col-auto px-2"},[n("a",{attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.handleSelectAllClicked(e)}}},[t._v(t._s(t.lang("form.select.select_all")))])]),t._v(" "),n("div",{staticClass:"col-auto px-2"},[n("a",{attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.handleUnselectAllClicked(e)}}},[t._v(t._s(t.lang("form.select.unselect_all")))])])])])]:t._e()]:[t._l(t.options,(function(e,r){return[n("div",{key:e.id,staticClass:"SharpSelect__item",class:t.itemClasses},[n("input",{staticClass:"SharpRadio",attrs:{type:"radio",tabindex:"0",id:""+t.uniqueIdentifier+r,disabled:t.readOnly,name:t.uniqueIdentifier},domProps:{checked:t.isSelected(e),value:e.id},on:{change:function(n){return t.handleRadioChanged(e)}}}),t._v(" "),n("label",{staticClass:"SharpRadio__label",attrs:{for:""+t.uniqueIdentifier+r}},[n("span",{staticClass:"SharpRadio__appearance"}),t._v("\n                            "+t._s(t.optionsLabel[e.id])+"\n                        ")])])]}))]],2)]],2)}),[],!1,null,null,null);ki.options.__file="Select.vue";var ji=ki.exports,Li={name:"SharpHtml",components:{TemplateRenderer:Tn},props:{value:Object,template:String}},Pi=Object(k.a)(Li,(function(){var t=this.$createElement;return(this._self._c||t)("TemplateRenderer",{attrs:{name:"Html",template:this.template,"template-data":this.value}})}),[],!1,null,null,null);Pi.options.__file="Html.vue";var Ei=Pi.exports;function Di(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function $i(t,e){if(null==t)return{};var n,r,a=function(t,e){if(null==t)return{};var n,r,a={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(a[n]=t[n]);return a}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(a[n]=t[n])}return a}var Ai={mapTypeControl:!1,streetViewControl:!1};function Bi(t){var e=t;return Array.isArray(e)?new google.maps.LatLngBounds(e[0],e[1]):null}function Ii(t){var e=t.maxBounds,n=function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Di(t,e,n[e])}))}return t}({},$i(t,["maxBounds"]));return Array.isArray(e)&&(n.restriction={latLngBounds:Bi(e)}),n}function Fi(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Ti={name:"SharpGmaps",components:{GmapMap:c.Map,GmapMarker:c.Marker},props:{markerPosition:Object,center:Object,zoom:Number},computed:{options:function(){return Ii(function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Fi(t,e,n[e])}))}return t}({},Ai,{maxBounds:this.maxBounds}))}}},Mi=Object(k.a)(Ti,(function(){var t=this.$createElement,e=this._self._c||t;return e("GmapMap",{ref:"map",attrs:{center:this.center,zoom:this.zoom,options:this.options}},[e("GmapMarker",{attrs:{position:this.markerPosition}})],1)}),[],!1,null,null,null);Mi.options.__file="Gmaps.vue";var zi=Mi.exports;function Vi(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Ni={name:"SharpGmapsEditable",components:{GmapMap:c.Map,GmapMarker:c.Marker},props:{markerPosition:Object,bounds:Array,center:Object,zoom:Number,maxBounds:Array},computed:{options:function(){return Ii(function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Vi(t,e,n[e])}))}return t}({},Ai,{maxBounds:this.maxBounds,draggableCursor:"crosshair"}))},hasMarker:function(){return!!this.markerPosition}},watch:{bounds:function(t){var e=Bi(t);e&&this.$refs.map.$mapObject.fitBounds(e)}},methods:{handleMapClicked:function(t){this.$emit("change",t.latLng.toJSON())},handleMarkerDragEnd:function(t){this.$emit("change",t.latLng.toJSON())}}},Ri=Object(k.a)(Ni,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("GmapMap",{ref:"map",attrs:{center:t.center,zoom:t.zoom,options:t.options},on:{click:t.handleMapClicked}},[t.hasMarker?[n("GmapMarker",{attrs:{position:t.markerPosition,draggable:""},on:{dragend:t.handleMarkerDragEnd}})]:t._e()],2)}),[],!1,null,null,null);Ri.options.__file="GmapsEditable.vue";var Ki=Ri.exports,qi=n("HZII"),Ui=n.n(qi)()((function(t){var e=t.apiKey,n={v:3};return e&&(n.key=e),Object(c.loadGmapApi)(n),a.a.$gmapApiPromiseLazy()})),Hi=null;var Gi=n("JpmB"),Wi=n("Tith"),Yi=n("pArE"),Qi=n("4R65");function Xi(t){var e=t;return Array.isArray(e)?Object(Qi.latLngBounds)(e[0],e[1]):null}var Ji={name:"SharpOsm",components:{LMap:Gi.a,LMarker:Wi.a,LTileLayer:Yi.a},props:{markerPosition:Object,zoom:Number,center:Object,maxBounds:Array,tilesUrl:{type:String,default:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}},computed:{transformedMaxBounds:function(){return Xi(this.maxBounds)}}},Zi=Object(k.a)(Ji,(function(){var t=this.$createElement,e=this._self._c||t;return e("LMap",{attrs:{zoom:this.zoom,center:this.center,"max-bounds":this.transformedMaxBounds}},[e("LTileLayer",{attrs:{url:this.tilesUrl}}),this._v(" "),e("LMarker",{attrs:{"lat-lng":this.markerPosition}})],1)}),[],!1,null,null,null);Zi.options.__file="Osm.vue";var to=Zi.exports,eo={name:"SharpOsmEditable",components:{LMap:Gi.a,LMarker:Wi.a,LTileLayer:Yi.a},props:{markerPosition:Object,center:Object,zoom:Number,bounds:Array,maxBounds:Array,tilesUrl:{type:String,default:"https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"}},computed:{hasMarker:function(){return!!this.markerPosition},transformedBounds:function(){return Xi(this.bounds)},transformedMaxBounds:function(){return Xi(this.maxBounds)}},methods:{handleMapClicked:function(t){this.$emit("change",t.latlng)},handleMarkerDragEnd:function(t){this.$emit("change",t.target.getLatLng())}}},no=Object(k.a)(eo,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("LMap",{attrs:{zoom:t.zoom,center:t.center,bounds:t.transformedBounds,"max-bounds":t.transformedMaxBounds},on:{click:t.handleMapClicked}},[n("LTileLayer",{attrs:{url:t.tilesUrl}}),t._v(" "),t.hasMarker?[n("LMarker",{attrs:{"lat-lng":t.markerPosition,draggable:""},on:{dragend:t.handleMarkerDragEnd}})]:t._e()],2)}),[],!1,null,null,null);no.options.__file="OsmEditable.vue";var ro=no.exports;function ao(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function io(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){ao(i,r,a,o,s,"next",t)}function s(t){ao(i,r,a,o,s,"throw",t)}o(void 0)}))}}function oo(t){return zt.a.get("https://nominatim.openstreetmap.org/search",{params:{q:t,format:"json"}}).then((function(t){return t.data}))}function so(t){return zt.a.get("https://nominatim.openstreetmap.org/reverse",{params:{lat:t.lat,lon:t.lng,format:"json"}}).then((function(t){return[t.data]}))}function lo(){return(lo=io(H.a.mark((function t(e){var n,r,a;return H.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(n=e.address,!(r=e.latLng)){t.next=7;break}return t.next=4,so(r);case 4:t.t0=t.sent,t.next=10;break;case 7:return t.next=9,oo(n);case 9:t.t0=t.sent;case 10:return a=t.t0,t.abrupt("return",a.map((function(t){return{location:{lat:Number(t.lat),lng:Number(t.lon)},bounds:[{lat:Number(t.boundingbox[0]),lng:Number(t.boundingbox[2])},{lat:Number(t.boundingbox[1]),lng:Number(t.boundingbox[3])}],address:t.display_name}})));case 12:case"end":return t.stop()}}),t)})))).apply(this,arguments)}var co=function(t){return lo.apply(this,arguments)};function uo(t,e,n){return"gmaps"===t?(a=(r=e).address,i=r.latLng,Hi||(Hi=new google.maps.Geocoder),new Promise((function(t,e){Hi.geocode({address:a,location:i},(function(n,r){"OK"===r?t(n.map((function(t){return{location:t.geometry.location.toJSON(),bounds:(e=t.geometry.viewport,[e.getSouthWest().toJSON(),e.getNorthEast().toJSON()]),address:t.formatted_address};var e}))):"ZERO_RESULTS"===r?t([]):e(r)}))}))):"osm"===t?co(e):Promise.resolve([]);var r,a,i}function ho(t,e){var n=t<0?e?"W":"S":e?"E":"N",r=0|(t<0?t=-t:t),a=0|t%1*60,i=(0|60*t%1*6e3)/100;return"".concat(r,"°").concat(a,'"').concat(i,"' ").concat(n)}function fo(t){return t?t.name:null}function po(t){return t&&t.options||{}}function mo(t){return t?t.tilesUrl:null}var vo={mixins:[Ue("form.geolocation.modal")],components:{Loading:Dt,Modal:ln,TextField:Pr,Button:D},props:{location:Object,center:Object,bounds:Object,zoom:Number,maxBounds:Array,geocoding:Boolean,mapsProvider:{type:String,default:"gmaps"},mapsOptions:Object,geocodingProvider:{type:String,default:"gmaps"},geocodingOptions:Object},data:function(){return{loading:!1,search:null,message:null,currentLocation:this.location,currentBounds:this.bounds}},computed:{editableMapComponent:function(){return"gmaps"===(t=this.mapsProvider)?Ki:"osm"===t?ro:void 0;var t},hasGeocoding:function(){return this.geocoding},classes:function(){return{"SharpGeolocationEdit--loading":this.loading}},mapClasses:function(){return["SharpGeolocationEdit__map--".concat(this.mapsProvider)]},tilesUrl:function(){return mo(this.mapsOptions)}},methods:{handleSearchInput:function(t){this.search=t},handleMarkerPositionChanged:function(t){var e=this;this.currentLocation=t,this.message="",this.$emit("change",this.currentLocation),this.hasGeocoding&&(this.loading=!0,uo(this.geocodingProvider,{latLng:t},this.geocodingOptions).then((function(t){t.length>0&&(e.search=t[0].address)})).finally((function(){e.loading=!1})))},handleSearchSubmitted:function(){var t=this,e=this.search;this.message="",this.loading=!0,uo(this.geocodingProvider,{address:e},this.geocodingOptions).then((function(n){n.length>0?(t.currentLocation=n[0].location,t.currentBounds=n[0].bounds,t.$emit("change",t.currentLocation)):t.message=t.lSub("geocode_input.message.no_results").replace(":query",e||"")})).catch((function(e){t.message="".concat(t.lSub("geocode_input.message.error")).concat(e?" (".concat(e,")"):"")})).finally((function(){t.loading=!1}))}}},yo=Object(k.a)(vo,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpGeolocationEdit",class:t.classes},[t.hasGeocoding?[n("div",{staticClass:"mb-2"},[n("form",{on:{submit:function(e){return e.preventDefault(),t.handleSearchSubmitted(e)}}},[n("div",{staticClass:"row no-gutters"},[n("div",{staticClass:"col position-relative"},[n("TextField",{staticClass:"SharpGeolocationEdit__input",attrs:{value:t.search,placeholder:t.lSub("geocode_input.placeholder")},on:{input:t.handleSearchInput}}),t._v(" "),n("Loading",{staticClass:"SharpGeolocationEdit__loading",attrs:{visible:t.loading,small:""}})],1),t._v(" "),n("div",{staticClass:"col-auto pl-2"},[n("Button",{attrs:{outline:""}},[t._v(t._s(t.lSub("search_button")))])],1)])]),t._v(" "),t.message?[n("small",[t._v(t._s(t.message))])]:t._e()],2)]:t._e(),t._v(" "),n(t.editableMapComponent,{tag:"component",staticClass:"SharpGeolocationEdit__map",class:t.mapClasses,attrs:{"marker-position":t.currentLocation,center:t.center,bounds:t.currentBounds,zoom:t.zoom,"max-bounds":t.maxBounds,"tiles-url":t.tilesUrl},on:{change:t.handleMarkerPositionChanged}})],2)}),[],!1,null,null,null);function bo(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}yo.options.__file="GeolocationEdit.vue";var go={name:"SharpGeolocation",mixins:[qe],inject:{$tab:{default:null}},components:{GeolocationEdit:yo.exports,Card:B,Button:D,Modal:ln},props:{value:Object,readOnly:Boolean,uniqueIdentifier:String,geocoding:Boolean,apiKey:String,boundaries:Object,zoomLevel:{type:Number,default:4},initialPosition:{type:Object,default:function(){return{lat:46.1445458,lng:-2.4343779}}},displayUnit:{type:String,default:"DD",validator:function(t){return"DMS"===t||"DD"===t}},mapsProvider:{type:Object,default:function(){return{name:"gmaps"}}},geocodingProvider:{type:Object,default:function(){return{name:"gmaps"}}}},data:function(){return{ready:!1,modalVisible:!1,location:this.value}},computed:{isLoading:function(){return!this.ready},isEmpty:function(){return!this.value},latLngString:function(){return"DMS"===this.displayUnit?{lat:ho(this.value.lat),lng:ho(this.value.lng,!0)}:"DD"===this.displayUnit?this.value:void 0},mapComponent:function(){return"gmaps"===(t=fo(this.mapsProvider))?zi:"osm"===t?to:void 0;var t},mapClasses:function(){return["SharpGeolocation__map--".concat(fo(this.mapsProvider))]},tilesUrl:function(){return mo(po(this.mapsProvider))},maxBounds:function(){return this.boundaries?[this.boundaries.sw,this.boundaries.ne]:null},modalTitle:function(){return this.geocoding?this.l("form.geolocation.modal.title"):this.l("form.geolocation.modal.title-no-geocoding")}},methods:{providerName:fo,providerOptions:po,handleModalSubmitted:function(){this.$emit("input",this.location)},handleRemoveButtonClicked:function(){this.$emit("input",null)},handleShowModalButtonClicked:function(){this.modalVisible=!0},handleEditButtonClicked:function(){this.modalVisible=!0},handleLocationChanged:function(t){this.location=t},loadProvider:function(t){var e,n=fo(t),r=po(t).apiKey;return e={apiKey:r},"gmaps"===n?Promise.resolve(Ui(e)):Promise.resolve()},init:function(){var t,e=this;return(t=H.a.mark((function t(){return H.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.loadProvider(e.mapsProvider);case 2:if(!e.geocodingProvider){t.next=5;break}return t.next=5,e.loadProvider(e.geocodingProvider);case 5:e.ready=!0;case 6:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){bo(i,r,a,o,s,"next",t)}function s(t){bo(i,r,a,o,s,"throw",t)}o(void 0)}))})()}},created:function(){this.init()},mounted:function(){this.$tab&&this.$tab.$once("active",(function(){!function(){if("function"==typeof Event)window.dispatchEvent(new Event("resize"));else{var t=document.createEvent("UIEvents");t.initUIEvent("resize",!0,!1,window,0),window.dispatchEvent(t)}}()}))}},_o=Object(k.a)(go,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpGeolocation"},[t.isLoading?[t._v("\n        "+t._s(t.l("form.geolocation.loading"))+"\n    ")]:t.isEmpty?[n("Button",{staticClass:"w-100",attrs:{outline:""},on:{click:t.handleShowModalButtonClicked}},[t._v("\n            "+t._s(t.l("form.geolocation.browse_button"))+"\n        ")])]:[n("Card",{staticClass:"SharpModule--closeable",attrs:{light:"","has-close":!t.readOnly},on:{"close-click":t.handleRemoveButtonClicked}},[n("div",{staticClass:"row"},[n("div",{staticClass:"col-7"},[n(t.mapComponent,{tag:"component",staticClass:"SharpGeolocation__map",class:t.mapClasses,attrs:{"marker-position":t.value,center:t.value,zoom:t.zoomLevel,"max-bounds":t.maxBounds,"tiles-url":t.tilesUrl}})],1),t._v(" "),n("div",{staticClass:"col-5 pl-0"},[n("div",{staticClass:"d-flex flex-column justify-content-between h-100"},[n("div",[n("div",[n("small",[t._v("Latitude : "+t._s(t.latLngString.lat))])]),t._v(" "),n("div",[n("small",[t._v("Longitude : "+t._s(t.latLngString.lng))])])]),t._v(" "),n("div",[n("Button",{staticClass:"remove-button",attrs:{small:"",outline:"",type:"danger",disabled:t.readOnly},on:{click:t.handleRemoveButtonClicked}},[t._v("\n                                "+t._s(t.l("form.geolocation.remove_button"))+"\n                            ")]),t._v(" "),n("Button",{attrs:{small:"",outline:"",disabled:t.readOnly},on:{click:t.handleEditButtonClicked}},[t._v("\n                                "+t._s(t.l("form.geolocation.edit_button"))+"\n                            ")])],1)])])])])],t._v(" "),n("Modal",{attrs:{title:t.modalTitle,visible:t.modalVisible,"no-close-on-backdrop":""},on:{"update:visible":function(e){t.modalVisible=e},ok:t.handleModalSubmitted}},[n("transition",{attrs:{duration:300}},[t.modalVisible?[n("GeolocationEdit",{attrs:{location:t.value,center:t.value||t.initialPosition,zoom:t.zoomLevel,"max-bounds":t.maxBounds,"maps-provider":t.providerName(t.mapsProvider),"maps-options":t.providerOptions(t.mapsProvider),geocoding:t.geocoding,"geocoding-provider":t.providerName(t.geocodingProvider),"geocoding-options":t.providerOptions(t.geocodingProvider)},on:{change:t.handleLocationChanged}})]:t._e()],2)],1)],2)}),[],!1,null,null,null);_o.options.__file="Geolocation.vue";var wo=_o.exports,So={bold:{attribute:"bold",icon:Jr("bold")},italic:{attribute:"italic",icon:Jr("italic")},strike:{attribute:"strike",icon:Jr("strike")},link:{action:"link",attribute:"href",icon:Jr("link")},heading1:{attribute:"heading1",icon:Jr("h1")},quote:{attribute:"quote",icon:Jr("quote")},code:{attribute:"code",icon:Jr("code")},bullet:{attribute:"bullet",icon:Jr("ul")},number:{attribute:"number",icon:Jr("ol")},increaseNestingLevel:{action:"increaseNestingLevel",icon:Jr("indent")},decreaseNestingLevel:{action:"decreaseNestingLevel",icon:Jr("de-indent")},undo:{action:"undo",icon:Jr("undo")},redo:{action:"redo",icon:Jr("redo")}},Co={mixins:[Ue("form.wysiwyg")],props:{toolbar:Array},data:function(){return{buttons:So}},directives:{buttonData:function(t,e){var n=e.value,r=n.attribute,a=n.action;r&&t.setAttribute("data-trix-attribute",r),a&&t.setAttribute("data-trix-action",a)}}},xo=Object(k.a)(Co,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("div",{staticClass:"editor-toolbar"},[t._l(t.toolbar,(function(e){return["|"===e?n("i",{staticClass:"separator"},[t._v("|")]):t.buttons[e]?n("button",{directives:[{name:"button-data",rawName:"v-button-data",value:t.buttons[e],expression:"buttons[part]"}],staticClass:"fa",class:t.buttons[e].icon,attrs:{tabindex:"-1"}}):t._e()]}))],2),t._v(" "),n("div",{staticClass:"trix-dialogs",attrs:{"data-trix-dialogs":""}},[n("div",{staticClass:"trix-dialog trix-dialog--link",attrs:{"data-trix-dialog":"href","data-trix-dialog-attribute":"href"}},[n("div",{staticClass:"trix-dialog__link-fields"},[n("input",{staticClass:"trix-input trix-input--dialog",attrs:{type:"url",name:"href",placeholder:t.lSub("dialogs.add_link.input_placeholder"),required:"","data-trix-input":""}}),t._v(" "),n("div",{staticClass:"trix-button-group"},[n("input",{staticClass:"trix-button trix-button--dialog",attrs:{type:"button",value:t.lSub("dialogs.add_link.link_button"),"data-trix-method":"setAttribute"}}),t._v(" "),n("input",{staticClass:"trix-button trix-button--dialog",attrs:{type:"button",value:t.lSub("dialogs.add_link.unlink_button"),"data-trix-method":"removeAttribute"}})])])])])])}),[],!1,null,null,null);xo.options.__file="TrixCustomToolbar.vue";var Oo=xo.exports,ko={name:"SharpTrix",mixins:[Qr({textProp:"text"})],components:{TrixCustomToolbar:Oo},props:{value:Object,toolbar:Array,height:{type:Number,default:250},placeholder:String,readOnly:Boolean,uniqueIdentifier:String},watch:{locale:function(){this.localized&&this.$refs.trix.editor.loadHTML(this.text)}},computed:{inputId:function(){return"trix-input-".concat(this.uniqueIdentifier)},toolbarId:function(){return"trix-toolbar-".concat(this.uniqueIdentifier)},text:function(){return this.localized?this.localizedText:this.value.text}},methods:{handleChanged:function(t){this.$emit("input",this.localizedValue(t.target.value))}},created:function(){window.Trix.config.toolbar.getDefaultHTML=function(){return""}}},jo=Object(k.a)(ko,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpTrix",class:{"SharpTrix--read-only":t.readOnly}},[n("div",{staticClass:"SharpModule__inner"},[n("input",{attrs:{id:t.inputId,type:"hidden"},domProps:{value:t.text}}),t._v(" "),t.toolbar?n("trix-toolbar",{staticClass:"SharpModule__header",attrs:{id:t.toolbarId}},[n("trix-custom-toolbar",{attrs:{toolbar:t.toolbar}})],1):t._e(),t._v(" "),n("trix-editor",{ref:"trix",staticClass:"SharpModule__content",style:{height:t.height+"px",maxHeight:t.height+"px"},attrs:{input:t.inputId,toolbar:t.toolbarId,placeholder:t.placeholder},on:{"trix-change":t.handleChanged}})],1)])}),[],!1,null,null,null);jo.options.__file="TrixEditor.vue";var Lo=jo.exports,Po=n("wmP1"),Eo={components:{ElDatePicker:n.n(Po).a},props:{value:{default:function(){return{start:null,end:null}}},displayFormat:{type:String,default:"DD/MM/YYYY"},startPlaceholder:{type:String,default:function(){return Zt("form.daterange.start_placeholder")}},endPlaceholder:{type:String,default:function(){return Zt("form.daterange.end_placeholder")}},clearable:{type:Boolean,default:!0},readOnly:Boolean,mondayFirst:Boolean},computed:{transformedValue:function(){var t=this.value||{};return t.start||t.end?[t.start,t.end]:null},transformedFormat:function(){return this.displayFormat.replace(/D/g,"d").replace(/Y/g,"y")},pickerOptions:function(){return{firstDayOfWeek:this.mondayFirst?1:7}},classes:function(){return{"SharpDateRange--clearable":this.clearable}}},methods:{handleChanged:function(t){var e=t||[];this.$emit("input",{start:e[0],end:e[1]})},handleFocused:function(){this.$emit("focus")},handleBlur:function(){this.$emit("blur")}}},Do=Object(k.a)(Eo,(function(){var t=this,e=t.$createElement;return(t._self._c||e)("el-date-picker",{ref:"picker",staticClass:"SharpDateRange",class:t.classes,attrs:{value:t.transformedValue,format:t.transformedFormat,"start-placeholder":t.startPlaceholder,"end-placeholder":t.endPlaceholder,disabled:t.readOnly,clearable:t.clearable,"picker-options":t.pickerOptions,type:"daterange","popper-class":"SharpDateRange__popper"},on:{input:t.handleChanged,focus:t.handleFocused,blur:t.handleBlur}})}),[],!1,null,null,null);Do.options.__file="DateRange.vue";var $o=Do.exports,Ao={autocomplete:Cr,text:Pr,textarea:kr,markdown:ya,number:Sa,upload:ka,tags:Ra,date:ai,check:si,list:Ci,select:ji,html:Ei,geolocation:wo,wysiwyg:Lo,daterange:$o};function Bo(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Io(t,e,n[e])}))}return t}function Io(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Fo(t,e){if(null==t)return{};var n,r,a=function(t,e){if(null==t)return{};var n,r,a={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(a[n]=t[n]);return a}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(a[n]=t[n])}return a}var To={name:"SharpField",inheritAttrs:!1,provide:function(){return{$field:this}},props:{fieldKey:String,fieldType:String,fieldProps:Object,fieldLayout:Object,value:[String,Number,Boolean,Object,Array,Date],locale:[Array,String],uniqueIdentifier:String,fieldConfigIdentifier:String,updateData:Function,readOnly:Boolean},computed:{isCustom:function(){return Ve(this.fieldType)},component:function(){return this.isCustom?Ne(this.fieldType):Ao[this.fieldType]}},render:function(t){var e=this;if(!this.component)return this.custom||he("Unknown field type '".concat(this.fieldType,"'"),this.fieldProps),null;var n=this.fieldProps,r=(n.key,Fo(n,["key"]));return t(this.component,{props:Bo({fieldKey:this.fieldKey,fieldLayout:this.fieldLayout,value:this.value,locale:this.locale,uniqueIdentifier:this.uniqueIdentifier,fieldConfigIdentifier:this.fieldConfigIdentifier},r),attrs:{dynamicAttributes:r.dynamicAttributes},on:Bo({input:function(t){var n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};e.fieldProps.readOnly&&!n.force?de("SharpField '".concat(e.fieldKey,"', can't update because is readOnly")):e.updateData(e.fieldKey,t,{forced:n.force})},blur:function(){e.fieldProps.focused=!1}},this.$listeners)})}},Mo=Object(k.a)(To,void 0,void 0,!1,null,null,null);Mo.options.__file="Field.vue";var zo=Mo.exports,Vo={props:{locales:{type:Array,required:!0},currentLocale:{type:String,required:!0},fieldValue:[String,Number,Boolean,Object,Array],isLocaleObject:Boolean,errors:Array},methods:{isActive:function(t){return this.currentLocale===t},isEmpty:function(t){var e=this.isLocaleObject?(this.fieldValue||{})[t]:this.fieldValue;return Array.isArray(e)?!e.length:!e},hasError:function(t){var e;return null===(e=this.errors)||void 0===e?void 0:e.includes(t)},handleButtonClicked:function(t){this.$emit("change",t)}}},No=Object(k.a)(Vo,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpFieldLocaleSelect"},[t._l(t.locales,(function(e){return[n("button",{staticClass:"SharpFieldLocaleSelect__btn ml-2",class:{"SharpFieldLocaleSelect__btn--active":t.isActive(e),"SharpFieldLocaleSelect__btn--empty":t.isEmpty(e),"SharpFieldLocaleSelect__btn--error":t.hasError(e)},on:{click:function(n){return t.handleButtonClicked(e)}}},[t._v("\n            "+t._s(e)+"\n        ")])]}))],2)}),[],!1,null,null,null);function Ro(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function Ko(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=[],r=!0,a=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(r=(o=s.next()).done)&&(n.push(o.value),!e||n.length!==e);r=!0);}catch(t){a=!0,i=t}finally{try{r||null==s.return||s.return()}finally{if(a)throw i}}return n}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function qo(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Uo(t,e,n[e])}))}return t}function Uo(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}No.options.__file="FieldLocaleSelect.vue";var Ho={name:"SharpFieldContainer",mixins:[It,Ye],components:{Field:zo,FieldLocaleSelect:No.exports},inject:["$tab","$form"],props:qo(qo({},zo.props),{},{label:String,helpMessage:String,originalValue:[String,Number,Boolean,Object,Array,Date],errorIdentifier:[String,Number],localizedErrorIdentifier:String}),data:function(){return{state:"default",stateMessage:""}},watch:{originalValue:{deep:!0,handler:"handleValueChanged"},"$form.errors":function(t){this.updateError(t)},locale:function(){this.updateError(this.$form.errors)}},computed:{formGroupClasses:function(){return["SharpForm__form-item--type-".concat(this.fieldType),{"SharpForm__form-item--danger":"error"===this.state||this.errorsLocales.length>0,"SharpForm__form-item--success":"ok"===this.state,"SharpForm__form-item--no-label":!this.showLabel}]},extraStyle:function(){return this.fieldProps.extraStyle},exposedProps:function(){return qo(qo({},this.$props),{},{uniqueIdentifier:this.mergedErrorIdentifier,fieldConfigIdentifier:this.mergedConfigIdentifier})},showLabel:function(){return!!this.label||""===this.label},resolvedOriginalValue:function(){return t={field:this.fieldProps,value:this.originalValue},e=t.field,n=t.value,"markdown"===e.type||"wysiwyg"===e.type?(n||{}).text:n;var t,e,n},isLocaleObject:function(){return Wn(this.fieldProps)},mergedErrorIdentifier:function(){return this.getMergedIdentifier("mergedErrorIdentifier",this.errorIdentifier)},mergedLocalizedErrorIdentifier:function(){return this.localizedErrorIdentifier?this.getMergedIdentifier("mergedErrorIdentifier",this.localizedErrorIdentifier):null},errorsLocales:function(){var t=this;return Object.entries(this.$form.errors).filter((function(t){var e=Ko(t,2);e[0];return!!e[1]})).reduce((function(e,n){var r=Ko(n,1)[0].match(new RegExp("^".concat(t.mergedErrorIdentifier,"\\.([^.]+)$"))),a=null==r?void 0:r[1];return a?[].concat(Ro(e),[a]):e}),[])}},methods:{updateError:function(t){var e,n=null!==(e=t[this.mergedLocalizedErrorIdentifier])&&void 0!==e?e:t[this.mergedErrorIdentifier];if(Array.isArray(n))this.setError(n[0]);else if(this.errorsLocales.length>0){var r=this.errorsLocales.join(", ").toUpperCase(),a=Zt("form.validation_error.localized").replace(":locales",r);this.setError(a)}else null==n?this.clear(!1):he('FieldContainer : Not processable error "'.concat(this.mergedErrorIdentifier,'" : '),n)},setError:function(t){var e;this.state="error",this.stateMessage=t,null===(e=this.$tab)||void 0===e||e.$emit("error",this.mergedErrorIdentifier)},setOk:function(){this.state="ok",this.stateMessage=""},clear:function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];this.state="default",this.stateMessage="",t&&(this.emitClear(this.mergedErrorIdentifier),this.mergedLocalizedErrorIdentifier&&this.emitClear(this.mergedLocalizedErrorIdentifier))},emitClear:function(t){var e;null===(e=this.$tab)||void 0===e||e.$emit("clear",t),this.$form.$emit("error-cleared",t)},triggerFocus:function(){this.$set(this.fieldProps,"focused",!0)},handleBlur:function(){this.$set(this.fieldProps,"focused",!1)},handleValueChanged:function(){"error"===this.state&&this.clear()},handleLocaleChanged:function(t){this.$emit("locale-change",this.fieldKey,t)}},mounted:function(){}},Go=Object(k.a)(Ho,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpFieldContainer SharpForm__form-item",class:t.formGroupClasses,style:t.extraStyle},[n("div",{staticClass:"row"},[n("div",{staticClass:"col"},[t.showLabel?n("label",{staticClass:"SharpForm__label",on:{click:t.triggerFocus}},[t._v("\n                "+t._s(t.label)+"\n            ")]):t._e()]),t._v(" "),t.fieldProps.localized?[n("div",{staticClass:"col-auto"},[n("FieldLocaleSelect",{attrs:{locales:t.$form.locales,"current-locale":t.locale,"field-value":t.resolvedOriginalValue,"is-locale-object":t.isLocaleObject,errors:t.errorsLocales},on:{change:t.handleLocaleChanged}})],1)]:t._e()],2),t._v(" "),n("Field",t._b({ref:"field",on:{error:t.setError,ok:t.setOk,clear:t.clear,blur:t.handleBlur,"locale-change":t.handleLocaleChanged}},"Field",t.exposedProps,!1)),t._v(" "),n("div",{staticClass:"SharpForm__form-requirement"},[t._v(t._s(t.stateMessage))]),t._v(" "),n("small",{staticClass:"SharpForm__help-message"},[t._v(t._s(t.helpMessage))])],1)}),[],!1,null,null,null);Go.options.__file="FieldContainer.vue";var Wo=Go.exports,Yo=function(t){var e=t.condValues,n=t.fieldValue,r=t.isSingleSelect;if(Array.isArray(e))return r?e.some((function(t){return t==n})):e.some((function(t){return n.some((function(e){return e==t}))}));if("!"===e[0]){var a=e.substring(1);return r?a!=n:!n.some((function(t){return t==a}))}return r?e==n:n.some((function(t){return t==e}))};function Qo(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Xo(t,e,n[e])}))}return t}function Xo(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Jo(t,e){if(null==t)return{};var n,r,a=function(t,e){if(null==t)return{};var n,r,a={},i=Object.keys(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||(a[n]=t[n]);return a}(t,e);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);for(r=0;r<i.length;r++)n=i[r],e.indexOf(n)>=0||Object.prototype.propertyIsEnumerable.call(t,n)&&(a[n]=t[n])}return a}function Zo(t,e,n){return!n||function(t,e,n){var r=!0,a=n.operator;if("or"!==a&&"and"!==a)return he("Conditional display : unknown operator '".concat(a,"'"),n),!0;var i=!0,o=!1,s=void 0;try{for(var l,c=n.fields[Symbol.iterator]();!(i=(l=c.next()).done);i=!0){var u=l.value;u.key in t||(he("Conditional display : can't find a field with key '".concat(n.key,"' in 'fields'"),n),r=!0);var d=t[u.key],h=e[u.key];if("autocomplete"===d.type||"select"===d.type||"tags"===d.type?r=Yo({condValues:u.values,fieldValue:"autocomplete"===d.type?h&&h.id:h,isSingleSelect:"select"===d.type&&!d.multiple||"autocomplete"===d.type}):"check"===d.type?"boolean"!=typeof u.values?(he("Conditional display : 'values' must be a boolean for a 'check' field ('".concat(u.key,"')"),n,d),r=!0):r=!!h===u.values:(he("Conditional display : unprocessable field type '".concat(d.type,"'"),d),r=!0),"and"===a&&!r)return!1;if("or"===a&&r)return!0}}catch(t){o=!0,s=t}finally{try{i||null==c.return||c.return()}finally{if(o)throw s}}return r}(t,e,n)}var ts=function(t,e,n,r){return t.localized&&e.localized&&n&&Wn(e)?n[r]:n},es={name:"SharpFieldDisplay",functional:!0,inject:["$form"],render:function(t,e){var n=e.props,r=e.injections,a=e.data,i=n.fieldKey,o=n.contextFields,s=n.contextData,l=n.errorIdentifier,c=n.updateVisibility,u=n.readOnly,d=Jo(n,["fieldKey","contextFields","contextData","errorIdentifier","updateVisibility","readOnly"]),h=r.$form,f=o[i],p=s[i];if(!(i in o))return he("Field display ('layout') : Can't find a field with key '".concat(i,"' in 'fields'"),o),t(Vn,{props:{name:i}});var m=Zo(o,s,f.conditionalDisplay);return c&&c(i,m),m?t(Wo,Qo(Qo({},a),{},{attrs:Qo({fieldKey:i,fieldProps:Qo(Qo({},f),{},{readOnly:u||f.readOnly}),fieldType:f.type,value:ts(h,f,p,n.locale),originalValue:p,label:f.label,helpMessage:f.helpMessage,errorIdentifier:l,localizedErrorIdentifier:f.localized?"".concat(l,".").concat(n.locale):null},d)})):null}},ns={components:{Dropdown:ft,DropdownItem:vt},props:{locale:String,locales:Array},methods:{handleChanged:function(t){this.$emit("change",t)}}},rs=Object(k.a)(ns,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("Dropdown",{staticClass:"SharpForm__locale-dropdown",scopedSlots:t._u([{key:"text",fn:function(){return[t.locale?[t._v(t._s(t.locale))]:[t._v("-")]]},proxy:!0}])},[t._v(" "),t._l(t.locales,(function(e){return[n("DropdownItem",{key:e,on:{click:function(n){return t.handleChanged(e)}}},[t._v("\n            "+t._s(e)+"\n        ")])]}))],2)}),[],!1,null,null,null);rs.options.__file="LocaleSelect.vue";var as=rs.exports;function is(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){os(t,e,n[e])}))}return t}function os(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function ss(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function ls(t){return function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){ss(i,r,a,o,s,"next",t)}function s(t){ss(i,r,a,o,s,"throw",t)}o(void 0)}))}}function cs(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var us={name:"SharpForm",extends:Ze,mixins:[qe,vi("fields")],components:{TabbedLayout:An,FieldsLayout:ui,Grid:Ot,Dropdown:ft,DropdownItem:vt,LocaleSelect:as},props:{entityKey:String,instanceId:String,independant:{type:Boolean,default:!1},ignoreAuthorizations:Boolean,props:Object},provide:function(){return{$form:this}},data:function(){return{ready:!1,fields:null,authorizations:null,breadcrumb:null,config:null,errors:{},fieldLocale:{},locales:null,fieldVisible:{},uploadingFields:{},curFieldsetId:0}},computed:{apiPath:function(){var t="form/".concat(this.entityKey);return this.instanceId&&(t+="/".concat(this.instanceId)),t},localized:function(){return Array.isArray(this.locales)&&!!this.locales.length},isSingle:function(){return!!this.config&&this.config.isSingle},isCreation:function(){return!this.isSingle&&!this.instanceId},isReadOnly:function(){return!this.ignoreAuthorizations&&(this.isCreation?!this.authorizations.create:!this.authorizations.update)},synchronous:function(){return this.independant},hasErrors:function(){return Object.values(this.errors).some((function(t){return!!t}))},baseEntityKey:function(){return this.entityKey.split(":")[0]},downloadLinkBase:function(){return"/download/".concat(this.entityKey,"/").concat(this.instanceId)},listUrl:function(){return"".concat(Kt,"/list/").concat(this.baseEntityKey,"?restore-context=1")},transformedFields:function(){return gr(this.fields,this.data)},currentLocale:function(){var t,e=Object.values(this.fieldLocale).map((function(t){return Array.isArray(t)?t.map((function(t){return Object.values(t)})):t})).flat(2),n=cs(new Set(e));return n.length?1===n.length?n[0]:null:null===(t=this.locales)||void 0===t?void 0:t[0]},isUploading:function(){return Object.values(this.uploadingFields).some((function(t){return!!t}))},actionBarProps:function(){return{showSubmitButton:this.isCreation?!!this.authorizations.create:!!this.authorizations.update,showDeleteButton:!this.isCreation&&!this.isSingle&&!!this.authorizations.delete,showBackButton:this.isReadOnly,create:!!this.isCreation,uploading:this.isUploading}},actionBarListeners:function(){return{submit:this.handleSubmitClicked,delete:this.handleDeleteClicked,cancel:this.handleCancelClicked}}},methods:{updateData:function(t,e){var n=this,r=arguments;return ls(H.a.mark((function a(){var i,o;return H.a.wrap((function(a){for(;;)switch(a.prev=a.next){case 0:i=r.length>2&&void 0!==r[2]?r[2]:{},o=i.forced,n.data=is(is(is({},n.data),o?null:pr(n.fields,t,(function(t){return n.fieldLocalizedValue(t.key,null)}))),{},os({},t,n.fieldLocalizedValue(t,e)));case 2:case"end":return a.stop()}}),a)})))()},updateVisibility:function(t,e){this.$set(this.fieldVisible,t,e)},updateLocale:function(t,e){this.$set(this.fieldLocale,t,e)},handleLocaleChanged:function(t){this.fieldLocale=this.defaultFieldLocaleMap({fields:this.fields,locales:this.locales},t)},mount:function(t){var e=t.fields,n=t.layout,r=t.data,a=void 0===r?{}:r,i=t.authorizations,o=void 0===i?{}:i,s=t.locales,l=t.breadcrumb,c=t.config;this.fields=e,this.data=a,this.layout=this.patchLayout(n),this.locales=s,this.authorizations=o,this.breadcrumb=l,this.config=c,e&&(this.fieldVisible=Object.keys(this.fields).reduce((function(t,e){return t[e]=!0,t}),{}),this.fieldLocale=this.defaultFieldLocaleMap({fields:e,locales:s})),this.validate()},validate:function(){Object.keys(this.fieldLocale).length>0&&!this.locales.length&&ke("Some fields are localized but the form hasn't any locales configured",{title:"Data error",isError:!0})},handleError:function(t){var e=t.response;422===e.status&&(this.errors=e.data.errors||{})},patchLayout:function(t){if(t){var e=0,n=function t(n){n.legend?n.id="".concat(e++,"#").concat(n.legend):n.fields&&n.fields.forEach((function(e){e.forEach(t)}))};return t.tabs.forEach((function(t){return t.columns.forEach(n)})),t}},init:function(){var t=this;return ls(H.a.mark((function e(){return H.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:if(!t.independant){e.next=5;break}t.mount(t.props),t.ready=!0,e.next=12;break;case 5:if(!t.entityKey){e.next=11;break}return e.next=8,t.get();case 8:t.ready=!0,e.next=12;break;case 11:he("no entity key provided");case 12:case"end":return e.stop()}}),e)})))()},redirectToClosestRoot:function(){var t,e,n,r;location.href=(t=this.breadcrumb,e=Te(t[t.length-1].url),n=t[t.length-2].url,(r=Te(n)).params.entityKey&&e.params.entityKey!==r.params.entityKey?De(n):De(t[0].url))},redirectToParentPage:function(){location.href=Fe(this.breadcrumb)},submit:function(){var t=this,e=arguments;return ls(H.a.mark((function n(){var r,a,i;return H.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:if(r=e.length>0&&void 0!==e[0]?e[0]:{},a=r.postFn,!t.isUploading){n.next=3;break}return n.abrupt("return");case 3:if(n.prev=3,!a){n.next=10;break}return n.next=7,a(t.data);case 7:n.t0=n.sent,n.next=13;break;case 10:return n.next=12,t.post();case 12:n.t0=n.sent;case 13:if(i=n.t0,!t.independant){n.next=19;break}return t.$emit("submit",i),n.abrupt("return",i);case 19:i.data.ok&&(t.$store.dispatch("setLoading",!0),t.redirectToParentPage());case 20:n.next=26;break;case 22:return n.prev=22,n.t1=n.catch(3),t.handleError(n.t1),n.abrupt("return",Promise.reject(n.t1));case 26:case"end":return n.stop()}}),n,null,[[3,22]])})))()},handleSubmitClicked:function(){this.submit().catch((function(){}))},handleDeleteClicked:function(){var t=this;this.axiosInstance.delete(this.apiPath).then((function(){t.redirectToClosestRoot()}))},handleCancelClicked:function(){this.redirectToParentPage()},setUploading:function(t,e){this.uploadingFields=is(is({},this.uploadingFields),{},os({},t,e))},hasUploadingFields:function(t){return Object.keys(this.uploadingFields).some((function(e){return e.startsWith("".concat(t,"."))}))}},created:function(){var t=this;this.$on("error-cleared",(function(e){t.errors=is(is({},t.errors),{},os({},e,null))}))},mounted:function(){this.init()}},ds=Object(k.a)(us,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpForm",attrs:{"data-popover-boundary":""}},[t.ready?[t._t("action-bar",null,{props:t.actionBarProps,listeners:t.actionBarListeners}),t._v(" "),t.hasErrors?[n("div",{staticClass:"SharpNotification SharpNotification--error",attrs:{role:"alert"}},[n("div",{staticClass:"SharpNotification__details"},[n("div",{staticClass:"SharpNotification__text-wrapper"},[n("p",{staticClass:"SharpNotification__title"},[t._v(t._s(t.l("form.validation_error.title")))]),t._v(" "),n("p",{staticClass:"SharpNotification__subtitle"},[t._v(t._s(t.l("form.validation_error.description")))])])])])]:t._e(),t._v(" "),n("TabbedLayout",{ref:"tabbedLayout",attrs:{layout:t.layout},scopedSlots:t._u([t.localized?{key:"nav-prepend",fn:function(){return[n("LocaleSelect",{attrs:{locale:t.currentLocale,locales:t.locales},on:{change:t.handleLocaleChanged}})]},proxy:!0}:null,{key:"default",fn:function(e){var r=e.tab;return[n("Grid",{ref:"columnsGrid",attrs:{rows:[r.columns]},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.itemLayout;return[n("FieldsLayout",{ref:"fieldLayout",attrs:{layout:r.fields,visible:t.fieldVisible},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.fieldLayout;return[n("FieldDisplay",{ref:"field",attrs:{"field-key":r.key,"context-fields":t.transformedFields,"context-data":t.data,"field-layout":r,locale:t.fieldLocale[r.key],"read-only":t.isReadOnly,"error-identifier":r.key,"config-identifier":r.key,"update-data":t.updateData,"update-visibility":t.updateVisibility},on:{"locale-change":t.updateLocale}})]}}],null,!0)})]}}],null,!0)})]}}],null,!0)})]:t._e()],2)}),[],!1,null,null,null);ds.options.__file="Form.vue";var hs=ds.exports,fs={name:"SharpActionBarForm",mixins:[qe],components:{ActionBar:L,Collapse:M,Button:D},props:{showSubmitButton:Boolean,showDeleteButton:Boolean,showBackButton:Boolean,create:Boolean,uploading:Boolean},computed:{submitLabel:function(){return this.uploading?Zt("action_bar.form.submit_button.pending.upload"):this.create?Zt("action_bar.form.submit_button.create"):Zt("action_bar.form.submit_button.update")}},methods:{focusDelete:function(){this.$refs.openDelete&&this.$refs.openDelete.focus()},handleSubmitClicked:function(){this.$emit("submit")},handleDeleteClicked:function(){this.$emit("delete")},handleCancelClicked:function(){this.$emit("cancel")}}},ps=Object(k.a)(fs,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("ActionBar",{scopedSlots:t._u([{key:"left",fn:function(){return[n("Button",{attrs:{type:"secondary-accent"},on:{click:t.handleCancelClicked}},[t.showBackButton?[t._v("\n                "+t._s(t.l("action_bar.form.back_button"))+"\n            ")]:[t._v("\n                "+t._s(t.l("action_bar.form.cancel_button"))+"\n            ")]],2),t._v(" "),t.showDeleteButton?[n("div",{staticClass:"w-100 h-100"},[n("Collapse",{attrs:{"transition-class":"SharpButton__collapse-transition"},scopedSlots:t._u([{key:"frame-0",fn:function(e){var r=e.next;return[n("button",{staticClass:"SharpButton SharpButton--danger",on:{click:function(e){return r(t.focusDelete)}}},[n("svg",{attrs:{width:"16",height:"16",viewBox:"0 0 16 24","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M4 0h8v2H4zM0 3v4h1v17h14V7h1V3H0zm13 18H3V8h10v13z"}}),t._v(" "),n("path",{attrs:{d:"M5 10h2v9H5zm4 0h2v9H9z"}})])])]}},{key:"frame-1",fn:function(e){var r=e.next;return[n("Button",{ref:"openDelete",attrs:{type:"danger"},on:{click:t.handleDeleteClicked,blur:function(t){return r()}}},[t._v("\n                            "+t._s(t.l("action_bar.form.delete_button"))+"\n                        ")])]}}],null,!1,3565747488)})],1)]:t._e()]},proxy:!0},{key:"right",fn:function(){return[t.showSubmitButton?[n("Button",{attrs:{type:"accent",disabled:t.uploading},on:{click:t.handleSubmitClicked}},[t._v("\n                "+t._s(t.submitLabel)+"\n            ")])]:t._e()]},proxy:!0}])})}),[],!1,null,null,null);ps.options.__file="ActionBar.vue";var ms={components:{Form:hs,ActionBarForm:ps.exports},computed:{entityKey:function(){return this.$route.params.entityKey},instanceId:function(){return this.$route.params.instanceId}}},vs=Object(k.a)(ms,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"FormPage"},[n("div",{staticClass:"container"},[n("Form",{attrs:{"entity-key":t.entityKey,"instance-id":t.instanceId},scopedSlots:t._u([{key:"action-bar",fn:function(e){var r=e.props,a=e.listeners;return[n("ActionBarForm",t._g(t._b({},"ActionBarForm",r,!1),a))]}}])})],1)])}),[],!1,null,null,null);vs.options.__file="FormPage.vue";var ys=[{name:"form",path:"/form/:entityKey/:instanceId?",component:vs.exports}],bs={components:{Modal:ln,Form:hs},props:{form:Object},data:function(){return{visible:!1}},watch:{form:function(t){this.visible=!!t}},methods:{submit:function(){var t;return(t=this.$refs.form).submit.apply(t,arguments)},handleSubmitButtonClicked:function(t){t.preventDefault(),this.$emit("submit")},handleClosed:function(){this.$emit("close")}}},gs=Object(k.a)(bs,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("Modal",{attrs:{visible:t.visible},on:{"update:visible":function(e){t.visible=e},ok:t.handleSubmitButtonClicked,hidden:t.handleClosed}},[n("transition",[t.visible?n("Form",{ref:"form",staticStyle:{"transition-duration":"300ms"},attrs:{props:t.form,independant:"","ignore-authorizations":""}}):t._e()],1)],1)}),[],!1,null,null,null);gs.options.__file="CommandFormModal.vue";var _s=gs.exports,ws={name:"SharpCommandsDropdown",components:{Dropdown:ft,DropdownItem:vt,DropdownSeparator:gt},props:{commands:{type:Array}},computed:{commandGroups:function(){return this.commands.filter((function(t){return t.length>0}))}},methods:{handleCommandClicked:function(t){this.$emit("select",t)}}},Ss=Object(k.a)(ws,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("Dropdown",{staticClass:"SharpCommandsDropdown",scopedSlots:t._u([{key:"text",fn:function(){return[t._t("text")]},proxy:!0}],null,!0)},[t._v(" "),t._l(t.commandGroups,(function(e){return[t._l(e,(function(e){return[n("DropdownItem",{key:e.key,on:{click:function(n){return t.handleCommandClicked(e)}}},[t._v("\n                "+t._s(e.label)+"\n                "),e.description?[n("div",{staticClass:"SharpCommandsDropdown__description mt-1"},[t._v("\n                        "+t._s(e.description)+"\n                    ")])]:t._e()],2)]})),t._v(" "),n("DropdownSeparator")]}))],2)}),[],!1,null,null,null);Ss.options.__file="CommandsDropdown.vue";var Cs=Ss.exports,xs={name:"SharpViewPanel",props:{content:String},computed:{visible:function(){return!!this.content}},methods:{handleBackdropClicked:function(){this.$emit("close")}},directives:{srcdoc:{inserted:function(t,e){var n=e.value;t.contentWindow.document.write(n)}}}},Os=Object(k.a)(xs,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("div",{directives:[{name:"show",rawName:"v-show",value:this.visible,expression:"visible"}],staticClass:"SharpViewPanel__glasspane",on:{click:this.handleBackdropClicked}}),this._v(" "),e("transition",{attrs:{"enter-class":"SharpViewPanel--collapsed","enter-active-class":"SharpViewPanel--expanding","enter-to-class":"SharpViewPanel--expanded","leave-class":"SharpViewPanel--expanded","leave-active-class":"SharpViewPanel--collapsing","leave-to-class":"SharpViewPanel--collapsed"}},[this.visible?[e("div",{staticClass:"SharpViewPanel"},[e("iframe",{directives:[{name:"srcdoc",rawName:"v-srcdoc",value:this.content,expression:"content"}],attrs:{src:"about:blank",sandbox:"allow-forms allow-scripts allow-same-origin allow-popups allow-modals allow-downloads"}})])]:this._e()],2)],1)}),[],!1,null,null,null);Os.options.__file="CommandViewPanel.vue";var ks=Os.exports;var js,Ls,Ps,Es={namespaced:!0,state:{commands:null},mutations:(js={},Ls="SET_COMMANDS",Ps=function(t,e){t.commands=e},Ls in js?Object.defineProperty(js,Ls,{value:Ps,enumerable:!0,configurable:!0,writable:!0}):js[Ls]=Ps,js),getters:{forType:function(t){return function(e){return t.commands?t.commands[e]:null}}},actions:{update:function(t,e){(0,t.commit)("SET_COMMANDS",e.commands)}}};function Ds(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){$s(t,e,n[e])}))}return t}function $s(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function As(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Bs(t,e,n[e])}))}return t}function Bs(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}function Is(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=[],r=!0,a=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(r=(o=s.next()).done)&&(n.push(o.value),!e||n.length!==e);r=!0);}catch(t){a=!0,i=t}finally{try{r||null==s.return||s.return()}finally{if(a)throw i}}return n}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}var Fs,Ts=new RegExp("^".concat("filter_"));function Ms(t){return"".concat("filter_").concat(t)}function zs(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Vs(t,e,n[e])}))}return t}function Vs(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Ns={namespaced:!0,state:function(){return{filters:null,values:{}}},mutations:(Fs={},Vs(Fs,"SET_FILTERS",(function(t,e){t.filters=e})),Vs(Fs,"SET_FILTER_VALUE",(function(t,e){var n=e.key,r=e.value;a.a.set(t.values,n,r)})),Fs),getters:{value:function(t){return function(e){return t.values[e]}},filters:function(t){return t.filters||[]},values:function(t){return t.values},filter:function(t){return function(e){return(t.filters||[]).find((function(t){return t.key===e}))}},defaultValue:function(){return function(t){return(t||{}).default}},isDateRange:function(){return function(t){return"daterange"===(t||{}).type}},filterQueryKey:function(){return function(t){return Ms(t)}},getQueryParams:function(t,e){return function(t){return function(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:function(t){return t};return Object.entries(t).reduce((function(t,n){var r=Is(n,2),a=r[0],i=r[1];return As({},t,Bs({},Ms(a),e(i,a)))}),{})}(t,(function(t,n){return e.serializeValue({filter:e.filter(n),value:t})}))}},getValuesFromQuery:function(){return function(t){return function(t){return Object.entries(t||{}).filter((function(t){var e=Is(t,1)[0];return Ts.test(e)})).reduce((function(t,e){var n=Is(e,2),r=n[0],a=n[1];return As({},t,Bs({},r.replace(Ts,""),a))}),{})}(t)}},resolveFilterValue:function(t,e){return function(t){var n,r,a,i=t.filter,o=t.value;return null==o?e.defaultValue(i):i.multiple&&!Array.isArray(o)?[o]:e.isDateRange(i)?(n=se((o||"").split(".."),2),r=n[0],a=n[1],{start:r?oe()(r,"YYYYMMDD").toDate():null,end:a?oe()(a,"YYYYMMDD").toDate():null}):o}},serializeValue:function(t,e){return function(t){var n=t.filter,r=t.value;return e.isDateRange(n)?function(t){if("string"==typeof t)return t;var e=(t||{}).start,n=(t||{}).end;return e&&(e=oe()(e).format("YYYYMMDD")),n&&(n=oe()(n).format("YYYYMMDD")),e||n?"".concat(e||"","..").concat(n||""):null}(r):r}},nextValues:function(t){return function(e){var n=e.filter,r=e.value,a=t.values;return n.master&&(a=Object.keys(t.values).reduce((function(t,e){return zs({},t,Vs({},e,null))}),{})),zs({},a,Vs({},n.key,r))}},nextQuery:function(t,e){return function(t){var n=t.filter,r=t.value;return e.getQueryParams(e.nextValues({filter:n,value:r}))}}},actions:{update:function(t,e){var n=t.commit,r=t.dispatch,a=e.filters,i=e.values;return n("SET_FILTERS",a),Promise.all((a||[]).map((function(t){r("setFilterValue",{filter:t,value:(i||{})[t.key]})})))},setFilterValue:function(t,e){var n=t.commit,r=t.getters,a=e.filter,i=e.value;n("SET_FILTER_VALUE",{key:a.key,value:r.resolveFilterValue({filter:a,value:i})})}}};function Rs(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}var Ks={namespaced:!0,modules:{filters:Ns},actions:{get:function(t){return(e=H.a.mark((function e(){var n,r;return H.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.dispatch,e.next=3,Xt.get("filters").then((function(t){return t.data}));case 3:r=e.sent,n("filters/update",{filters:r.filters});case 5:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(t){Rs(i,r,a,o,s,"next",t)}function s(t){Rs(i,r,a,o,s,"throw",t)}o(void 0)}))})();var e},post:function(t,e){!function(t){if(null==t)throw new TypeError("Cannot destructure undefined")}(t);var n=e.filter,r=e.value;return function(t){var e=t.filterKey,n=t.value;return Xt.post("filters/".concat(e),{value:n})}({filterKey:n.key,value:r})}}},qs={name:"SharpFilterControl",props:{opened:Boolean,label:String,noCaret:Boolean},components:{DropdownArrow:ct},computed:{classes:function(){return{"SharpFilterControl--open":this.opened}}},methods:{handleClicked:function(){this.$emit("click")}}},Us=Object(k.a)(qs,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpFilterControl",class:t.classes,on:{click:t.handleClicked}},[n("span",{staticClass:"SharpFilterControl__text"},[t._v("\n        "+t._s(t.label)+"\n    ")]),t._v(" "),t._t("default"),t._v(" "),t.noCaret?t._e():[n("span",{staticClass:"SharpFilterControl__caret-container"},[n("DropdownArrow",{staticClass:"SharpFilterControl__caret"})],1)]],2)}),[],!1,null,null,null);Us.options.__file="FilterControl.vue";var Hs=Us.exports,Gs={name:"SharpFilterDateRange",components:{DateRange:$o,FilterControl:Hs},props:{value:{required:!0},required:Boolean,displayFormat:String,mondayFirst:Boolean,label:String},data:function(){return{opened:!1}},computed:{empty:function(){return!this.value},noCaret:function(){return!!this.value&&!this.required},classes:function(){return{"SharpFilterDateRange--empty":this.empty}}},methods:{handleClicked:function(){this.$refs.range.$refs.picker.focus()},handleInput:function(t){this.$emit("input",t)},handlePickerFocused:function(){this.opened=!0},handlePickerBlur:function(){this.opened=!1}}},Ws=Object(k.a)(Gs,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpFilterDateRange",class:t.classes},[n("FilterControl",{attrs:{opened:t.opened,label:t.label,"no-caret":t.noCaret},on:{click:t.handleClicked}},[n("DateRange",{ref:"range",staticClass:"SharpFilterDateRange__field",attrs:{value:t.value,"display-format":t.displayFormat,"monday-first":t.mondayFirst,clearable:!t.required},on:{input:t.handleInput,focus:t.handlePickerFocused,blur:t.handlePickerBlur}})],1)],1)}),[],!1,null,null,null);Ws.options.__file="FilterDateRange.vue";var Ys=Ws.exports;function Qs(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Xs={name:"SharpFilterSelect",mixins:[qe],components:{Select:ji,Autocomplete:Cr,FilterControl:Hs},props:{label:{type:String,required:!0},values:{type:Array,required:!0},value:{type:[String,Number,Array]},multiple:Boolean,required:Boolean,searchable:Boolean,searchKeys:Array,template:String},data:function(){return{opened:!1,debug:!1}},computed:{optionById:function(){return this.values.reduce((function(t,e){return function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Qs(t,e,n[e])}))}return t}({},t,Qs({},e.id,e))}),{})},empty:function(){return null==this.value||this.multiple&&!this.value.length},autocompleteValue:function(){var t=this;return this.multiple?(this.value||[]).map((function(e){return t.optionById[e]})):this.optionById[this.value]}},methods:{handleSelect:function(t){this.$emit("input",t)},handleAutocompleteInput:function(t){this.$emit("input",this.multiple?t.map((function(t){return t.id})):(t||{}).id)},handleClick:function(){this.opened?this.close():this.open()},open:function(){this.opened=!0,this.$emit("open"),this.$nextTick(this.showDropdown)},close:function(){this.debug||(this.opened=!1,this.$emit("close"),this.$nextTick(this.blur))},showDropdown:function(){var t=this.$refs.autocomplete.$refs.multiselect;t.activate(),this.debug&&(this.unwatch&&this.unwatch(),this.unwatch=t.$watch("isOpen",(function(t){t||(this.isOpen=!0)}),{sync:!0}))},blur:function(){this.$refs.select.$refs.multiselect.deactivate()}}},Js=Object(k.a)(Xs,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpFilterSelect",class:{"SharpFilterSelect--open":t.opened,"SharpFilterSelect--empty":t.empty,"SharpFilterSelect--multiple":t.multiple,"SharpFilterSelect--searchable":t.searchable},attrs:{tabindex:"0"}},[n("Autocomplete",{ref:"autocomplete",staticClass:"SharpFilterSelect__select",attrs:{value:t.autocompleteValue,"local-values":t.values,"search-keys":t.searchKeys,"list-item-template":t.template,placeholder:t.l("entity_list.filter.search_placeholder"),multiple:t.multiple,"hide-selected":t.multiple,"allow-empty":!t.required,"preserve-search":!1,"show-pointer":!1,searchable:t.searchable,"no-result-item":"",mode:"local"},on:{"multiselect-input":t.handleAutocompleteInput,close:t.close}}),t._v(" "),n("FilterControl",{attrs:{label:t.label,"no-caret":""},on:{click:t.handleClick}},[n("Select",{ref:"select",staticClass:"SharpFilterSelect__select",attrs:{value:t.value,options:t.values,multiple:t.multiple,clearable:!t.required,inline:!1,placeholder:" "},on:{input:t.handleSelect}})],1)],1)}),[],!1,null,null,null);Js.options.__file="FilterSelect.vue";var Zs=Js.exports;var tl={name:"SharpFilter",props:{filter:{type:Object,required:!0},value:[Object,Array,String,Number]},computed:{filterComp:function(){return"select"===(t=this.filter.type)?Zs:"daterange"===t?Ys:void 0;var t}},methods:{handleInput:function(t){this.$emit("input",t)}}},el=Object(k.a)(tl,(function(){var t=this.$createElement;return(this._self._c||t)(this.filterComp,this._b({tag:"component",attrs:{value:this.value},on:{input:this.handleInput}},"component",this.filter,!1))}),[],!1,null,null,null);el.options.__file="Filter.vue";var nl=el.exports;function rl(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function al(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var il={components:{FilterSelect:Zs},computed:function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){al(t,e,n[e])}))}return t}({},Object(i.b)("global-filters",{filters:"filters/filters",filterValue:"filters/value"})),methods:{handleFilterChanged:function(t,e){var n=this;this.$store.dispatch("global-filters/post",{filter:t,value:e}).then((function(){n.$store.dispatch("setLoading",!0),location.href=Kt}))},handleOpened:function(){this.$emit("open")},handleClosed:function(){this.$emit("close")},updateLayout:Ge()((function(){var t=this;rl(this.$el.querySelectorAll(".SharpFilterSelect")).forEach((function(e){var n=t.$el.parentElement,r=n.offsetHeight-e.offsetHeight-(e.offsetTop-n.offsetTop);e.querySelector(".SharpAutocomplete .multiselect__content").style.height="".concat(r,"px")}))}),300)},mounted:function(){this.updateLayout(),window.addEventListener("resize",this.updateLayout)},destroyed:function(){window.removeEventListener("resize",this.updateLayout)}},ol=Object(k.a)(il,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpGlobalFilters"},[t._l(t.filters,(function(e){return[n("FilterSelect",{key:e.key,attrs:{label:e.label,values:e.values,value:t.filterValue(e.key),multiple:e.multiple,required:e.required,template:e.template,"search-keys":e.searchKeys,searchable:e.searchable},on:{input:function(n){return t.handleFilterChanged(e,n)},open:function(n){return t.handleOpened(e)},close:function(n){return t.handleClosed(e)}}})]}))],2)}),[],!1,null,null,null);ol.options.__file="GlobalFilters.vue";var sl,ll=ol.exports;function cl(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function ul(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var dl={namespaced:!0,modules:{filters:Ns,commands:Es},state:{dashboardKey:null,data:null,widgets:null,config:null,layout:null},mutations:(sl={},ul(sl,"UPDATE",(function(t,e){var n=e.data,r=e.layout,a=e.widgets,i=e.config;t.data=n,t.widgets=a,t.layout=r,t.config=i})),ul(sl,"SET_DASHBOARD_KEY",(function(t,e){t.dashboardKey=e})),sl),actions:{update:function(t,e){var n=t.commit,r=t.dispatch,a=e.data,i=e.widgets,o=e.layout,s=e.config,l=e.filtersValues;return n("UPDATE",{data:a,widgets:i,layout:o,config:s}),Promise.all([r("filters/update",{filters:s.filters,values:l}),r("commands/update",{commands:s.commands})])},get:function(t,e){return(n=H.a.mark((function n(){var r,a,i,o,s;return H.a.wrap((function(n){for(;;)switch(n.prev=n.next){case 0:return r=t.state,a=t.dispatch,i=t.getters,o=e.filtersValues,n.next=4,l={dashboardKey:r.dashboardKey,filters:i["filters/getQueryParams"](o)},c=void 0,u=void 0,c=l.dashboardKey,u=l.filters,Xt.get("dashboard/".concat(c),{params:Ds({},u)}).then((function(t){return t.data}));case 4:return s=n.sent,n.next=7,a("update",{data:s.data,widgets:s.widgets,layout:s.layout,config:s.config,filtersValues:o});case 7:case"end":return n.stop()}var l,c,u}),n)})),function(){var t=this,e=arguments;return new Promise((function(r,a){var i=n.apply(t,e);function o(t){cl(i,r,a,o,s,"next",t)}function s(t){cl(i,r,a,o,s,"throw",t)}o(void 0)}))})();var n},postCommand:function(t,e){var n=t.state,r=e.command,a=e.data,i=e.query;return function(t){var e=t.dashboardKey,n=t.commandKey,r=t.query,a=t.data;return Xt.post("dashboard/".concat(e,"/command/").concat(n),{query:r,data:a},{responseType:"blob"})}({dashboardKey:n.dashboardKey,commandKey:r.key,data:a,query:i})},getCommandFormData:function(t,e){var n=t.state,r=e.command,a=e.query;return function(t){var e=t.dashboardKey,n=t.commandKey,r=t.query;return Xt.get("dashboard/".concat(e,"/command/").concat(n,"/data"),{params:Ds({},r)}).then((function(t){return t.data.data}))}({dashboardKey:n.dashboardKey,commandKey:r.key,query:a})},setDashboardKey:function(t,e){(0,t.commit)("SET_DASHBOARD_KEY",e)}}},hl={name:"SharpWidgetPanel",components:{TemplateRenderer:Tn},props:{value:Object,template:String}},fl=Object(k.a)(hl,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"SharpWidgetPanel"},[e("TemplateRenderer",{attrs:{name:"WidgetPanel",template:this.template,"template-data":this.value.data}})],1)}),[],!1,null,null,null);fl.options.__file="Panel.vue";var pl=fl.exports,ml={name:"SharpWidgetOrderedList",components:{DataList:ot,DataListRow:Z},props:{value:Object,withCounts:{type:Boolean,default:!0},title:String},computed:{items:function(){return this.value.data},columns:function(){return[{key:"label",size:12,sizeXS:12}]}},methods:{hasCount:function(t){return this.withCounts&&"number"==typeof t.count}}},vl=Object(k.a)(ml,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpWidgetOrderedList w-100"},[n("h2",{staticClass:"mb-3"},[t._v("\n        "+t._s(t.title)+"\n    ")]),t._v(" "),n("DataList",{staticClass:"SharpWidgetOrderedList__list",attrs:{items:t.items,columns:t.columns,"hide-header":""},scopedSlots:t._u([{key:"item",fn:function(e){var r=e.item;return[n("DataListRow",{attrs:{url:r.url,columns:t.columns,row:r},scopedSlots:t._u([t.hasCount(r)?{key:"append",fn:function(){return[n("span",{staticClass:"SharpTag SharpTag--default"},[t._v(t._s(r.count))])]},proxy:!0}:null],null,!0)})]}}])})],1)}),[],!1,null,null,null);vl.options.__file="OrderedList.vue";var yl=vl.exports,bl=n("QkVN"),gl=n.n(bl),_l=n("EyHm"),wl=n.n(_l),Sl=n("MjxK"),Cl=n("zkkH"),xl=n("+hEb"),Ol=n("BZIA"),kl=n("mCHd");function jl(){return{chart:{toolbar:{show:!1,tools:{pan:!1,zoom:!0,download:!1}},locales:[kl,Sl,Cl,xl,Ol],defaultLocale:document.documentElement.lang},legend:{showForSingleSeries:!0},tooltip:{y:{title:{formatter:function(t,e){var n=e.seriesIndex;return t!=="series-".concat(n+1)?"".concat(t,":"):""}}}}}}function Ll(t){var e,n;return!(null!==(e=null===(n=t.legend)||void 0===n?void 0:n.show)&&void 0!==e&&!e)}function Pl(t){var e=document.createElement("canvas").getContext("2d");return t.map((function(t){return e.fillStyle=t,e.fillStyle}))}var El={components:{ApexChart:wl.a},props:{chartData:Object,options:Object},computed:{hasLegends:function(){return Ll(this.chartOptions)},chartOptions:function(){return gl()({},jl(),{colors:this.chartData.colors,labels:this.chartData.labels,legend:{position:"bottom"},stroke:{width:4},dataLabels:{enabled:!1}},this.options)}}},Dl=Object(k.a)(El,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",{staticClass:"mt-2",class:{"mb-2":this.hasLegends}},[e("ApexChart",{staticClass:"apexchart",attrs:{type:"area",series:this.chartData.series,options:this.chartOptions,height:this.options.chart.height}})],1)}),[],!1,null,null,null);Dl.options.__file="Line.vue";var $l=Dl.exports;var Al={components:{ApexChart:wl.a},props:{chartData:Object,options:Object},computed:{hasLegends:function(){return Ll(this.chartOptions)},chartOptions:function(){return gl()({},jl(),{legend:{position:"bottom"},labels:this.chartData.labels,colors:this.chartData.colors,grid:{padding:{right:12}}},this.options)}}},Bl=Object(k.a)(Al,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",{class:{"mb-2":this.hasLegends}},[e("ApexChart",{staticClass:"apexchart",attrs:{type:"bar",series:this.chartData.series,options:this.chartOptions,height:this.options.chart.height}})],1)}),[],!1,null,null,null);Bl.options.__file="Bar.vue";var Il=Bl.exports;var Fl={components:{ApexChart:wl.a},props:{chartData:Object,options:Object},computed:{chartOptions:function(){return gl()({},jl(),{colors:this.chartData.colors,labels:this.chartData.labels,legend:{position:"right"}},this.options)}}},Tl=Object(k.a)(Fl,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",[e("ApexChart",{attrs:{type:"pie",series:this.chartData.series,options:this.chartOptions,height:this.options.chart.height}})],1)}),[],!1,null,null,null);Tl.options.__file="Pie.vue";var Ml=Tl.exports;function zl(t,e){return"line"===t?(i=null!==(r=null==(n=e)?void 0:n.datasets)&&void 0!==r?r:[],o=null!==(a=null==n?void 0:n.labels)&&void 0!==a?a:[],{series:i.map((function(t){return{data:t.data,name:t.label}})),colors:Pl(i.map((function(t){return t.color}))),labels:o}):"bar"===t?function(t){var e,n,r=null!==(e=null==t?void 0:t.datasets)&&void 0!==e?e:[],a=null!==(n=null==t?void 0:t.labels)&&void 0!==n?n:[];return{series:r.map((function(t){return{data:t.data,name:t.label}})),colors:Pl(r.map((function(t){return t.color}))),labels:a}}(e):"pie"===t?function(t){var e,n=(null!==(e=null==t?void 0:t.datasets)&&void 0!==e?e:[]).filter((function(t){var e;return(null===(e=t.data)||void 0===e?void 0:e.length)>0}));return{series:n.map((function(t){return t.data[0]})),colors:Pl(n.map((function(t){return t.color}))),labels:n.map((function(t){var e;return null!==(e=t.label)&&void 0!==e?e:""}))}}(e):void 0;var n,r,a,i,o}function Vl(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}var Nl={name:"SharpWidgetChart",props:{display:String,title:String,value:Object,ratioX:Number,ratioY:Number,height:Number,minimal:Boolean,showLegend:{type:Boolean,default:!0},dateLabels:Boolean,options:Object},data:function(){return{resizing:!0,zoomed:!1}},computed:{chartComp:function(){return"line"===(t=this.display)?$l:"bar"===t?Il:"pie"===t?Ml:void 0;var t},chartData:function(){return zl(this.display,this.value)},chartOptions:function(){var t,e,n,r,a,i=this;return{chart:{toolbar:{show:this.zoomed},height:null!==(t=this.height)&&void 0!==t?t:"100%",sparkline:{enabled:this.minimal},parentHeightOffset:0,redrawOnParentResize:!1,events:{updated:function(){i.resizing=!1},zoomed:function(){i.zoomed=!0}},redrawOnWindowResize:function(){return i.resizing=!0,!0}},xaxis:{type:!(null===(e=this.options)||void 0===e?void 0:e.horizontal)&&this.dateLabels?"datetime":"category"},plotOptions:{bar:{horizontal:!!(null===(n=this.options)||void 0===n?void 0:n.horizontal)}},legend:{show:this.showLegend&&!this.minimal},stroke:{curve:null===(r=null===(a=this.options)||void 0===a?void 0:a.curved)||void 0===r||r?"smooth":"straight"}}},hasRatio:function(){return!this.height},classes:function(){return["SharpWidgetChart--".concat(this.display),{"SharpWidgetChart--aspect-ratio":this.hasRatio,"SharpWidgetChart--resizing":this.resizing}]},sizerStyle:function(){return{"padding-bottom":"".concat(this.ratioY/this.ratioX*100,"%")}}},mounted:function(){var t,e=this;return(t=H.a.mark((function t(){return H.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$nextTick();case 2:e.resizing=!1;case 3:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){Vl(i,r,a,o,s,"next",t)}function s(t){Vl(i,r,a,o,s,"throw",t)}o(void 0)}))})()}},Rl=Object(k.a)(Nl,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[t.title?[n("h2",{staticClass:"my-2"},[t._v(t._s(t.title))])]:t._e(),t._v(" "),n("div",{ref:"chart",staticClass:"SharpWidgetChart",class:t.classes},[t.hasRatio?[n("div",{staticClass:"SharpWidgetChart__sizer",style:t.sizerStyle})]:t._e(),t._v(" "),n(t.chartComp,{tag:"component",staticClass:"SharpWidgetChart__inner",attrs:{"chart-data":t.chartData,options:t.chartOptions}})],2)],2)}),[],!1,null,null,null);Rl.options.__file="Chart.vue";var Kl=Rl.exports;function ql(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Ul={name:"SharpWidget",props:{widgetType:String,widgetProps:Object,value:Object},computed:{widgetComp:function(){return t=this.widgetType,this.widgetProps.display,"graph"===t?Kl:"panel"===t?pl:"list"===t?yl:void 0;var t},exposedProps:function(){return function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){ql(t,e,n[e])}))}return t}({},this.widgetProps,{value:this.value})},hasLink:function(){return!!this.widgetProps.link}}},Hl=Object(k.a)(Ul,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("article",{staticClass:"SharpWidget SharpCard",class:{"SharpWidget--chart":"graph"===t.widgetType,"SharpWidget--link":t.hasLink}},[n(t.hasLink?"a":"div",{tag:"component",class:{SharpWidget__link:t.hasLink},attrs:{href:t.widgetProps.link}},[n("div",{staticClass:"SharpCard__overview"},[n("div",{staticClass:"SharpCard__overview-about"},[n(t.widgetComp,t._b({tag:"component"},"component",t.exposedProps,!1))],1)])])],1)}),[],!1,null,null,null);Hl.options.__file="Widget.vue";var Gl=Hl.exports;function Wl(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Yl(t,e,n[e])}))}return t}function Yl(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Ql={name:"SharpActionBarDashboard",mixins:[qe],components:{ActionBar:L,FilterDropdown:nl,CommandsDropdown:Cs},props:{commands:Array},computed:Wl({},Object(i.b)("dashboard",{filters:"filters/filters",filterValue:"filters/value",filterNextQuery:"filters/nextQuery"})),methods:{filterKey:function(t){return"actionbardashboard_".concat(t.key)},handleFilterChanged:function(t,e){this.$router.push({query:Wl({},this.$route.query,{},this.filterNextQuery({filter:t,value:e}))})},handleCommandSelected:function(t){this.$emit("command",t)}}},Xl=Object(k.a)(Ql,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("ActionBar",{scopedSlots:t._u([{key:"extras",fn:function(){return[n("div",{staticClass:"row mx-n2"},[t._l(t.filters,(function(e){return[n("div",{staticClass:"col-auto px-2"},[n("FilterDropdown",{key:e.id,attrs:{filter:e,value:t.filterValue(e.key)},on:{input:function(n){return t.handleFilterChanged(e,n)}}})],1)]}))],2)]},proxy:!0},t.commands.length?{key:"extras-right",fn:function(){return[n("CommandsDropdown",{staticClass:"SharpActionBar__actions-dropdown SharpActionBar__actions-dropdown--commands",attrs:{commands:t.commands},on:{select:t.handleCommandSelected},scopedSlots:t._u([{key:"text",fn:function(){return[t._v("\n                "+t._s(t.l("dashboard.commands.dashboard.label"))+"\n            ")]},proxy:!0}],null,!1,3867860828)})]},proxy:!0}:null],null,!0)})}),[],!1,null,null,null);function Jl(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function Zl(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){tc(t,e,n[e])}))}return t}function tc(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}Xl.options.__file="ActionBar.vue";var ec={name:"SharpDashboardPage",mixins:[Je,an],components:{Grid:Ot,Widget:Gl,ActionBarDashboard:Xl.exports,CommandFormModal:_s,CommandViewPanel:ks},data:function(){return{ready:!1}},watch:{$route:"init"},computed:Zl({},Object(i.c)("dashboard",{data:function(t){return t.data},widgets:function(t){return t.widgets},layout:function(t){return t.layout}}),{},Object(i.b)("dashboard",{filtersValues:"filters/values",getFiltersQueryParams:"filters/getQueryParams",getFiltersValuesFromQuery:"filters/getValuesFromQuery",commandsForType:"commands/forType"}),{commands:function(){return this.commandsForType("dashboard")||[]},commandsQuery:function(){return Zl({},this.getFiltersQueryParams(this.filtersValues),{},this.$route.query)}}),methods:{handleCommandRequested:function(t){var e=this,n=this.commandsQuery;this.sendCommand(t,{postCommand:function(){return e.$store.dispatch("dashboard/postCommand",{command:t,query:n})},postForm:function(r){return e.$store.dispatch("dashboard/postCommand",{command:t,query:n,data:r})},getFormData:function(){return e.$store.dispatch("dashboard/getCommandFormData",{command:t,query:n})}})},init:function(){var t,e=this;return(t=H.a.mark((function t(){return H.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$store.dispatch("dashboard/setDashboardKey",e.$route.params.id);case 2:return t.next=4,e.$store.dispatch("dashboard/get",{filtersValues:e.getFiltersValuesFromQuery(e.$route.query)});case 4:e.ready=!0;case 5:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){Jl(i,r,a,o,s,"next",t)}function s(t){Jl(i,r,a,o,s,"throw",t)}o(void 0)}))})()}},created:function(){this.init()}},nc=Object(k.a)(ec,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpDashboardPage"},[t.ready?[n("div",{staticClass:"container"},[n("ActionBarDashboard",{attrs:{commands:t.commands},on:{command:t.handleCommandRequested}}),t._v(" "),n("Grid",{attrs:{rows:t.layout.rows},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.itemLayout;return[n("Widget",{attrs:{"widget-type":t.widgets[r.key].type,"widget-props":t.widgets[r.key],value:t.data[r.key]}})]}}],null,!1,1993281933)})],1)]:t._e(),t._v(" "),n("CommandFormModal",{ref:"commandForm",attrs:{form:t.commandCurrentForm}}),t._v(" "),n("CommandViewPanel",{attrs:{content:t.commandViewContent},on:{close:t.handleCommandViewPanelClosed}})],2)}),[],!1,null,null,null);nc.options.__file="DashboardPage.vue";var rc,ac=[{name:"dashboard",path:"/dashboard/:id",component:nc.exports}];function ic(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var oc={namespaced:!0,modules:{filters:Ns},state:function(){return{entityKey:null,query:{}}},mutations:(rc={},ic(rc,"SET_ENTITY_KEY",(function(t,e){t.entityKey=e})),ic(rc,"SET_QUERY",(function(t,e){t.query=e})),rc),getters:{query:function(t){return t.query}},actions:{update:function(t,e){var n=t.dispatch,r=(e.data,e.layout,e.config),a=e.filtersValues;return Promise.all([n("filters/update",{filters:r.filters,values:a})])},reorder:function(t,e){var n=t.state,r=e.instances;return function(t){var e=t.entityKey,n=t.instances;return Xt.post("list/".concat(e,"/reorder"),{instances:n})}({entityKey:n.entityKey,instances:r})},setEntityKey:function(t,e){(0,t.commit)("SET_ENTITY_KEY",e)},setQuery:function(t,e){(0,t.commit)("SET_QUERY",e)}}},sc=n("Y+p1"),lc=n.n(sc);function cc(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function uc(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function dc(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){hc(t,e,n[e])}))}return t}function hc(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var fc={name:"SharpEntityList",mixins:[Ze,qe,an],components:{DataList:ot,DataListRow:Z,StateIcon:_n,CommandsDropdown:Cs,Dropdown:ft,DropdownItem:vt,CommandFormModal:_s,CommandViewPanel:ks},props:{entityKey:String,module:String,inline:Boolean,showCreateButton:{type:Boolean,default:!0},showReorderButton:{type:Boolean,default:!0},showSearchField:{type:Boolean,default:!0},showEntityState:{type:Boolean,default:!0},hiddenCommands:Object,hiddenFilters:Object},data:function(){return{ready:!1,page:0,search:"",sortedBy:null,sortDir:null,sortDirs:{},reorderActive:!1,reorderedItems:null,containers:null,layout:null,data:null,config:null,authorizations:null,forms:null}},watch:{query:function(t,e){lc()(t,e)||this.init()}},computed:{filters:function(){return this.storeGetter("filters/filters")},filtersValues:function(){return this.storeGetter("filters/values")},filterNextQuery:function(){return this.storeGetter("filters/nextQuery")},getFiltersValuesFromQuery:function(){return this.storeGetter("filters/getValuesFromQuery")},query:function(){return this.storeGetter("query")},commandsQuery:function(){return dc({},this.storeGetter("filters/getQueryParams")(this.filtersValues),{},this.query)},hasMultiforms:function(){return!!this.forms},hasShowPage:function(){return!!this.config.hasShowPage},apiParams:function(){return this.query},apiPath:function(){return"list/".concat(this.entityKey)},actionBarProps:function(){return{count:this.totalCount,search:this.search,filters:this.visibleFilters,filtersValues:this.filtersValues,commands:this.allowedEntityCommands,forms:this.multiforms,reorderActive:this.reorderActive,canCreate:this.canCreate,canReorder:this.canReorder,canSearch:this.canSearch}},actionBarListeners:function(){return{"search-change":this.handleSearchChanged,"search-submit":this.handleSearchSubmitted,"filter-change":this.handleFilterChanged,"reorder-click":this.handleReorderButtonClicked,"reorder-submit":this.handleReorderSubmitted,command:this.handleEntityCommandRequested,create:this.handleCreateButtonClicked}},allowedEntityCommands:function(){var t=this;return(this.config.commands.entity||[]).map((function(e){return e.filter((function(e){return t.isEntityCommandAllowed(e)}))}))},visibleFilters:function(){var t=this;return this.hiddenFilters?this.filters.filter((function(e){return!(e.key in t.hiddenFilters)})):this.filters},multiforms:function(){return this.forms?Object.values(this.forms):null},canCreate:function(){return this.showCreateButton&&!!this.authorizations.create},canReorder:function(){return this.showReorderButton&&this.config.reorderable&&this.authorizations.update&&this.data.items.length>1},canSearch:function(){return this.showSearchField&&!!this.config.searchable},items:function(){return this.data.items||[]},columns:function(){var t=this;return this.layout.map((function(e){return dc({},e,{},t.containers[e.key])}))},paginated:function(){return!!this.config.paginated},totalCount:function(){return this.data.totalCount||this.items.length},pageSize:function(){return this.data.pageSize},hasActionsColumn:function(){var t=this;return this.items.some((function(e){return t.instanceHasState(e)||t.instanceHasCommands(e)}))}},methods:{storeGetter:function(t){return this.$store.getters["".concat(this.module,"/").concat(t)]},storeDispatch:function(t,e){return this.$store.dispatch("".concat(this.module,"/").concat(t),e)},handleSearchChanged:function(t){this.search=t},handleSearchSubmitted:function(){this.storeDispatch("setQuery",dc({},this.query,{search:this.search,page:1}))},handleFilterChanged:function(t,e){this.storeDispatch("setQuery",dc({},this.query,{},this.filterNextQuery({filter:t,value:e}),{page:1}))},handleReorderButtonClicked:function(){this.reorderActive=!this.reorderActive,this.reorderedItems=this.reorderActive?uc(this.data.items):null},handleReorderSubmitted:function(){var t=this;return this.storeDispatch("reorder",{instances:this.reorderedItems.map((function(e){return t.instanceId(e)}))}).then((function(){t.$set(t.data,"items",uc(t.reorderedItems)),t.reorderedItems=null,t.reorderActive=!1}))},handleEntityCommandRequested:function(t){this.handleCommandRequested(t,{endpoint:this.commandEndpoint(t.key)})},handleCreateButtonClicked:function(t){var e=t?this.formUrl({formKey:t.key}):this.formUrl();location.href=e},instanceId:function(t){var e=this.config.instanceIdAttribute;return e?t[e]:t.id},instanceState:function(t){if(!this.instanceHasState(t))return null;var e=this.config.state.attribute;return e?t[e]:t.state},instanceHasState:function(t){return!!this.config.state&&this.showEntityState},instanceHasCommands:function(t){return this.instanceCommands(t).flat().length>0},instanceHasStateAuthorization:function(t){if(!this.instanceHasState(t))return!1;var e=this.config.state.authorization,n=this.instanceId(t);return Array.isArray(e)?e.includes(n):!!e},instanceCommands:function(t){var e=this;return(this.config.commands.instance||[]).reduce((function(n,r){return[].concat(uc(n),[r.filter((function(n){return e.isInstanceCommandAllowed(t,n)}))])}),[])},instanceStateIconColor:function(t){var e=this.instanceState(t);return this.instanceStateOptions(e).color},instanceStateLabel:function(t){var e=this.instanceState(t);return this.instanceStateOptions(e).label},instanceStateOptions:function(t){return this.config.state?this.config.state.values.find((function(e){return e.value===t})):null},instanceForm:function(t){var e=this.instanceId(t);return this.multiforms.find((function(t){return t.instances.includes(e)}))},instanceUrl:function(t){var e=this.instanceId(t);if(!this.instanceHasViewAuthorization(t))return null;if(this.hasShowPage)return this.showUrl({instanceId:e});if(this.hasMultiforms){var n=this.instanceForm(t)||{};return this.formUrl({formKey:n.key,instanceId:e})}return this.formUrl({instanceId:e})},instanceHasViewAuthorization:function(t){var e=this.instanceId(t),n=this.authorizations.view;return Array.isArray(n)?n.includes(e):!!n},filterByKey:function(t){return(this.config.filters||[]).find((function(e){return e.key===t}))},setState:function(t,e){var n=this,r=this.instanceId(t);return this.axiosInstance.post("".concat(this.apiPath,"/state/").concat(r),{attribute:this.config.state.attribute,value:e}).then((function(t){var e=t.data;n.handleCommandActionRequested(e.action,e)})).catch((function(t){var e=t.response.data;422===t.response.status&&ke(e.message,{title:Zt("modals.state.422.title"),isError:!0})}))},handleInstanceStateChanged:function(t,e){this.setState(t,e)},handleInstanceCommandRequested:function(t,e){var n=this.instanceId(t);this.handleCommandRequested(e,{endpoint:this.commandEndpoint(e.key,n)})},handleSortChanged:function(t){var e=t.prop,n=t.dir;this.storeDispatch("setQuery",dc({},this.query,{page:1,sort:e,dir:n}))},handleReorderedItemsChanged:function(t){this.reorderedItems=t},handlePageChanged:function(t){this.storeDispatch("setQuery",dc({},this.query,{page:t}))},formUrl:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.formKey,n=t.instanceId;return Re({entityKey:e?"".concat(this.entityKey,":").concat(e):this.entityKey,instanceId:n})},showUrl:function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},e=t.instanceId;return Ke({entityKey:this.entityKey,instanceId:e})},tryParseNumber:function(t){if(Array.isArray(t))return t.map(this.tryParseNumber);var e=Number(t);return isNaN(Number(e))?t:e},filterValueOrDefault:function(t,e){return null!=t&&""!==t?this.tryParseNumber(t):e.default||(e.multiple?[]:null)},initCommands:function(){this.addCommandActionHandlers({refresh:this.handleRefreshCommand})},handleCommandRequested:function(t,e){var n=this,r=e.endpoint,a=this.commandsQuery;this.sendCommand(t,{postCommand:function(){return n.axiosInstance.post(r,{query:a},{responseType:"blob"})},postForm:function(t){return n.axiosInstance.post(r,{query:a,data:t},{responseType:"blob"})},getFormData:function(){return n.axiosInstance.get("".concat(r,"/data"),{params:a}).then((function(t){return t.data.data}))}})},handleRefreshCommand:function(t){var e=this;this.data.items=this.data.items.map((function(n){return r=t.items,a=n,r.find((function(t){return e.instanceId(a)===e.instanceId(t)}))||n;var r,a}))},commandEndpoint:function(t,e){return"".concat(this.apiPath,"/command/").concat(t).concat(e?"/".concat(e):"")},isEntityCommandAllowed:function(t){var e=this.hiddenCommands?this.hiddenCommands.entity:null;return!!t.authorization&&!(e||[]).includes(t.key)},isInstanceCommandAllowed:function(t,e){var n=this.instanceId(t),r=this.hiddenCommands?this.hiddenCommands.instance:null;return(Array.isArray(e.authorization)?e.authorization.includes(n):!!e.authorization)&&!(r||[]).includes(e.key)},mount:function(t){var e=t.containers,n=t.layout,r=t.data,a=void 0===r?{}:r,i=t.config,o=void 0===i?{}:i,s=t.authorizations,l=t.forms;this.containers=e,this.layout=n,this.data=a,this.config=o,this.authorizations=s,this.forms=l,this.config.commands=o.commands||{},this.config.filters=o.filters||[],this.page=this.data.page,!this.sortDir&&(this.sortDir=this.config.defaultSortDir),!this.sortedBy&&(this.sortedBy=this.config.defaultSort)},bindParams:function(t){var e=t.search,n=t.page,r=t.sort,a=t.dir;this.search=e,n&&(this.page=Number(n)),r&&(this.sortedBy=r),a&&(this.sortDir=a)},init:function(){var t,e=this;return(t=H.a.mark((function t(){return H.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return console.log(e.entityKey,e.query),t.next=3,e.storeDispatch("setEntityKey",e.entityKey);case 3:return t.next=5,e.get();case 5:return e.bindParams(e.query),e.$emit("change",{data:e.data,layout:e.layout,config:e.config,containers:e.containers,authorizations:e.authorizations,forms:e.forms}),t.next=9,e.storeDispatch("update",{config:e.config,filtersValues:e.getFiltersValuesFromQuery(e.query)});case 9:e.ready=!0;case 10:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){cc(i,r,a,o,s,"next",t)}function s(t){cc(i,r,a,o,s,"throw",t)}o(void 0)}))})()}},beforeMount:function(){this.init(),this.initCommands()}},pc=Object(k.a)(fc,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpEntityList"},[t.ready?[t._t("action-bar",null,{props:t.actionBarProps,listeners:t.actionBarListeners}),t._v(" "),n("DataList",{attrs:{items:t.items,columns:t.columns,page:t.page,paginated:t.paginated,"total-count":t.totalCount,"page-size":t.pageSize,"reorder-active":t.reorderActive,sort:t.sortedBy,dir:t.sortDir},on:{change:t.handleReorderedItemsChanged,"sort-change":t.handleSortChanged,"page-change":t.handlePageChanged},scopedSlots:t._u([{key:"empty",fn:function(){return[t._v("\n                "+t._s(t.l("entity_list.empty_text"))+"\n            ")]},proxy:!0},{key:"item",fn:function(e){var r=e.item;return[n("DataListRow",{attrs:{url:t.instanceUrl(r),columns:t.columns,row:r},scopedSlots:t._u([t.hasActionsColumn?{key:"append",fn:function(){return[n("div",{staticClass:"row justify-content-end justify-content-md-start mx-n2"},[t.instanceHasState(r)?[n("div",{staticClass:"col-auto col-md-12 my-1 px-2"},[n("Dropdown",{staticClass:"SharpEntityList__state-dropdown",attrs:{disabled:!t.instanceHasStateAuthorization(r)},scopedSlots:t._u([{key:"text",fn:function(){return[n("StateIcon",{attrs:{color:t.instanceStateIconColor(r)}}),t._v(" "),n("span",{staticClass:"text-truncate"},[t._v("\n                                                "+t._s(t.instanceStateLabel(r))+"\n                                            ")])]},proxy:!0}],null,!0)},[t._v(" "),t._l(t.config.state.values,(function(e){return n("DropdownItem",{key:e.value,on:{click:function(n){return t.handleInstanceStateChanged(r,e.value)}}},[n("StateIcon",{attrs:{color:e.color}}),t._v(" \n                                            "+t._s(e.label)+"\n                                        ")],1)}))],2)],1)]:t._e(),t._v(" "),t.instanceHasCommands(r)?[n("div",{staticClass:"col-auto col-md-12 my-1 px-2"},[n("CommandsDropdown",{staticClass:"SharpEntityList__commands-dropdown",attrs:{commands:t.instanceCommands(r)},on:{select:function(e){return t.handleInstanceCommandRequested(r,e)}},scopedSlots:t._u([{key:"text",fn:function(){return[t._v("\n                                            "+t._s(t.l("entity_list.commands.instance.label"))+"\n                                        ")]},proxy:!0}],null,!0)})],1)]:t._e()],2)]},proxy:!0}:null],null,!0)})]}},{key:"append-head",fn:function(){return[t._t("append-head",null,{props:t.actionBarProps,listeners:t.actionBarListeners})]},proxy:!0}],null,!0)})]:t._e(),t._v(" "),n("CommandFormModal",{ref:"commandForm",attrs:{form:t.commandCurrentForm}}),t._v(" "),n("CommandViewPanel",{attrs:{content:t.commandViewContent},on:{close:t.handleCommandViewPanelClosed}})],2)}),[],!1,null,null,null);pc.options.__file="EntityList.vue";var mc=pc.exports,vc={name:"SharpActionBarList",mixins:[qe],components:{ActionBar:L,Dropdown:ft,DropdownItem:vt,ItemVisual:Lt,CommandsDropdown:Cs,FilterDropdown:nl,Search:yn},props:{count:Number,search:String,filters:Array,filtersValues:Object,commands:Array,forms:Array,canCreate:Boolean,canReorder:Boolean,canSearch:Boolean,reorderActive:Boolean},data:function(){return{searchActive:!1}},computed:{hasForms:function(){return this.forms&&this.forms.length>0},hasCommands:function(){return this.commands&&this.commands.some((function(t){return t&&t.length>0}))}},methods:{handleSearchInput:function(t){this.$emit("search-change",t)},handleSearchSubmitted:function(){this.$emit("search-submit")},handleFilterChanged:function(t,e){this.$emit("filter-change",t,e)},handleReorderButtonClicked:function(){this.$emit("reorder-click"),document.activeElement.blur()},handleReorderSubmitButtonClicked:function(){this.$emit("reorder-submit")},handleCommandSelected:function(t){this.$emit("command",t)},handleCreateButtonClicked:function(){this.$emit("create")},handleCreateFormSelected:function(t){this.$emit("create",t)}}},yc=Object(k.a)(vc,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("ActionBar",{staticClass:"SharpActionBarList",class:{"SharpActionBarList--search-active":t.searchActive},attrs:{"right-class":"d-block"},scopedSlots:t._u([{key:"left",fn:function(){return[n("span",{staticClass:"text-content text-nowrap"},[t._v(t._s(t.count)+" "+t._s(t.l("action_bar.list.items_count")))])]},proxy:!0},{key:"right",fn:function(){return[n("div",{staticClass:"row justify-content-end"},[t.canSearch&&!t.reorderActive?[n("div",{staticClass:"col col-lg-auto"},[n("Search",{staticClass:"h-100",attrs:{value:t.search,active:t.searchActive,placeholder:t.l("action_bar.list.search.placeholder")},on:{"update:active":function(e){t.searchActive=e},input:t.handleSearchInput,submit:t.handleSearchSubmitted}})],1)]:t._e(),t._v(" "),t.canReorder?[n("div",{staticClass:"col-auto"},[t.reorderActive?[n("button",{staticClass:"SharpButton SharpButton--secondary-accent h-100",on:{click:t.handleReorderButtonClicked}},[t._v("\n                            "+t._s(t.l("action_bar.list.reorder_button.cancel"))+"\n                        ")]),t._v(" "),n("button",{staticClass:"SharpButton SharpButton--accent h-100",on:{click:t.handleReorderSubmitButtonClicked}},[t._v("\n                            "+t._s(t.l("action_bar.list.reorder_button.finish"))+"\n                        ")])]:[n("button",{staticClass:"SharpButton SharpButton--secondary-accent h-100",on:{click:t.handleReorderButtonClicked}},[t._v("\n                            "+t._s(t.l("action_bar.list.reorder_button"))+"\n                        ")])]],2)]:t._e(),t._v(" "),t.canCreate&&!t.reorderActive?[n("div",{staticClass:"col-auto"},[t.hasForms?[n("Dropdown",{staticClass:"SharpActionBar__forms-dropdown h-100",attrs:{text:t.l("action_bar.list.forms_dropdown")}},t._l(t.forms,(function(e,r){return n("DropdownItem",{key:r,on:{click:function(n){return t.handleCreateFormSelected(e)}}},[n("ItemVisual",{attrs:{item:e,"icon-class":"fa-fw"}}),t._v(t._s(e.label)+"\n                            ")],1)})),1)]:[n("button",{staticClass:"SharpButton SharpButton--accent h-100",on:{click:t.handleCreateButtonClicked}},[t._v("\n                            "+t._s(t.l("action_bar.list.create_button"))+"\n                        ")])]],2)]:t._e()],2)]},proxy:!0},t.reorderActive?null:{key:"extras",fn:function(){return[n("div",{staticClass:"row mx-n2"},[t._l(t.filters,(function(e){return[n("div",{staticClass:"col-auto px-2"},[n("FilterDropdown",{key:e.id,attrs:{filter:e,value:t.filtersValues[e.key]},on:{input:function(n){return t.handleFilterChanged(e,n)}}})],1)]}))],2)]},proxy:!0},t.reorderActive?null:{key:"extras-right",fn:function(){return[t.hasCommands?[n("CommandsDropdown",{staticClass:"SharpActionBar__actions-dropdown SharpActionBar__actions-dropdown--commands",attrs:{commands:t.commands},on:{select:t.handleCommandSelected},scopedSlots:t._u([{key:"text",fn:function(){return[t._v("\n                    "+t._s(t.l("entity_list.commands.entity.label"))+"\n                ")]},proxy:!0}],null,!1,2635357281)})]:t._e()]},proxy:!0}],null,!0)})}),[],!1,null,null,null);function bc(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function gc(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){_c(t,e,n[e])}))}return t}function _c(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}yc.options.__file="ActionBar.vue";var wc={name:"SharpEntityListPage",components:{EntityList:mc,ActionBarList:yc.exports},data:function(){return{ready:!1}},watch:{query:"handleQueryChanged","$route.query":"init"},computed:gc(gc({},Object(i.b)("entity-list",["query"])),{},{entityKey:function(){return this.$route.params.entityKey}}),methods:{handleQueryChanged:function(t,e){lc()(t,e)||this.$router.push({query:gc({},t)})},init:function(){var t,e=this;return(t=H.a.mark((function t(){return H.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$store.dispatch("entity-list/setQuery",gc({},e.$route.query));case 2:e.ready=!0;case 3:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){bc(i,r,a,o,s,"next",t)}function s(t){bc(i,r,a,o,s,"throw",t)}o(void 0)}))})()}},created:function(){this.init()}},Sc=Object(k.a)(wc,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpEntityListPage"},[n("div",{staticClass:"container"},[t.ready?[n("EntityList",{attrs:{"entity-key":t.entityKey,module:"entity-list"},scopedSlots:t._u([{key:"action-bar",fn:function(e){var r=e.props,a=e.listeners;return[n("ActionBarList",t._g(t._b({},"ActionBarList",r,!1),a))]}}],null,!1,4144326494)})]:t._e()],2)])}),[],!1,null,null,null);Sc.options.__file="EntityListPage.vue";var Cc,xc=[{name:"entity-list",path:"/list/:entityKey",component:Sc.exports}];function Oc(t){var e=t.entityKey,n=t.instanceId;return Xt.get("show/".concat(e,"/").concat(n||"")).then((function(t){return t.data}))}function kc(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function jc(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var Lc={namespaced:!0,modules:{filters:Ns,"entity-lists":{namespaced:!0}},state:{entityKey:null,instanceId:null,show:null},mutations:(Cc={},jc(Cc,"SET_SHOW_VIEW",(function(t,e){t.show=e})),jc(Cc,"SET_ENTITY_KEY",(function(t,e){t.entityKey=e})),jc(Cc,"SET_INSTANCE_ID",(function(t,e){t.instanceId=e})),Cc),getters:{entityKey:function(t){return t.entityKey},instanceId:function(t){return t.instanceId},config:function(t){return t.show.config},fields:function(t){return t.show.fields},layout:function(t){return t.show.layout},data:function(t){return t.show.data},breadcrumb:function(t){return t.show.breadcrumb},authorizations:function(t){return t.show.authorizations},canEdit:function(t,e){return e.authorizations.update},canChangeState:function(t,e){return!!e.config.state&&e.config.state.authorization},authorizedCommands:function(t,e){return((e.config.commands||{}).instance||[]).map((function(t){return t.filter((function(t){return t.authorization}))}))},instanceState:function(t,e){var n=e.config.state;if(n){var r=e.data[n.attribute];return n.values.find((function(t){return t.value===r}))}return null},stateValues:function(t,e){return e.config.state?e.config.state.values:null}},actions:{get:function(t){return(e=H.a.mark((function e(){var n,r,a;return H.a.wrap((function(e){for(;;)switch(e.prev=e.next){case 0:return n=t.state,r=t.commit,e.next=3,Oc({entityKey:n.entityKey,instanceId:n.instanceId});case 3:a=e.sent,r("SET_SHOW_VIEW",a);case 5:case"end":return e.stop()}}),e)})),function(){var t=this,n=arguments;return new Promise((function(r,a){var i=e.apply(t,n);function o(t){kc(i,r,a,o,s,"next",t)}function s(t){kc(i,r,a,o,s,"throw",t)}o(void 0)}))})();var e},postCommand:function(t,e){var n=t.state,r=e.command,a=e.data;return function(t){var e=t.entityKey,n=t.instanceId,r=t.commandKey,a=t.data;return Xt.post("show/".concat(e,"/command/").concat(r,"/").concat(n||""),{data:a},{responseType:"blob"})}({entityKey:n.entityKey,instanceId:n.instanceId,commandKey:r.key,data:a})},getCommandFormData:function(t,e){var n,r,a,i,o=t.state,s=e.command;return n={entityKey:o.entityKey,instanceId:o.instanceId,commandKey:s.key},r=n.entityKey,a=n.instanceId,i=n.commandKey,Xt.get("show/".concat(r,"/command/").concat(i).concat(a?"/".concat(a):"","/data")).then((function(t){return t.data.data}))},postState:function(t,e){var n=t.state,r=t.getters;return function(t){var e=t.entityKey,n=t.instanceId,r=t.value;return Xt.post("show/".concat(e,"/state/").concat(n||""),{value:r}).then((function(t){return t.data}))}({entityKey:n.entityKey,instanceId:n.instanceId,attribute:r.config.state.attribute,value:e})},setEntityKey:function(t,e){(0,t.commit)("SET_ENTITY_KEY",e)},setInstanceId:function(t,e){(0,t.commit)("SET_INSTANCE_ID",e)}}},Pc={mixins:[qe],components:{ActionBar:L,CommandsDropdown:Cs,Dropdown:ft,DropdownItem:vt,StateIcon:_n},props:{commands:Array,formUrl:String,backUrl:String,canEdit:Boolean,canChangeState:Boolean,showBackButton:Boolean,state:Object,stateValues:Array},computed:{hasCommands:function(){return this.commands&&this.commands.some((function(t){return t&&t.length>0}))},hasState:function(){return!!this.state}},methods:{handleEditButtonClicked:function(){this.$emit("edit")},handleCommandSelected:function(t){this.$emit("command",t)},handleStateChanged:function(t){this.$emit("state-change",t)}}},Ec=Object(k.a)(Pc,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("ActionBar",{scopedSlots:t._u([{key:"left",fn:function(){return[t.showBackButton?[n("a",{staticClass:"SharpButton SharpButton--secondary-accent",attrs:{href:t.backUrl}},[t._v("\n                "+t._s(t.l("action_bar.show.back_button"))+"\n            ")])]:t._e()]},proxy:!0},{key:"right",fn:function(){return[t.canEdit?[n("a",{staticClass:"SharpButton SharpButton--accent",attrs:{href:t.formUrl}},[t._v("\n                "+t._s(t.l("action_bar.show.edit_button"))+"\n            ")])]:t._e()]},proxy:!0},{key:"extras-right",fn:function(){return[n("div",{staticClass:"row mx-n1"},[t.hasState?[n("div",{staticClass:"col-auto px-1"},[n("Dropdown",{staticClass:"SharpActionBar__actions-dropdown SharpActionBar__actions-dropdown--state",attrs:{disabled:!t.canChangeState},scopedSlots:t._u([{key:"text",fn:function(){return[n("StateIcon",{attrs:{color:t.state.color}}),t._v(" "),n("span",{staticClass:"text-truncate"},[t._v(t._s(t.state.label))])]},proxy:!0}],null,!1,3350467533)},[t._v(" "),t._l(t.stateValues,(function(e){return[n("DropdownItem",{key:e.value,on:{click:function(n){return t.handleStateChanged(e.value)}}},[n("StateIcon",{attrs:{color:e.color}}),t._v(" \n                                "+t._s(e.label)+"\n                            ")],1)]}))],2)],1)]:t._e(),t._v(" "),t.hasCommands?[n("div",{staticClass:"col-auto px-1"},[n("CommandsDropdown",{staticClass:"SharpActionBar__actions-dropdown SharpActionBar__actions-dropdown--commands",attrs:{commands:t.commands},on:{select:t.handleCommandSelected},scopedSlots:t._u([{key:"text",fn:function(){return[t._v("\n                            "+t._s(t.l("entity_list.commands.instance.label"))+"\n                        ")]},proxy:!0}],null,!1,2971469203)})],1)]:t._e()],2)]},proxy:!0}])})}),[],!1,null,null,null);Ec.options.__file="ActionBar.vue";var Dc=Ec.exports,$c={mixins:[qe],components:{ItemVisual:Lt,Search:yn,Dropdown:ft,DropdownItem:vt,FilterDropdown:nl},props:{count:Number,search:String,filters:Array,filtersValues:Object,commands:Array,forms:Array,canCreate:Boolean,canReorder:Boolean,canSearch:Boolean,reorderActive:Boolean},data:function(){return{searchActive:!1}},computed:{hasForms:function(){return this.forms&&this.forms.length>0},hasLeftControls:function(){var t=this.filters||[];return this.count>0&&(t.length>0||this.canSearch)}},methods:{handleSearchInput:function(t){this.$emit("search-change",t)},handleSearchSubmitted:function(){this.$emit("search-submit")},handleFilterChanged:function(t,e){this.$emit("filter-change",t,e)},handleReorderButtonClicked:function(){this.$emit("reorder-click"),document.activeElement.blur()},handleReorderSubmitButtonClicked:function(){this.$emit("reorder-submit")},handleCommandSelected:function(t){this.$emit("command",t)},handleCreateButtonClicked:function(){this.$emit("create")},handleCreateFormSelected:function(t){this.$emit("create",t)}}},Ac=Object(k.a)($c,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"action-bar"},[t.hasLeftControls&&t.$slots.default?[n("div",{staticClass:"mb-2"},[t._t("default")],2)]:t._e(),t._v(" "),n("div",{staticClass:"row action-bar__row"},[t.hasLeftControls?[n("div",{staticClass:"col-sm action-bar__col"},[t.reorderActive?t._e():[n("div",{staticClass:"row action-bar__row"},[t._l(t.filters,(function(e){return[n("div",{staticClass:"col-auto action-bar__col mb-2"},[n("div",{staticClass:"action-bar__element"},[n("FilterDropdown",{key:e.id,staticClass:"h-100",attrs:{filter:e,value:t.filtersValues[e.key]},on:{input:function(n){return t.handleFilterChanged(e,n)}}})],1)])]})),t._v(" "),t.canSearch?[n("div",{staticClass:"col-auto action-bar__col mb-2"},[n("div",{staticClass:"action-bar__element"},[n("Search",{staticClass:"h-100",attrs:{value:t.search,active:t.searchActive,placeholder:t.l("action_bar.list.search.placeholder")},on:{"update:active":function(e){t.searchActive=e},input:t.handleSearchInput,submit:t.handleSearchSubmitted}})],1)])]:t._e()],2)]],2)]:[n("div",{staticClass:"col-sm align-self-center action-bar__col mb-2"},[t._t("default")],2)],t._v(" "),n("div",{staticClass:"col-sm-auto action-bar__col"},[n("div",{staticClass:"row flex-nowrap justify-content-end action-bar__row"},[t.canReorder?[n("div",{staticClass:"col-auto action-bar__col mb-2"},[t.reorderActive?[n("div",{staticClass:"row action-bar__row"},[n("div",{staticClass:"col-auto action-bar__col"},[n("button",{staticClass:"SharpButton SharpButton--secondary-accent",on:{click:t.handleReorderButtonClicked}},[t._v("\n                                        "+t._s(t.l("action_bar.list.reorder_button.cancel"))+"\n                                    ")])]),t._v(" "),n("div",{staticClass:"col-auto action-bar__col"},[n("button",{staticClass:"SharpButton SharpButton--accent",on:{click:t.handleReorderSubmitButtonClicked}},[t._v("\n                                        "+t._s(t.l("action_bar.list.reorder_button.finish"))+"\n                                    ")])])])]:[n("button",{staticClass:"SharpButton SharpButton--secondary-accent",on:{click:t.handleReorderButtonClicked}},[t._v("\n                                "+t._s(t.l("action_bar.list.reorder_button"))+"\n                            ")])]],2)]:t._e(),t._v(" "),t.canCreate&&!t.reorderActive?[n("div",{staticClass:"col-auto action-bar__col mb-2"},[n("div",{staticClass:"action-bar__element"},[t.hasForms?[n("Dropdown",{staticClass:"SharpActionBar__forms-dropdown h-100",attrs:{text:t.l("action_bar.list.forms_dropdown")}},t._l(t.forms,(function(e,r){return n("DropdownItem",{key:r,on:{click:function(n){return t.handleCreateFormSelected(e)}}},[n("ItemVisual",{attrs:{item:e,"icon-class":"fa-fw"}}),t._v(t._s(e.label)+"\n                                    ")],1)})),1)]:[n("button",{staticClass:"SharpButton SharpButton--accent",on:{click:t.handleCreateButtonClicked}},[t._v("\n                                    "+t._s(t.l("action_bar.list.create_button"))+"\n                                ")])]],2)])]:t._e()],2)])],2)],2)}),[],!1,null,null,null);Ac.options.__file="ActionBar.vue";var Bc=Ac.exports,Ic={props:{label:String}},Fc=Object(k.a)(Ic,(function(){var t=this.$createElement,e=this._self._c||t;return e("div",[this.label?[e("div",{staticClass:"show-field__label"},[this._v(this._s(this.label))])]:this._e(),this._v(" "),e("div",{staticClass:"show-field__content"},[this._t("default")],2)],2)}),[],!1,null,null,null);Fc.options.__file="FieldLayout.vue";var Tc=Fc.exports;function Mc(t,e){var n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:{},r=n.lazy;t.$watch(e,(function(e){t.$emit("visible-change",e)}),{immediate:!r})}var zc={mixins:[qe],components:{EntityList:mc,CommandsDropdown:Cs,ActionBar:Bc,FieldLayout:Tc},props:{fieldKey:String,entityListKey:String,showCreateButton:Boolean,showReorderButton:Boolean,showSearchField:Boolean,showEntityState:Boolean,hiddenFilters:Object,hiddenCommands:Object,label:String,emptyVisible:Boolean},data:function(){return{list:null}},computed:{storeModule:function(){return"show/entity-lists/".concat(this.fieldKey)},getFiltersQueryParams:function(){return this.storeGetter("filters/getQueryParams")},isVisible:function(){if(this.list){var t=this.list,e=t.data,n=t.authorizations;return!!(e.items&&e.items.length>0||this.showCreateButton&&n.create||this.emptyVisible)}return this.emptyVisible}},methods:{hasCommands:function(t){return t&&t.some((function(t){return t&&t.length>0}))},storeGetter:function(t){return this.$store.getters["".concat(this.storeModule,"/").concat(t)]},handleChanged:function(t){this.list=t}},created:function(){var t=this,e=this.storeModule.split("/");this.$store.hasModule(e)||this.$store.registerModule(e,oc),this.hiddenFilters&&this.$store.dispatch("".concat(this.storeModule,"/setQuery"),this.getFiltersQueryParams(this.hiddenFilters)),Mc(this,(function(){return t.isVisible}),{lazy:!0})}},Vc=Object(k.a)(zc,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("FieldLayout",{staticClass:"ShowEntityListField"},[n("EntityList",{attrs:{"entity-key":t.entityListKey,module:t.storeModule,"show-create-button":t.showCreateButton,"show-reorder-button":t.showReorderButton,"show-search-field":t.showSearchField,"show-entity-state":t.showEntityState,"hidden-commands":t.hiddenCommands,"hidden-filters":t.hiddenFilters,inline:""},on:{change:t.handleChanged},scopedSlots:t._u([{key:"action-bar",fn:function(e){var r=e.props,a=e.listeners;return[n("ActionBar",t._g(t._b({staticClass:"ShowEntityListField__action-bar"},"ActionBar",r,!1),a),[n("div",{staticClass:"ShowEntityListField__label show-field__label"},[t._v("\n                    "+t._s(t.label)+"\n                ")])])]}},{key:"append-head",fn:function(e){var r=e.props.commands,a=e.listeners;return[t.hasCommands(r)?[n("CommandsDropdown",{staticClass:"SharpActionBar__actions-dropdown SharpActionBar__actions-dropdown--commands",attrs:{commands:r},on:{select:a.command},scopedSlots:t._u([{key:"text",fn:function(){return[t._v("\n                        "+t._s(t.l("entity_list.commands.entity.label"))+"\n                    ")]},proxy:!0}],null,!0)})]:t._e()]}}])})],1)}),[],!1,null,null,null);Vc.options.__file="EntityList.vue";var Nc=Vc.exports,Rc=n("v23C"),Kc=n.n(Rc);function qc(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var Uc={mixins:[qe],components:{FieldLayout:Tc},props:{value:String,collapseToWordCount:Number,label:String,emptyVisible:Boolean},data:function(){return{expanded:!1}},computed:{currentContent:function(){return this.hasCollapsed&&!this.expanded?this.collapsedContent:this.value},hasCollapsed:function(){return!!this.collapsedContent},collapsedContent:function(){if(!this.collapseToWordCount||!this.value)return null;var t=this.value.trim(),e=function(t){var e=document.createElement("div");return e.innerHTML=t,e.textContent}(t),n=function(t,e){var n=qc(t.matchAll(/\S+\s*/g));return n.length>e?n.slice(0,e).map((function(t){return t[0]})).join(""):t}(e,this.collapseToWordCount);return n.length<e.length?Kc()(t,n.length+2,{html:!0}):null},isVisible:function(){return!!this.value||this.emptyVisible}},methods:{handleToggleClicked:function(){this.expanded=!this.expanded}},created:function(){var t=this;Mc(this,(function(){return t.isVisible}))}},Hc=Object(k.a)(Uc,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("FieldLayout",{staticClass:"ShowTextField",attrs:{label:t.label}},[n("div",{ref:"content",staticClass:"ShowTextField__content",domProps:{innerHTML:t._s(t.currentContent)}}),t._v(" "),t.hasCollapsed?[n("div",{staticClass:"mt-2"},[n("a",{staticClass:"ShowTextField__more",attrs:{href:"#"},on:{click:function(e){return e.preventDefault(),t.handleToggleClicked(e)}}},[t.expanded?[t._v("\n                    - "+t._s(t.l("show.text.show_less"))+"\n                ")]:[t._v("\n                    + "+t._s(t.l("show.text.show_more"))+"\n                ")]],2)])]:t._e()],2)}),[],!1,null,null,null);Hc.options.__file="Text.vue";var Gc=Hc.exports,Wc={components:{FieldLayout:Tc},props:{value:String,label:String},computed:{isVisible:function(){return!!this.value}},created:function(){var t=this;Mc(this,(function(){return t.isVisible}))}},Yc=Object(k.a)(Wc,(function(){var t=this.$createElement,e=this._self._c||t;return e("FieldLayout",{staticClass:"ShowPictureField"},[e("div",{staticClass:"text-center"},[e("img",{staticClass:"ShowPictureField__thumbnail",attrs:{src:this.value,alt:""}})])])}),[],!1,null,null,null);Yc.options.__file="Picture.vue";var Qc=Yc.exports,Xc=n("67qy");function Jc(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function Zc(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var tu={components:{Button:D,FieldLayout:Tc},props:{fieldConfigIdentifier:String,value:Object,label:String,collapsed:{type:Boolean,default:!0},list:Boolean},data:function(){return{thumbnailWidth:0}},computed:function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Zc(t,e,n[e])}))}return t}({},Object(i.b)("show",["entityKey","instanceId"]),{classes:function(){return{"ShowFileField--has-label":!!this.label}},thumbnailColClasses:function(){return{"ShowFileField__thumbnail-col--collapsed":!this.hasThumbnail&&this.collapsed}},downloadUrl:function(){return t={entityKey:this.entityKey,instanceId:this.instanceId,fieldKey:this.fieldConfigIdentifier,fileName:this.fileName},e=t.entityKey,n=t.instanceId,r=t.fieldKey,a=t.fileName,Jt("show/download/".concat(r,"/").concat(e,"/").concat(n),{params:{fileName:a}});var t,e,n,r,a},name:function(){return this.value?this.value.name:null},fileName:function(){var t=(this.name||"").split("/");return t[t.length-1]},hasThumbnail:function(){var t;return!!(null===(t=this.value)||void 0===t?void 0:t.thumbnail)},thumbnailUrl:function(){return this.value?this.value.thumbnail:null},thumbnailStyle:function(){return{"max-height":this.thumbnailWidth?"".concat(this.thumbnailWidth,"px"):null}},size:function(){return this.value?this.value.size:null},sizeLabel:function(){return this.size?ve(this.size):null},iconClass:function(){var t=this.fileName.split(".").pop();return Object(Xc.getClassNameForExtension)(t)},isVisible:function(){return!!this.value}}),methods:{lang:Zt,layout:function(){var t,e=this;return(t=H.a.mark((function t(){return H.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:e.$refs.thumbnail&&(e.thumbnailWidth=e.$refs.thumbnail.parentElement.offsetWidth);case 1:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){Jc(i,r,a,o,s,"next",t)}function s(t){Jc(i,r,a,o,s,"throw",t)}o(void 0)}))})()},handleThumbnailLoaded:function(){this.layout()}},created:function(){var t=this;Mc(this,(function(){return t.isVisible}))},mounted:function(){this.layout(),this.handleResize=Ge()(this.layout,150),window.addEventListener("resize",this.handleResize)},beforeDestroy:function(){window.removeEventListener("resize",this.handleResize)}},eu=Object(k.a)(tu,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("FieldLayout",{staticClass:"ShowFileField",class:t.classes,attrs:{label:t.label}},[n("div",{staticClass:"row mx-n2"},[n("div",{staticClass:"col-3 px-2 align-self-center ShowFileField__thumbnail-col",class:t.thumbnailColClasses},[t.hasThumbnail?[n("div",{staticClass:"ShowFileField__thumbnail-container"},[n("img",{ref:"thumbnail",staticClass:"ShowFileField__thumbnail",style:t.thumbnailStyle,attrs:{src:t.thumbnailUrl,alt:""},on:{load:t.handleThumbnailLoaded}})])]:[n("div",{staticClass:"ShowFileField__placeholder text-center"},[n("i",{staticClass:"fa far",class:t.iconClass})])]],2),t._v(" "),n("div",{staticClass:"col px-2 mt-1",staticStyle:{"min-width":"0"}},[n("div",{staticClass:"ShowFileField__name text-truncate mb-2"},[t._v("\n                "+t._s(t.fileName)+"\n            ")]),t._v(" "),n("div",{staticClass:"ShowFileField__info"},[n("div",{staticClass:"row mx-n2 h-100"},[n("div",{staticClass:"col-auto px-2"},[n("div",{staticClass:"ShowFileField__size text-muted"},[t._v("\n                            "+t._s(t.sizeLabel)+"\n                        ")])]),t._v(" "),n("div",{staticClass:"col-auto px-2"},[n("div",{staticClass:"text-muted"},[n("i",{staticClass:"fa fas fa-download"}),t._v(" "),n("a",{staticStyle:{color:"inherit"},attrs:{href:t.downloadUrl,download:t.fileName}},[t._v("\n                                "+t._s(t.lang("show.file.download"))+"\n                            ")])])])])])])])])}),[],!1,null,null,null);eu.options.__file="File.vue";var nu=eu.exports;function ru(t,e){return function(t){if(Array.isArray(t))return t}(t)||function(t,e){var n=[],r=!0,a=!1,i=void 0;try{for(var o,s=t[Symbol.iterator]();!(r=(o=s.next()).done)&&(n.push(o.value),!e||n.length!==e);r=!0);}catch(t){a=!0,i=t}finally{try{r||null==s.return||s.return()}finally{if(a)throw i}}return n}(t,e)||function(){throw new TypeError("Invalid attempt to destructure non-iterable instance")}()}function au(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function iu(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var ou={components:{Grid:Ot,UnknownField:Vn,FieldLayout:Tc},props:{value:Array,itemFields:{type:Object,required:!0},layout:Object,label:String,emptyVisible:Boolean},computed:{isEmpty:function(){return!this.value||0===this.value.length},isVisible:function(){return!this.isEmpty||this.emptyVisible},classes:function(){return{"ShowListField--empty":this.isEmpty}},fileFieldsCollapsed:function(){return this.allValuesOfType("file").every((function(t){return t&&!t.thumbnail}))}},methods:{lang:Zt,fieldOptions:function(t){var e=this.itemFields?function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){iu(t,e,n[e])}))}return t}({},this.itemFields[t.key]):null;return e||console.error('Show list field: unknown field "'.concat(t.key,'"')),"file"===e.type&&(e.collapsed=this.fileFieldsCollapsed),e},fieldValue:function(t,e){return t?t[e.key]:null},allValuesOfType:function(t){var e=this;return(this.value||[]).reduce((function(n,r){return[].concat(au(n),au(Object.entries(r).filter((function(n){var r=ru(n,1)[0],a=e.itemFields[r];return a&&a.type===t})).map((function(t){var e=ru(t,2);e[0];return e[1]}))))}),[])}},created:function(){var t=this;Mc(this,(function(){return t.isVisible}))}},su=Object(k.a)(ou,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("FieldLayout",{staticClass:"ShowListField",class:t.classes,attrs:{label:t.label}},[n("div",{staticClass:"ShowListField__content"},[t.isEmpty?[n("em",{staticClass:"ShowListField__empty text-muted"},[t._v(t._s(t.lang("show.list.empty")))])]:[n("div",{staticClass:"ShowListField__list"},[t._l(t.value,(function(e){return[n("div",{staticClass:"ShowListField__item"},[n("Grid",{staticClass:"ShowListField__fields-grid",attrs:{rows:t.layout.item},scopedSlots:t._u([{key:"default",fn:function(r){var a=r.itemLayout;return[t.fieldOptions(a)?[n("ShowField",{attrs:{options:t.fieldOptions(a),value:t.fieldValue(e,a),"config-identifier":a.key}})]:[n("UnknownField",{attrs:{name:a.key}})]]}}],null,!0)})],1)]}))],2)]],2)])}),[],!1,null,null,null);su.options.__file="List.vue";var lu={entityList:Nc,text:Gc,picture:Qc,file:nu,list:su.exports};function cu(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var uu={mixins:[Ye],props:{value:{},options:Object,layout:Object},data:function(){return{visible:!0}},computed:{component:function(){return this.options?Ve(t=this.options.type)?Ne(t):lu[t]:null;var t},props:function(){return function(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){cu(t,e,n[e])}))}return t}({},this.options)},isVisible:function(){return!!this.component&&this.visible}},methods:{handleVisiblityChanged:function(t){this.visible=t,this.$emit("visible-change",t)}}},du=Object(k.a)(uu,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{directives:[{name:"show",rawName:"v-show",value:t.isVisible,expression:"isVisible"}],staticClass:"show-field"},[n(t.component,t._b({tag:"component",attrs:{"field-key":t.options.key,"field-config-identifier":t.mergedConfigIdentifier,value:t.value,layout:t.layout},on:{"visible-change":t.handleVisiblityChanged}},"component",t.props,!1))],1)}),[],!1,null,null,null);du.options.__file="Field.vue";var hu=du.exports;function fu(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function pu(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}function mu(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){vu(t,e,n[e])}))}return t}function vu(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}var yu={mixins:[an],components:{ActionBarShow:Dc,Grid:Ot,ShowField:hu,UnknownField:Vn,CommandFormModal:_s,CommandViewPanel:ks},data:function(){return{ready:!1,fieldsVisible:null,refreshKey:0}},computed:mu({},Object(i.b)("show",["entityKey","instanceId","fields","layout","data","config","breadcrumb","instanceState","canEdit","authorizedCommands","stateValues","canChangeState"]),{formUrl:function(){return Re({entityKey:this.entityKey,instanceId:this.instanceId})},backUrl:function(){return Fe(this.breadcrumb)},showBackButton:function(){return!!this.backUrl}}),methods:{fieldOptions:function(t){var e=this.fields?this.fields[t.key]:null;return e||console.error('Show page: unknown field "'.concat(t.key,'"')),e},fieldValue:function(t){return this.data?this.data[t.key]:null},isFieldVisible:function(t){return!this.fieldsVisible||!1!==this.fieldsVisible[t.key]},fieldsRowClass:function(t){var e=this,n=t.map((function(t){var n=e.fieldOptions(t);return"ShowPage__fields-row--".concat(n.type)}));return["ShowPage__fields-row"].concat(pu(n))},sectionClasses:function(t){return{"ShowPage__section--no-container":this.sectionHasField(t,"entityList")}},sectionColClass:function(){return"ShowPage__section-col"},sectionFields:function(t){return t.columns.reduce((function(t,e){return[].concat(pu(t),pu(e.fields.flat()))}),[])},isSectionVisible:function(t){var e=this;return this.sectionFields(t).some((function(t){return e.isFieldVisible(t)}))},sectionHasField:function(t,e){var n=this;return this.sectionFields(t).some((function(t){var r=n.fieldOptions(t);return r&&r.type===e}))},handleFieldVisibilityChanged:function(t,e){this.fieldsVisible=mu({},this.fieldsVisible,vu({},t,e))},handleCommandRequested:function(t){var e=this;this.sendCommand(t,{postCommand:function(){return e.$store.dispatch("show/postCommand",{command:t})},postForm:function(n){return e.$store.dispatch("show/postCommand",{command:t,data:n})},getFormData:function(){return e.$store.dispatch("show/getCommandFormData",{command:t})}})},handleStateChanged:function(t){var e=this;return this.$store.dispatch("show/postState",t).then((function(t){e.handleCommandActionRequested(t.action,t)})).catch((function(t){var e=t.response.data;422===t.response.status&&ke(e.message,{title:Zt("modals.state.422.title"),isError:!0})}))},handleRefreshCommand:function(){this.init()},initCommands:function(){this.addCommandActionHandlers({refresh:this.handleRefreshCommand})},init:function(){var t,e=this;return(t=H.a.mark((function t(){return H.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:return t.next=2,e.$store.dispatch("show/setEntityKey",e.$route.params.entityKey);case 2:return t.next=4,e.$store.dispatch("show/setInstanceId",e.$route.params.instanceId);case 4:return t.next=6,e.$store.dispatch("show/get");case 6:e.ready=!0,e.refreshKey++;case 8:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){fu(i,r,a,o,s,"next",t)}function s(t){fu(i,r,a,o,s,"throw",t)}o(void 0)}))})()}},beforeMount:function(){this.init(),this.initCommands()}},bu=Object(k.a)(yu,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"ShowPage"},[n("div",{staticClass:"container"},[t.ready?[n("ActionBarShow",{attrs:{commands:t.authorizedCommands,state:t.instanceState,"state-values":t.stateValues,"form-url":t.formUrl,"back-url":t.backUrl,"can-edit":t.canEdit,"can-change-state":t.canChangeState,"show-back-button":t.showBackButton},on:{command:t.handleCommandRequested,"state-change":t.handleStateChanged}}),t._v(" "),t._l(t.layout.sections,(function(e){return[n("div",{directives:[{name:"show",rawName:"v-show",value:t.isSectionVisible(e),expression:"isSectionVisible(section)"}],staticClass:"ShowPage__section",class:t.sectionClasses(e)},[e.title?[n("h2",{staticClass:"ShowPage__section-title mb-2"},[t._v(t._s(e.title))])]:t._e(),t._v(" "),n("div",{staticClass:"ShowPage__section-content"},[n("Grid",{staticClass:"ShowPage__section-grid",attrs:{rows:[e.columns],"col-class":t.sectionColClass},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.itemLayout;return[n("Grid",{staticClass:"ShowPage__fields-grid",attrs:{rows:r.fields,"row-class":t.fieldsRowClass},scopedSlots:t._u([{key:"default",fn:function(e){var r=e.itemLayout;return[t.fieldOptions(r)?[n("ShowField",{key:t.refreshKey,attrs:{options:t.fieldOptions(r),value:t.fieldValue(r),"config-identifier":r.key,layout:r},on:{"visible-change":function(e){return t.handleFieldVisibilityChanged(r.key,e)}}})]:[n("UnknownField",{attrs:{name:r.key}})]]}}],null,!0)})]}}],null,!0)})],1)],2)]}))]:t._e()],2),t._v(" "),n("CommandFormModal",{ref:"commandForm",attrs:{form:t.commandCurrentForm}}),t._v(" "),n("CommandViewPanel",{attrs:{content:t.commandViewContent},on:{close:t.handleCommandViewPanelClosed}})],1)}),[],!1,null,null,null);bu.options.__file="ShowPage.vue";var gu=[{name:"show",path:"/show/:entityKey/:instanceId?",component:bu.exports}],_u={name:"SharpActionView",components:{Modal:ln,LoadingOverlay:Bt},provide:function(){return{axiosInstance:Xt}},props:{context:{type:String,required:!0}},data:function(){return{showErrorPage:!1,errorPageData:null}},computed:{dialogs:function(){return this.$store.state.dialogs},isLoading:function(){return this.$store.getters.isLoading}},created:function(){var t=this;this._provided.axiosInstance.interceptors.response.use((function(t){return t}),(function(e){var n=e.response,r=n.status,a=n.data;return"get"===e.config.method&&404===r&&(t.showErrorPage=!0,t.errorPageData={status:r,message:a.message}),Promise.reject(e)}))}},wu=Object(k.a)(_u,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"SharpActionView"},[t.showErrorPage?[n("div",{staticClass:"container"},[n("h1",[t._v("Error "+t._s(t.errorPageData.status))]),t._v(" "),n("p",[t._v(t._s(t.errorPageData.message))])])]:[t._t("default"),t._v(" "),n("notifications",{staticStyle:{top:"6rem"},attrs:{position:"top right","animation-name":"slideRight",reverse:""},scopedSlots:t._u([{key:"body",fn:function(e){var r=e.item,a=e.close;return[n("div",{staticClass:"SharpToastNotification",class:"SharpToastNotification--"+(r.type||"info"),attrs:{role:"alert","data-test":"notification"}},[n("div",{staticClass:"SharpToastNotification__details"},[n("h3",{staticClass:"SharpToastNotification__title mb-2"},[t._v(t._s(r.title))]),t._v(" "),r.text?n("p",{staticClass:"SharpToastNotification__caption",domProps:{innerHTML:t._s(r.text)}}):t._e()]),t._v(" "),n("button",{staticClass:"SharpToastNotification__close-button",attrs:{type:"button","data-test":"close-notification"},on:{click:a}},[n("svg",{staticClass:"SharpToastNotification__icon",attrs:{"aria-label":"close",width:"10",height:"10",viewBox:"0 0 10 10","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M9.8 8.6L8.4 10 5 6.4 1.4 10 0 8.6 3.6 5 .1 1.4 1.5 0 5 3.6 8.6 0 10 1.4 6.4 5z"}})])])])]}}])}),t._v(" "),t._l(t.dialogs,(function(e){return[n("Modal",t._b({key:e.id,on:{ok:e.okCallback,hidden:e.hiddenCallback}},"Modal",e.props,!1),[t._v("\n                "+t._s(e.text)+"\n            ")])]}))],t._v(" "),n("LoadingOverlay",{attrs:{visible:t.isLoading}})],2)}),[],!1,null,null,null);wu.options.__file="ActionView.vue";var Su=wu.exports;function Cu(t,e,n,r,a,i,o){try{var s=t[i](o),l=s.value}catch(t){return void n(t)}s.done?e(l):Promise.resolve(l).then(r,a)}function xu(t){return function(t){if(Array.isArray(t)){for(var e=0,n=new Array(t.length);e<t.length;e++)n[e]=t[e];return n}}(t)||function(t){if(Symbol.iterator in Object(t)||"[object Arguments]"===Object.prototype.toString.call(t))return Array.from(t)}(t)||function(){throw new TypeError("Invalid attempt to spread non-iterable instance")}()}var Ou={name:"SharpLeftNav",mixins:[We("lg")],components:{GlobalFilters:ll,Loading:Dt},props:{items:Array,current:String,title:String,collapseable:{type:Boolean,default:!0},hasGlobalFilters:Boolean},data:function(){return{ready:!1,collapsed:!1,state:"expanded",filterOpened:!1}},watch:{collapsed:{handler:function(t){this.$root.$emit("setClass","leftNav--collapsed",this.collapsed),this.state=t?"collapsing":"expanding",setTimeout(this.updateState,250)}}},computed:{flattenedItems:function(){return this.items.reduce((function(t,e){return"category"===e.type?[].concat(xu(t),xu(e.entities)):[].concat(xu(t),[e])}),[])},currentIcon:function(){var t=this;return"dashboard"===this.current?"fas fa-tachometer-alt":(this.flattenedItems.find((function(e){return e.key===t.current}))||{}).icon},classes:function(){return["SharpLeftNav--".concat(this.state),{"SharpLeftNav--filter-opened":this.filterOpened,"SharpLeftNav--collapseable":this.collapseable}]}},methods:{updateState:function(){this.state=this.collapsed?"collapsed":"expanded"},handleGlobalFilterOpened:function(){this.filterOpened=!0},handleGlobalFilterClosed:function(){this.filterOpened=!1},handleClicked:function(){this.collapsed&&(this.collapsed=!1)},init:function(){var t,e=this;return(t=H.a.mark((function t(){return H.a.wrap((function(t){for(;;)switch(t.prev=t.next){case 0:if(!e.hasGlobalFilters){t.next=3;break}return t.next=3,e.$store.dispatch("global-filters/get");case 3:e.ready=!0;case 4:case"end":return t.stop()}}),t)})),function(){var e=this,n=arguments;return new Promise((function(r,a){var i=t.apply(e,n);function o(t){Cu(i,r,a,o,s,"next",t)}function s(t){Cu(i,r,a,o,s,"throw",t)}o(void 0)}))})()}},created:function(){this.collapsed=this.isViewportSmall,this.updateState(),this.init()}},ku=Object(k.a)(Ou,(function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("nav",{staticClass:"SharpLeftNav",class:t.classes,attrs:{role:"navigation","aria-label":"Menu Sharp"},on:{click:t.handleClicked}},[n("div",{staticClass:"SharpLeftNav__top-icon"},[n("i",{staticClass:"fa",class:t.currentIcon})]),t._v(" "),n("div",{staticClass:"flex-grow-0"},[n("div",{staticClass:"SharpLeftNav__title-container position-relative"},[t._t("title",[n("h2",{staticClass:"SharpLeftNav__title"},[t._v(t._s(t.title))])])],2)]),t._v(" "),n("div",{staticClass:"flex-grow-1 position-relative",staticStyle:{"min-height":"0"}},[t.ready?[n("div",{staticClass:"SharpLeftNav__content d-flex flex-column"},[n("div",{staticClass:"SharpLeftNav__inner flex-grow-1",staticStyle:{"min-height":"0"}},[n("GlobalFilters",{on:{open:t.handleGlobalFilterOpened,close:t.handleGlobalFilterClosed}}),t._v(" "),t._t("default")],2),t._v(" "),n("div",{staticClass:"flex-grow-0"},[n("div",{staticClass:"SharpLeftNav__collapse",on:{click:function(e){e.stopPropagation(),t.collapsed=!t.collapsed}}},[n("a",{staticClass:"SharpLeftNav__collapse-link",attrs:{href:"#"},on:{click:function(t){t.preventDefault()}}},[n("svg",{staticClass:"SharpLeftNav__collapse-arrow",attrs:{width:"8",height:"12",viewBox:"0 0 8 12","fill-rule":"evenodd"}},[n("path",{attrs:{d:"M7.5 10.6L2.8 6l4.7-4.6L6.1 0 0 6l6.1 6z"}})])])])])])]:[n("div",{staticClass:"d-flex align-items-center justify-content-center h-100"},[n("Loading",{attrs:{visible:"",small:""}})],1)]],2)])}),[],!1,null,null,null);ku.options.__file="LeftNav.vue";var ju=ku.exports;function Lu(t){for(var e=1;e<arguments.length;e++){var n=null!=arguments[e]?arguments[e]:{},r=Object.keys(n);"function"==typeof Object.getOwnPropertySymbols&&(r=r.concat(Object.getOwnPropertySymbols(n).filter((function(t){return Object.getOwnPropertyDescriptor(n,t).enumerable})))),r.forEach((function(e){Pu(t,e,n[e])}))}return t}function Pu(t,e,n){return e in t?Object.defineProperty(t,e,{value:n,enumerable:!0,configurable:!0,writable:!0}):t[e]=n,t}f.a.use(function(){var t=document.documentElement.lang;return x[t]||x.en}()),a.a.use(d.a),a.a.use(c.install,{installComponents:!1}),a.a.use(o.a),a.a.use(i.a);var Eu=Be(),Du=ge();a.a.use((function(t,e){e.store,e.router}),{store:Du,router:Eu}),a.a.use((function(t,e){var n=e.router;e.store.registerModule("dashboard",dl),n.addRoutes(ac)}),{store:Du,router:Eu}),a.a.use((function(t,e){var n=e.router;e.store.registerModule("entity-list",oc),n.addRoutes(xc)}),{store:Du,router:Eu}),a.a.use((function(t,e){e.store.registerModule("global-filters",Ks)}),{store:Du,router:Eu}),a.a.use((function(t,e){var n=e.router;e.store;t.component("FieldDisplay",es),n.addRoutes(ys)}),{store:Du,router:Eu}),a.a.use((function(t,e){var n=e.router,r=e.store;n.addRoutes(gu),r.registerModule("show",Lc),t.component("ShowField",hu)}),{store:Du,router:Eu}),a.a.use((function(t,e){e.store,e.router}),{store:Du,router:Eu}),window.Trix=l.a,a.a.config.ignoredElements=[/^trix-/],new a.a({el:"#sharp-app",components:{"sharp-action-view":Su,"sharp-left-nav":ju,"sharp-collapsible-item":q,"sharp-nav-item":N,"sharp-item-visual":Lt},created:function(){var t=this;this.$on("setClass",(function(e,n){t.$el.classList[n?"add":"remove"](e)})),this.$route.query["x-access-from"]&&this.$router.replace(Lu({},this.$route,{query:Lu({},this.$route.query,{"x-access-from":void 0})}))},store:Du,router:Eu})},GS0v:function(t,e,n){var r=n("QOb4");"string"==typeof r&&(r=[[t.i,r,""]]);var a={hmr:!0,transform:void 0,insertInto:void 0};n("aET+")(r,a);r.locals&&(t.exports=r.locals)},"K0p+":function(t,e){},QOb4:function(t,e,n){(t.exports=n("I1BE")(!1)).push([t.i,".unknown-field[data-v-d6c36028]{font-family:Courier New,Courier,monospace;background:rgba(255,0,0,.25);border:1px solid red;padding:.5em 1em;font-weight:700}",""])},ynHM:function(t,e,n){"use strict";var r=n("GS0v");n.n(r).a}},[[0,0,1]]]);