<?php

namespace App\Helper;

// use App\Models\AdditionalCost;
// use App\Models\Notification;
// use App\Models\Store;
// use App\Models\Status;
// use App\Models\Deposit;
// use App\Models\OrderProduct;
// use App\Models\Order;
// use App\Models\Invoice;
// use App\Models\Total;
// use App\Models\User;
// use App\Models\Customer;
// use App\Models\Setting;
// use App\Models\Address;
// use Illuminate\Support\Facades\File;
// use Illuminate\Database\Eloquent\Builder;
// use Illuminate\Filesystem\Filesystem;
// use Intervention\Image\ImageManagerStatic as Image;
// use Spatie\ImageOptimizer\OptimizerChainFactory;
use DateTime;
// use Illuminate\Support\Facades\DB;

// use App\Jobs\ProcessNotif;

class HelperSocialchat
{
    public function getChannelId($store)
    {
        if (empty($store)) return null;
        $appId = env('SOCIALCHAT_APP_ID');
        $secretKey = env('SOCIALCHAT_SECRET_KEY');
        $bearer = base64_encode($appId . '_' . $secretKey);
        $params = [
            'page' => 1,
            'limit' => 100,
            'channelTypes' => [
                'whatsapp-unofficial',
            ],
            // 'search' => '62', // CS WhatsApp Phone Number
            // 'search' => $store->whatsapp_1, // CS WhatsApp Phone Number
        ];
        $url = 'https://api.socialchat.id/partner/channel?' . http_build_query($params);
        $opts = array(
            'http' =>
            array(
                'method'  => 'GET',
                'header'  => [
                    'Content-Type: application/x-www-form-urlencoded',
                    'Authorization: Bearer ' . $bearer,
                ],
            )
        );
        $context  = stream_context_create($opts);
        $result = @file_get_contents($url, false, $context);
        $result = json_decode($result);
        if (empty($result) || !empty($result->error_message)) {
            return null;
            // return $result->error_message;
        }
        // return $result;
        if (is_object($result) && property_exists($result, 'docs') && is_array($result->docs) && !empty($result->docs) && is_object($result->docs[0]) && property_exists($result->docs[0], '_id')) {
            $csPhone = $store->whatsapp_1 ?? '';
            $csPhone = '0' . substr($csPhone, 2);
            $channel_index = array_search($csPhone, array_column(array_column($result->docs, 'label'), 'phone'));
            if ($channel_index > -1) {
                $channel = $result->docs[$channel_index];
                $store->socialchat_channel_id = $channel->_id;
                $store->save();
                // return (array)$channel;
                return $channel->_id;
            }
            return null;
        }
        return null;
    }

    public function getConversationId($store, $phone)
    {
        $socialchat_channel_id = $store->socialchat_channel_id;

        if (!$socialchat_channel_id) {
            $socialchat_channel_id = $this->getChannelId($store);
        }
        if (empty($socialchat_channel_id)) return null;
        $appId = env('SOCIALCHAT_APP_ID');
        $secretKey = env('SOCIALCHAT_SECRET_KEY');
        $bearer = base64_encode($appId . '_' . $secretKey);
        $channelId = $socialchat_channel_id;
        $from_datetime = new DateTime();
        $from_datetime->modify('-1 year');
        $to_datetime = new DateTime();
        $to_datetime->modify('+1 day');
        $params = [
            'page' => 1,
            'limit' => 10,
            'channelIds' => [
                $channelId,
            ],
            'channelTypes' => [
                'whatsapp-unofficial',
            ],
            'search' => $phone,
            'fromDate' => $from_datetime->format('Y-m-d\TH:i:s.000\Z'),
            'toDate' => $to_datetime->format('Y-m-d\TH:i:s.000\Z'),
        ];
        $url = 'https://api.socialchat.id/partner/conversation?' . http_build_query($params);
        $opts = array(
            'http' =>
            array(
                'method'  => 'GET',
                'header'  => [
                    'Content-Type: application/x-www-form-urlencoded',
                    'Authorization: Bearer ' . $bearer,
                ],
            )
        );
        $context  = stream_context_create($opts);
        $result = @file_get_contents($url, false, $context);
        $result = json_decode($result);
        if (empty($result) || !empty($result->error_message)) {
            return null;
        }
        if (is_object($result) && property_exists($result, 'docs') && is_array($result->docs) && !empty($result->docs) && is_object($result->docs[0]) && property_exists($result->docs[0], '_id')) {
            return $result->docs[0]->_id;
        }
        return null;
    }

    public function sendMessage($conversationId, $msg, $media = null)
    {
        $appId = env('SOCIALCHAT_APP_ID');
        $secretKey = env('SOCIALCHAT_SECRET_KEY');
        $bearer = base64_encode($appId . '_' . $secretKey);
        $url = 'https://api.socialchat.id/partner/message/' . $conversationId;
        $data = [];
        $data['text'] = $msg;
        if (!empty($media)) {
            $data['media'] = $media;
        }
        $postdata = http_build_query($data);
        $opts = array(
            'http' =>
            array(
                'method'  => 'POST',
                'header'  => [
                    'Content-Type: application/x-www-form-urlencoded',
                    'Authorization: Bearer ' . $bearer,
                ],
                'content' => $postdata
            )
        );
        $context  = stream_context_create($opts);
        $result = @file_get_contents($url, false, $context);
        $result = json_decode($result);
        if (empty($result) || !empty($result->error_message)) {
            return false;
            // return $result->error_message;
        }
        if (!empty($result) && is_object($result) && property_exists($result, 'messageId')) {
            return $result;
            // {
            //     "conversationId": "6633707caf9ef08bd39b7b96",
            //     "identifierId": "NOGVOOFDQEMZIJVKWSXDFKSVHYPUKN",
            //     "messageId": "TEMP-UMGUNSMZWHAZGVESJEEQ",
            //     "senderId": "628112926455:<EMAIL>",
            //     "senderName": "Sg",
            //     "text": "Coba lagi",
            //     "type": "text",
            //     "statusRead": "pending",
            //     "channelBy": {
            //     "_id": "663366f1c39e7f818e2b86ea",
            //     "channelType": "whatsapp-unofficial"
            //     },
            //     "sendAt": "2024-05-03T23:00:34.212Z",
            //     "rawRequestBody": {
            //     "text": "Coba lagi",
            //     "identifierId": "NOGVOOFDQEMZIJVKWSXDFKSVHYPUKN"
            //     }
            //     }
        }
    }
}
