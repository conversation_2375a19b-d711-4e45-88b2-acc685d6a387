<?php

namespace App\Sharp;

use App\Models\User;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use Code16\Sharp\Utils\LinkToEntity;
use App\Sharp\Filters\UserRoleFilter;
use App\Sharp\Filters\UserStoreFilter;
use Illuminate\Database\Eloquent\Builder;

class UserSharpList extends SharpEntityList
{
    /**
     * Build list containers using ->addDataContainer()
     *
     * @return void
     */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('name')
                ->setLabel('Nama')
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("contact")
                // ->setSortable()
                ->setLabel("Kontak")
        )->addDataContainer(
            EntityListDataContainer::make("role")
                // ->setSortable()
                ->setLabel("Role")
        )->addDataContainer(
            EntityListDataContainer::make("created_at")
                ->setSortable()
                ->setLabel("Created")
        );
    }

    /**
     * Build list layout using ->addColumn()
     *
     * @return void
     */

    public function buildListLayout()
    {
        $this->addColumn("name", 3, 12)
            ->addColumn("contact", 3, 12)
            ->addColumn("role", 4, 12)
            ->addColumn("created_at", 2, 12);
    }

    /**
     * Build list config
     *
     * @return void
     */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('created_at', 'asc')
            ->addFilter("role", UserRoleFilter::class)
            ->addFilter("store", UserStoreFilter::class)
            ->setPaginated();
    }

    /**
     * Retrieve all rows data as array.
     *
     * @param EntityListQueryParams $params
     * @return array
     */
    public function getListData(EntityListQueryParams $params)
    {
        $users = User::distinct();
        $current_user = auth()->user();

        if ($current_user->role_id == 3) { // filter for CS
            $stores_id = $current_user->stores->pluck('id');
            $users->whereIn('role_id', [3, 4, 5, 6])
                ->where(function (Builder $query) use ($stores_id) {
                    $query->whereHas('stores', function (Builder $subquery) use ($stores_id) {
                        $subquery->whereIn('id', $stores_id);
                    })->orWhereIn('store_id', $stores_id);
                });
        }

        if ($params->sortedBy()) {
            $users->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            foreach ($params->searchWords() as $word) {
                $users->where('name', 'like', $word)
                    ->orWhere('phone', 'like', $word)
                    ->orWhere('email', 'like', $word);
            }
        }

        if ($role_filter = $params->filterFor("role")) {
            $users->where("role_id", $role_filter);
        }

        if ($stores_filter = $params->filterFor("store")) {
            $users->where(function (Builder $query) use ($stores_filter) {
                $query->where("store_id", $stores_filter)
                    ->orWhereHas('stores', function (Builder $subquery) use ($stores_filter) {
                        $subquery->where('id', $stores_filter);
                    });
            });
        }

        return $this
            ->setCustomTransformer("name", function ($value, $user) {
                $is_partner = $user->is_partner ? '<br><div style="color: red; font-weight: bold;">MITRA</div>' : '';
                return $value . $is_partner;
            })
            ->setCustomTransformer("role", function ($value, $user) {
                $role_link = (new LinkToEntity('<i class="fas fa-tag mr-1"></i>' . $user->role->title, "role"))
                    ->setTooltip("See related role")
                    // ->setSearch($user->role->title)
                    ->toFormOfInstance($user->role)
                    ->render();
                $store_link = $user->store ? (new LinkToEntity('<p class="mt-2"><i class="fas fa-store mr-1"></i>' . $user->store->name . '</p>', "store"))
                    ->setTooltip("See related store")
                    // ->setSearch($user->store->name)
                    ->toFormOfInstance($user->store)
                    ->render() : '';
                $stores = $user->stores->map(function ($store) {
                    return (new LinkToEntity('<p class="mt-2"><i class="fas fa-store mr-1"></i>' . $store->name . '</p>', "store"))
                        ->setTooltip("See related store")
                        // ->setSearch($store->title)
                        ->toFormOfInstance($store)
                        ->render();
                })->implode("");
                return $role_link . ($stores ? $stores : $store_link);
            })
            ->setCustomTransformer("contact", function ($value, $user) {
                $phone = $user->phone ? '<a style="display: block;" href="https://wa.me/' . $user->phone . '" target="_blank"><i class="fab fa-whatsapp mr-1"></i>' . $user->phone . '</a>' : '';
                $email = $user->email ? '<a style="display: block;" href="mailto:' . $user->email . '" target="_blank"><i class="fas fa-envelope mr-1"></i>' . $user->email . '</a>' : '';
                return $phone . $email;
            })
            ->setCustomTransformer("created_at", function ($value, $user) {
                return $user->created_at->format('jMy(G:i)');
            })
            ->transform($users->with(["role", "stores"])->paginate(30));
    }
}
