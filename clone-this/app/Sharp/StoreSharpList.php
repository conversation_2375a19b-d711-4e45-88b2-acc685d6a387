<?php

namespace App\Sharp;

use Carbon\Carbon;
use App\Models\Store;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use App\Sharp\Commands\CustomerMapPreview;
use Code16\Sharp\EntityList\SharpEntityList;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;

class StoreSharpList extends SharpEntityList
{
    /**
     * Build list containers using ->addDataContainer()
     *
     * @return void
     */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('name')
                ->setLabel('<PERSON>a <PERSON>')
                ->setSortable()
            // ->setHtml()
        )->addDataContainer(
            EntityListDataContainer::make("area")
                ->setSortable()
                ->setLabel("Area")
        )->addDataContainer(
            EntityListDataContainer::make("city")
                ->setSortable()
                ->setLabel("Kota")
        )->addDataContainer(
            EntityListDataContainer::make("customers_count")
                // ->setSortable()
                ->setLabel("Pelanggan")
        )->addDataContainer(
            EntityListDataContainer::make("sort_order")
                ->setSortable()
                ->setLabel("Urutan")
        );
    }

    /**
     * Build list layout using ->addColumn()
     *
     * @return void
     */

    public function buildListLayout()
    {
        $this->addColumn("name", 3, 12)
            ->addColumn("area", 2, 12)
            ->addColumn("city", 2, 12)
            ->addColumn("customers_count", 3, 12)
            ->addColumn("sort_order", 2, 12);
    }

    /**
     * Build list config
     *
     * @return void
     */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->addInstanceCommand("preview", CustomerMapPreview::class)
            ->setDefaultSort('sort_order', 'asc')
            ->setPaginated()
            ->setReorderable(StoreSharpReorderHandler::class);
    }

    /**
     * Retrieve all rows data as array.
     *
     * @param EntityListQueryParams $params
     * @return array
     */
    public function getListData(EntityListQueryParams $params)
    {
        $current_user = auth()->user();
        $stores = Store::distinct();

        if ($current_user->role_id == 3) { // filter for Admin Toko and Driver
            $stores_id = $current_user->stores->pluck('id');
            $stores->whereIn('id', $stores_id);
        }

        if ($params->sortedBy()) {
            if ($params->sortedBy() == 'city') {
                $stores->join('cities', 'cities.id', '=', 'stores.city_id')  //'details' and 'users' is the table name; not the model name
                    ->orderBy('cities.name', $params->sortedDir());
            } elseif ($params->sortedBy() == 'customers_count') {
                // $stores->sortBy(function ($store) {
                //     return $store->addresses->groupBy('customer_id')->count();
                // }, $params->sortedDir());
            } else {
                $stores->orderBy($params->sortedBy(), $params->sortedDir());
            }
        }

        if ($params->hasSearch()) {
            foreach ($params->searchWords() as $word) {
                $stores->where('name', 'like', $word)
                    ->orWhere('area', 'like', $word)
                    ->orWhere('slug', 'like', $word);
            }
        }

        return $this
            ->setCustomTransformer("name", function ($value, $store) {
                $is_comingsoon = $store->is_comingsoon ? ' <strong style="color: red; font-size: 12px !important;"> (COMINGSOON) </strong>' : '';
                $is_holiday = $store->holiday_start ? ' <strong style="color: orange; font-size: 12px !important;"> (LIBUR) </strong>' : '';
                $accurate = $store->accurate_warehouse_name ? '<br><strong style="font-size: 10px !important; color: purple;">ACC Gudang: ' . $store->accurate_warehouse_name . '</strong>' : '';
                return $store->name . $is_comingsoon . $is_holiday . $accurate;
            })
            ->setCustomTransformer("city", function ($value, $store) {
                return $store->city->name;
            })
            ->setCustomTransformer("customers_count", function ($value, $store) {
                $month01 = Carbon::now();
                $month02 = Carbon::now();
                $month03 = Carbon::now();
                $month06 = Carbon::now();
                $month01->subDays(30);
                $month02->subDays(60);
                $month03->subDays(90);
                $month06->subDays(180);

                // $latest_order = DB::table('orders')
                //     ->whereNull('deleted_at')
                //     ->where('store_id', $store->id)
                //     ->where(function ($q) {
                //         $q->where('status_id', 4)
                //             ->orWhere('status_id', 6)
                //             ->orWhere('status_id', 7);
                //     })
                //     ->select('address_id', DB::raw('MAX(created_at) as last_order_created_at'))
                //     ->groupBy('address_id');

                // $address = DB::table('addresses')
                //     // ->groupBy('customer_id')
                //     ->joinSub($latest_order, 'latest_order', function ($join) {
                //         $join->on('addresses.id', '=', 'latest_order.address_id');
                //     })
                //     ->whereNull('deleted_at')
                //     ->where('store_id', $store->id)
                //     ->selectRaw('COUNT(*) as total')
                //     ->selectRaw("COUNT(CASE WHEN last_order_created_at > '" . $month01->toDateString() . "' THEN 1 END) as month0")
                //     ->selectRaw("COUNT(CASE WHEN last_order_created_at <= '" . $month01->toDateString() . "' AND last_order_created_at > '" . $month02->toDateString() . "' THEN 1 END) as month1")
                //     ->selectRaw("COUNT(CASE WHEN last_order_created_at <= '" . $month02->toDateString() . "' AND last_order_created_at > '" . $month03->toDateString() . "' THEN 1 END) as month2")
                //     ->selectRaw("COUNT(CASE WHEN last_order_created_at <= '" . $month03->toDateString() . "' AND last_order_created_at > '" . $month06->toDateString() . "' THEN 1 END) as month3")
                //     ->selectRaw("COUNT(CASE WHEN last_order_created_at <= '" . $month06->toDateString() . "' THEN 1 END) as month6")
                //     ->first();

                // $latest_bbro = DB::table('marketings')
                //     ->whereNull('deleted_at')
                //     ->where('store_id', $store->id)
                //     ->where(function ($q) {
                //         $q->where('status_id', 6);
                //         // ->orWhere('status_id', 6)
                //         // ->orWhere('status_id', 7);
                //     })
                //     ->selectRaw("COUNT(CASE WHEN created_at > '" . $month01->toDateString() . "' THEN 1 END) as month0")
                //     ->first();

                $customers = Customer::distinct()
                    ->whereHas('addresses', function ($query) use ($store) {
                        $query->where('store_id', $store->id);
                    });

                $count_total = (clone $customers)->count();
                $count_lt1_month = (clone $customers)
                    ->whereDate('options->last_order_at', '>=', $month01->toDateString())
                    ->count();
                $count_lt2_month = (clone $customers)
                    ->whereDate('options->last_order_at', '<=', $month01->toDateString())
                    ->whereDate('options->last_order_at', '>', $month02->toDateString())
                    ->count();
                $count_lt3_month = (clone $customers)
                    ->whereDate('options->last_order_at', '<=', $month02->toDateString())
                    ->whereDate('options->last_order_at', '>', $month03->toDateString())
                    ->count();
                $count_lt6_month = (clone $customers)
                    ->whereDate('options->last_order_at', '<=', $month03->toDateString())
                    ->whereDate('options->last_order_at', '>', $month06->toDateString())
                    ->count();
                $count_gt6_month = (clone $customers)
                    ->where(function ($q) use ($month06) {
                        $q->whereDate('options->last_order_at', '<=', $month06->toDateString())
                            ->orWhereNull('options->last_order_at')
                        ;
                    })
                    ->whereNull('options->last_notified_at')
                    ->count();
                $count_gt6_month_and_wbcl = (clone $customers)
                    ->where(function ($q) use ($month06) {
                        $q->whereDate('options->last_order_at', '<=', $month06->toDateString())
                            ->orWhereNull('options->last_order_at')
                        ;
                    })
                    ->whereNotNull('options->last_notified_at')
                    ->count();

                $output = "";
                // $output .= "<div>Total: <strong>" . $store->addresses->groupBy('customer_id')->count() . "</strong></div>";
                // $output .= "<div>Total: <strong>" . DB::table('addresses')->where('store_id', $store->id)->whereNull('deleted_at')->count() . "</strong></div>";
                // $output .= "<div>Total: <strong>" . $address->total . "</strong></div>";
                // $output .= "<div>< 1 bulan tidak order: <strong>" . $address->month0 . "</strong></div>";
                // $output .= "<div>> 1 bulan: <strong>" . $address->month1 . "</strong></div>";
                // $output .= "<div>> 2 bulan: <strong>" . $address->month2 . "</strong></div>";
                // $output .= "<div>> 3 bulan: <strong>" . $address->month3 . "</strong></div>";
                // $output .= "<div>> 6 bulan: <strong>" . $address->month6 . "</strong></div>";
                // $output .= "<div>B-Bro 1 bulan: <strong>" . $latest_bbro->month0 . "</strong></div>";
                $output .= "<div>Total: <strong>" . $count_total . "</strong></div>";
                $output .= "<div>< 1 bulan tidak order: <strong>" . $count_lt1_month . "</strong></div>";
                $output .= "<div>1-2 bulan tidak order: <strong>" . $count_lt2_month . "</strong></div>";
                $output .= "<div>2-3 bulan tidak order: <strong>" . $count_lt3_month . "</strong></div>";
                $output .= "<div>3-6 bulan tidak order: <strong>" . $count_lt6_month . "</strong></div>";
                $output .= "<div>> 6 bulan tidak order: <strong>" . $count_gt6_month . "</strong></div>";
                $output .= "<div>wbcl: <strong>" . $count_gt6_month_and_wbcl . "</strong></div>";
                return $output;
            })
            ->transform($stores->paginate(30));
    }
}
