<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;

class OrderSharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            // 'code' => [
            //     'required',
            //     $this->context()->instanceId() ? 'unique:orders,code,'.$this->context()->instanceId() : 'unique:orders',
            // ],
            // 'created_at' => 'required',
            'store_id' => 'required',
            // 'customer_id' => 'required',
            'address_id' => 'required',
            'status_id' => 'required',
            'driver_id' => 'required_if:status_id,2,3,4',
            // 'received_at' => 'required_if:status_id,4',
            // 'driver_note' => 'required_if:status_id,5',
            // 'orderproducts' => 'required|array',
            'orderproducts.*.product_id' => 'required',
            'orderproducts.*.qty' => 'required',
            // 'feedback_service_rating' => 'required_with:feedback_delivery_rating|required_with:feedback_note',
            // 'feedback_delivery_rating' => 'required_with:feedback_service_rating|required_with:feedback_note',
        ];
    }
}
