<?php

namespace App\Sharp;

use DateTime;
use App\Helper\Helper;
use App\Models\Deposit;
use App\Models\Customer;
use App\Models\Order;
use App\Models\Invoice;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
// use Code16\Sharp\Form\Fields\SharpFormTextField;
// use Code16\Sharp\Form\Fields\SharpFormListField;
use Code16\Sharp\Form\Fields\SharpFormTextareaField;
use Code16\Sharp\Form\Fields\SharpFormDateField;
use Code16\Sharp\Form\Fields\SharpFormUploadField;
// use Code16\Sharp\Form\Fields\SharpFormSelectField;
// use Code16\Sharp\Form\Fields\SharpFormGeolocationField;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use App\Sharp\CustomFormFields\SharpFormTextCustomField;
use Code16\Sharp\Form\SharpForm;
use Code16\Sharp\Http\WithSharpContext;
use Code16\Sharp\Form\Eloquent\Transformers\FormUploadModelTransformer;

class DepositSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater, WithSharpContext;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        return $this
            ->setCustomTransformer("userstamp", function ($value, $model) {
                return [
                    "created_by" => $model->creator ? $model->creator->name : 'System',
                    "created_at" => $model->created_at ? $model->created_at->format('jMy(G:i)') : '-',
                    "updated_by" => $model->editor ? $model->editor->name : 'System',
                    "updated_at" => $model->updated_at ? $model->updated_at->format('jMy(G:i)') : '-',
                ];
            })
            ->setCustomTransformer("order", function ($value, $model) {
                return $model->order ? [
                    "code" => $model->order->code,
                    "store" => $model->order->store->name,
                    "customer" => $model->order->customer->name,
                    "address" => $model->order->address->address,
                ] : [];
            })
            ->setCustomTransformer("invoice", function ($value, $model) {
                return $model->invoice ? [
                    "code" => $model->invoice->code,
                    "store" => $model->invoice->store->name,
                    "customer" => $model->invoice->customer->name,
                ] : [];
            })
            ->setCustomTransformer("total_invoice", function ($value, $model) {
                return $model->invoice ? $model->invoice->total_bill : null;
            })
            ->setCustomTransformer("invoice_discount", function ($value, $model) {
                return $model->invoice ? $model->invoice->discount : null;
            })
            // ->setCustomTransformer("total_after_discount", function ($value, $model) {
            //     return $model->invoice ? $model->invoice->total_after_discount : null;
            // })
            ->setCustomTransformer("invoice_transfer", function ($value, $model) {
                return $model->invoice ? $model->invoice->amount_pay : null;
            })
            ->setCustomTransformer("invoice_note_confirm", function ($value, $model) {
                return $model->invoice ? $model->invoice->note_confirm : null;
            })
            ->setCustomTransformer("nota", function ($value, $model) {
                return $model->order ? $model->order->total : null;
            })
            ->setCustomTransformer("deposit", function ($value, $model) {
                return $model->order ? $model->order->amount_deposit_used : null;
            })
            ->setCustomTransformer("transfer", function ($value, $model) {
                return $model->order ? $model->order->amount_pay : null;
            })
            ->setCustomTransformer("cash", function ($value, $model) {
                return $model->order ? $model->order->amount_split_to_cash : null;
            })
            ->setCustomTransformer("confirm_note", function ($value, $model) {
                return $model->order ? $model->order->confirm_note : null;
            })
            ->setCustomTransformer("driver_note", function ($value, $model) {
                return $model->order ? $model->order->driver_note : null;
            })
            ->setCustomTransformer("order_note", function ($value, $model) {
                return $model->order ? $model->order->note : null;
            })
            ->setCustomTransformer("note", function ($value, $model) {
                return str_replace(' (manual)', '', $value);
            })
            ->setCustomTransformer(
                "paymentproof",
                new FormUploadModelTransformer()
            )
            ->transform(
                Deposit::with(["paymentproof"])->findOrFail($id)
            );
    }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $deposit = $id ? Deposit::findOrFail($id) : new Deposit;

        $helper = new Helper;

        $old_created_at = $id ? new DateTime($deposit->created_at) : new DateTime();
        $old_amount = $id ? $deposit->amount : 0;
        $old_balance = $id ? $deposit->balance : 0;

        // $customer;
        // $customer_id;
        if ($breadcrumb = $this->context()->getPreviousPageFromBreadcrumb("show")) {
            list($type, $entityKey, $instanceId) = $breadcrumb;
            if ($entityKey == "customer") {
                $customer_id = $instanceId;
                $customer = Customer::find($customer_id);
            }
        }

        // PROCESS DEPOSIT DATA
        // --------------------
        $ignores = [
            'userstamp',
            'total_invoice',
            'invoice_discount',
            'total_after_discount',
            'invoice_transfer',
            'invoice_note_confirm',
            'nota',
            'deposit',
            'transfer',
            'cash',
            'order',
            'order_note',
            'driver_note',
            'confirm_note'
        ];
        // ORDER
        if ($deposit->order) {
            Order::where('id', $deposit->order_id)
                ->update([
                    'amount_pay' => $data['transfer'],
                    'amount_split_to_cash' => $data['cash'],
                    'note' => $data['order_note'] ? $data['order_note'] : null,
                    'driver_note' => $data['driver_note'] ? $data['driver_note'] : null,
                    'confirm_note' => $data['confirm_note'] ? $data['confirm_note'] : null,
                ]);
            array_push($ignores, 'paymentproof');
            // INVOICE
        } else if ($deposit->invoice) {
            Invoice::where('id', $deposit->invoice_id)
                ->update([
                    'discount' => $data['invoice_discount'],
                    'total_after_discount' => $data['total_invoice'] - $data['invoice_discount'],
                    'amount_pay' => $data['invoice_transfer'],
                    'note_confirm' => $data['invoice_note_confirm'] ? $data['invoice_note_confirm'] : null,
                ]);
            array_push($ignores, 'paymentproof');
            // MANUAL
        } else {
            // is Creation
            if ($this->context()->isCreation()) {
                $data['customer_id'] = $customer->id;
                $data['balance'] = $data['amount'] + $customer->deposit_amount;
                $customer_id = $data['customer_id'];
                // is Update
            } else {
                $diff_amount = $data['amount'] - $old_amount;
                $data['balance'] = $old_balance + $diff_amount;
                $customer_id = $deposit->customer_id;
            }
            $data['note'] .= ' (manual)';
        }

        if (!$deposit->invoice) {
            $this->ignore($ignores)->save($deposit, $data);
        }


        $new_deposit = Deposit::find($deposit->id);
        $from_datetime = new DateTime($new_deposit->created_at);
        $from_datetime = $old_created_at < $from_datetime ? $old_created_at : $from_datetime;

        $generate = $helper->generateDepositHistory($customer_id, $from_datetime->format('Y-m-d H:i'));

        // $this->notify("Info")
        //     ->setDetail($from_datetime->format('Y-m-d H:i'))
        //     ->setLevelSuccess()
        //     ->setAutoHide(false);

        return $deposit->id;
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        $helper = new Helper;

        $deposit = Deposit::find($id);
        $customer_id = $deposit->customer_id;
        $created_at = $deposit->created_at;
        $from_datetime = new DateTime($created_at);

        Deposit::findOrFail($id)->find($id)->delete();

        $helper->generateDepositHistory($customer_id, $from_datetime->format('Y-m-d H:i'));
    }

    public function create(): array
    {
        $current_time = new DateTime();
        return [
            'created_at' => $current_time->format('Y-m-d H:i')
        ];
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {
        $deposit = Deposit::find($this->context()->instanceId());
        $this
            ->addField(
                SharpFormDateField::make("created_at")
                    ->setLabel("Tanggal & Waktu")
                    // ->setDisplayFormat("HH:mm:ss")
                    // ->setStepTime(1)
                    // ->setHasDate(false)
                    ->setHasTime(true)
                    ->setReadOnly(($deposit && $deposit->order_id) || ($deposit && $deposit->invoice_id) || auth()->user()->role_id >= 3)
            )
            ->addField(
                SharpFormTextCustomField::make('total_invoice')
                    ->setLabel('Total Invoice')
                    ->setInputType('money')
                    ->setReadOnly()
            )
            ->addField(
                SharpFormTextCustomField::make('invoice_discount')
                    ->setLabel('Potongan')
                    ->setInputType('money')
            )
            ->addField(
                SharpFormTextCustomField::make('invoice_transfer')
                    ->setLabel('Jumlah Transfer')
                    ->setInputType('money')
                    ->setReadOnly(auth()->user()->role_id >= 3)
            )
            ->addField(
                SharpFormTextareaField::make('invoice_note_confirm')
                    ->setLabel('Catatan (Confirm) *')
            )
            ->addField(
                SharpFormTextCustomField::make('nota')
                    ->setLabel('Nota')
                    ->setInputType('money')
                    ->setReadOnly()
            )
            ->addField(
                SharpFormTextCustomField::make('deposit')
                    ->setLabel('Deposit')
                    ->setInputType('money')
                    ->setReadOnly()
            )
            ->addField(
                SharpFormTextCustomField::make('transfer')
                    ->setLabel('Transfer')
                    ->setInputType('money')
                    ->setReadOnly(auth()->user()->role_id >= 3)
            )
            ->addField(
                SharpFormTextCustomField::make('cash')
                    ->setLabel('Cash')
                    ->setInputType('money')
                    ->setReadOnly(auth()->user()->role_id >= 3)
            )
            ->addField(
                SharpFormTextCustomField::make('amount')
                    ->setLabel('Deposit Manual (minus/plus) *')
                    ->setInputType('money')
                    ->setReadOnly($this->context()->isUpdate() && auth()->user()->role_id >= 3)
            )
            ->addField(
                SharpFormTextCustomField::make('balance')
                    ->setLabel('Saldo')
                    ->setInputType('money')
                    ->setReadOnly()
            )
            ->addField(
                SharpFormTextareaField::make('note')
                    ->setLabel('Catatan *')
                    ->setReadOnly(($deposit && $deposit->order_id) || auth()->user()->role_id >= 3)
            )
            ->addField(
                SharpFormTextareaField::make('confirm_note')
                    ->setLabel('Catatan (Confirm) *')
            )
            ->addField(
                SharpFormTextareaField::make('driver_note')
                    ->setLabel('Catatan (Driver) *')
            )
            ->addField(
                SharpFormTextareaField::make('order_note')
                    ->setLabel('Catatan (Job) *')
            )
            ->addField(
                SharpFormUploadField::make("paymentproof")
                    ->setLabel("Bukti Transfer")
                    ->setFileFilterImages()
                    ->shouldOptimizeImage()
                    // ->setCompactThumbnail()
                    // ->setCropRatio("1:1", ["jpg","jpeg","png"])
                    ->setStorageDisk("public")
                    ->setStorageBasePath("img/deposit-payment-proof")
                    ->setReadOnly(auth()->user()->role_id >= 3)
            )
            ->addField(
                SharpFormHtmlField::make('order')
                    ->setLabel('Order')
                    ->setTemplatePath("/sharp/templates/order_details.vue")
            )
            ->addField(
                SharpFormHtmlField::make('invoice')
                    ->setLabel('Invoice')
                    ->setTemplatePath("/sharp/templates/invoice_details.vue")
            )
            ->addField(
                SharpFormHtmlField::make('userstamp')
                    ->setTemplatePath("/sharp/templates/userstamp.vue")
            );
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $deposit = Deposit::find($this->context()->instanceId());
        $this->addColumn(6, function (FormLayoutColumn $column) use ($deposit) {
            if ($deposit && $deposit->order_id) {
                $column->withSingleField('order')
                    ->withSingleField('created_at')
                    ->withSingleField('nota')
                    ->withSingleField('deposit')
                    ->withSingleField('transfer')
                    ->withSingleField('cash')
                    ->withSingleField('balance');
            } else if ($deposit && $deposit->invoice_id) {
                $column->withSingleField('invoice')
                    ->withSingleField('created_at')
                    ->withSingleField('total_invoice')
                    ->withSingleField('invoice_discount')
                    ->withSingleField('invoice_transfer');
            } else {
                $column->withSingleField('created_at')
                    ->withSingleField('amount')
                    ->withSingleField('paymentproof');
            }
            if ($deposit && $deposit->order_id && $deposit->order->status_id == 6) {
                $column->withSingleField('confirm_note');
            } elseif ($deposit && $deposit->order_id && $deposit->order->status_id == 5) {
                $column->withSingleField('driver_note');
            } elseif ($deposit && $deposit->order_id && $deposit->order->status_id == 4) {
                $column->withSingleField('driver_note');
            } elseif ($deposit && $deposit->order_id && in_array($deposit->order->status_id, [3, 2, 1])) {
                $column->withSingleField('order_note');
            } elseif ($deposit && $deposit->invoice_id) {
                $column->withSingleField('invoice_note_confirm');
            } else {
                $column->withSingleField('note');
            }
        })->addColumn(12, function (FormLayoutColumn $column) {
            $column->withSingleField('userstamp');
        });
    }
}
