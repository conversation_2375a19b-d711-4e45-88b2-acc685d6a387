<?php

namespace App\Sharp;

use App\Models\CostCategory;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use App\Sharp\Filters\CostCategoryParentFilter;

class CostCategorySharpList extends SharpEntityList
{
    /**
    * Build list containers using ->addDataContainer()
    *
    * @return void
    */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('title')
                ->setLabel('Nama')
                // ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("parent_id")
              ->setLabel("Category")
            //   ->setSortable()
        );
    }

    /**
    * Build list layout using ->addColumn()
    *
    * @return void
    */

    public function buildListLayout()
    {
        $this->addColumn("parent_id", 6, 12)
          ->addColumn("title", 6, 12);
    }

    /**
    * Build list config
    *
    * @return void
    */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('parent_id', 'asc')
            // ->setPaginated()
            ->addFilter("parent_category_id", CostCategoryParentFilter::class)
            ;
    }

    /**
    * Retrieve all rows data as array.
    *
    * @param EntityListQueryParams $params
    * @return array
    */
    public function getListData(EntityListQueryParams $params)
    {
        $model = CostCategory::distinct();
            // ->where('is_root', 0);

        if ($params->sortedBy()) {
            $model->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            foreach ($params->searchWords() as $word) {
                $model->where('title', 'like', $word);
            }
        }

        if ($params->filterFor("parent_category_id")) {
            $id = $params->filterFor("parent_category_id");
            $model->where('parent_id', $id);
        }

        return $this
          ->setCustomTransformer("parent_id", function ($value, $model) {
              return $value ? $model->parent->title : '-';
          })
        //   ->setCustomTransformer("title", function ($value, $model) {
        //     return $model->is_root ? $value : '--- '.$value;
        // })
          ->transform($model->with(["parent"])->paginate(30));
    }
}