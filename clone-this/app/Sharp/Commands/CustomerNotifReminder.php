<?php

namespace App\Sharp\Commands;

use Carbon\Carbon;
// use App\Models\Address;
// use App\Models\Customer;
// use App\Helper\Helper;
use App\Models\Store;
use App\Models\Customer;
use Illuminate\Support\Facades\DB;
use Illuminate\Database\Eloquent\Builder;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Code16\Sharp\EntityList\Commands\EntityCommand;

class CustomerNotifReminder extends EntityCommand
{

    /**
     * @return string
     */
    public function label(): string
    {
        return "Notif Reminder";
    }

    public function description(): string
    {
        return "Kirim notif reminder ke pelanggan";
    }

    // protected function initialData(): array
    // {
    //     return [
    //         "template" => "3",
    //         "test_button" => [
    //             'testval' => 'xxx',
    //         ],
    //     ];
    // }

    public function buildFormFields(): void
    {
        $this
            ->addField(
                SharpFormHtmlField::make("test_button")
                    ->setInlineTemplate(
                        "<div style='display:flex; justify-content:space-between; gap:10px;'>
                        <a target='_blank' href='/test-notif-reminder/template/t1'>Test Template 1 📲</a>
                        <a target='_blank' href='/test-notif-reminder/template/t2'>Test Template 2 📲</a>
                        <a target='_blank' href='/test-notif-reminder/template/t3'>Test Template 3 📲</a>
                        </div>
                        "
                    )
            )
            ->addField(
                SharpFormSelectField::make(
                    'template',
                    ['1' => 'Template 1', '2' => 'Template 2', '3' => 'Template 3']
                )
                    ->setLabel('Template notif *')
                    ->setDisplayAsDropdown()
                    ->setClearable()
                // $this->addField(
                //     SharpFormTextareaField::make("message")
                //         ->setLabel("Message")
            )
        ;
    }

    // protected function initialData(): array
    // {
    //     return [
    //         "store_id" => 5
    //     ];
    // }

    // public function confirmationText()
    // {
    //     return "Apakah anda yakin ingin mengirim notifikasi reminder ke pelanggan?";
    // }

    public function execute(EntityListQueryParams $params, array $data = []): array
    {
        $this->validate($data, [
            "template" => "required"
        ]);

        $date_created = $params->filterFor("date_created");
        $custom_filters = $params->filterFor("custom_filters");
        $last_order = $params->filterFor("last_order");
        $distance_store_id = $params->filterFor("distance_store");
        $max_distance_store = $params->filterFor("max_distance_store");

        $customers = Customer::distinct();

        if ($params->hasSearch()) {
            $words = '';
            foreach ($params->searchWords(false) as $key => $word) {
                if ($key != 0) {
                    $words = $words . ' ';
                }
                $words = $words . $word;
            }
            $words = '%' . $words . '%';
            $customers->where(function ($query) use ($words) {
                $query->whereHas('addresses', function (Builder $subquery) use ($words) {
                    $subquery->where('address', 'like', $words)
                        ->orWhere('latlng', 'like', $words)
                        ->orWhere('label', 'like', $words);
                })
                    ->orWhere('name', 'like', $words)
                    ->orWhere('email', 'like', $words)
                    ->orWhere('phone', 'like', $words);
            });
        }

        if ($params->filterFor("stores")) {
            $ids = $params->filterFor("stores");
            $distance_store_id = $params->filterFor("distance_store");
            $max_distance_store = $params->filterFor("max_distance_store");
            $customers->whereHas('addresses', function (Builder $query) use ($ids, $distance_store_id, $max_distance_store) {
                $query->whereIn('store_id', (array)$ids);
                if ($distance_store_id) {
                    $query->whereHas('storedistances', function (Builder $subquery) use ($distance_store_id, $max_distance_store) {
                        $subquery->where('store_id', $distance_store_id);
                        if ((int)$max_distance_store > 0) {
                            $subquery->where('distance_meter_by_route', "<=", (int)$max_distance_store);
                            // $subquery->where(function ($subsubquery) use ($max_distance_store) {
                            //     $subsubquery
                            //         ->where('distance_meter', "<=", (int)$max_distance_store)
                            //         ->orWhere('distance_meter_by_route', "<=", (int)$max_distance_store);
                            // });
                        }
                    });
                }
            });
        }

        if ($date_created = $params->filterFor("date_created")) {
            // $date_type = $params->filterFor("date_type");
            // if ($date_type == 'custom') {
            $customers->whereBetween("created_at", [
                $date_created['start'],
                $date_created['end'],
            ]);
            // }
        }

        $last_order = $params->filterFor("last_order");

        if ($last_order) {
            $month01 = Carbon::now();
            $month02 = Carbon::now();
            $month03 = Carbon::now();
            $month06 = Carbon::now();
            $month01->subDays(30);
            $month02->subDays(60);
            $month03->subDays(90);
            $month06->subDays(180);
            $latest_order = DB::table('orders')
                ->whereNull('deleted_at')
                ->where(function ($q) {
                    $q->where('status_id', 4)
                        ->orWhere('status_id', 6)
                        ->orWhere('status_id', 7);
                })
                ->select('customer_id', DB::raw('MAX(created_at) as last_order_created_at'))
                ->groupBy('customer_id');
            // $date_type = $params->filterFor("date_type");
            // if ($date_type == 'custom') {
            //     $customers->whereBetween("created_at", [
            //         $date_created['start'],
            //         $date_created['end'],
            //     ]);
            // }
            $customers->joinSub($latest_order, 'latest_order', function ($join) {
                $join->on('customers.id', '=', 'latest_order.customer_id');
            });
            if ($last_order === 'blash_response') {
                $customers
                    ->whereDate('last_order_created_at', '>=', $month01->toDateString())
                    ->whereNotNull('blash_updated_at');
            } else if ($last_order === '<1') {
                $customers->whereDate('last_order_created_at', '>=', $month01->toDateString());
            } else if ($last_order === '>1') {
                $customers->whereDate('last_order_created_at', '<=', $month01->toDateString())
                    ->whereDate('last_order_created_at', '>', $month02->toDateString());
            } else if ($last_order === '>2') {
                $customers->whereDate('last_order_created_at', '<=', $month02->toDateString())
                    ->whereDate('last_order_created_at', '>', $month03->toDateString());
            } else if ($last_order === '>3') {
                $customers->whereDate('last_order_created_at', '<=', $month03->toDateString())
                    ->whereDate('last_order_created_at', '>', $month06->toDateString());
            } else if ($last_order === '>6') {
                $customers->whereDate('last_order_created_at', '<=', $month06->toDateString());
            }
        }

        if ($params->filterFor("custom_filters")) {
            $custom_filters = $params->filterFor("custom_filters");
            if (in_array('empty_coordinate', (array)$custom_filters)) {
                $customers->whereHas('addresses', function (Builder $query) {
                    $query->whereNull('latlng');
                });
            }
            if (in_array('special_price', (array)$custom_filters)) {
                $customers->whereHas('products');
            }
            if (in_array('notif_wa_unset', (array)$custom_filters)) {
                $customers->where('notif_status', 'unset');
            }
            if (in_array('notif_wa_on', (array)$custom_filters)) {
                $customers->where('notif_status', 'on');
            }
            if (in_array('notif_wa_off', (array)$custom_filters)) {
                $customers->where('notif_status', 'off');
            }
            if (in_array('is_company', (array)$custom_filters)) {
                $customers->where('is_company', 1);
            }
            if (in_array('lt2', (array)$custom_filters)) {
                $customers->whereHas('addresses', function (Builder $subquery) {
                    $subquery->where('is_secondfloor', 1);
                });
            }
            if (in_array('duplicated_phone', (array)$custom_filters)) {
                $customers
                    // ->havingRaw('COUNT(phone) > 1')
                    // ->having(DB::raw('COUNT(phone)'), '>', 1)
                    ->whereIn('id', function ($subquery) {
                        $subquery->select('id')->from('customers')->groupBy('phone')->havingRaw('count(*) > 1');
                    });
            }
            if (in_array('blashed', (array)$custom_filters)) {
                $customers->whereNotNull('blash_updated_at');
            }
            if (in_array('unblash', (array)$custom_filters)) {
                $customers->whereNull('blash_updated_at');
            }
            if (in_array('notyet_sync_acc', (array)$custom_filters)) {
                $customers->whereHas('addresses', function (Builder $subquery) {
                    $subquery->whereNull('accurate_customer_id')
                        ->orWhereNull('accurate_customer_code');
                });
            }
        }

        // return $this->info('Berhasil mengirim notifikasi reminder ke pelanggan ' . $data['template']);
        return $this->view("sharp.change-store", compact(
            'stores',
            'store_destination',
            'distance_store',
            'max_distance_store',
            'customers',
            'data'
        ));
    }

    public function authorize(): bool
    {
        return sharp_user()->role_id <= 2;
    }
}
