<?php

namespace App\Sharp\Commands;

use App\Models\Customer;
use App\Models\CustomerProduct;
use App\Models\Product;
use Code16\Sharp\EntityList\Commands\InstanceCommand;
// use Carbon\Carbon;
// use Illuminate\Support\Facades\DB;

class ActivateCustomerLocaleProduct extends InstanceCommand
{

    public function label(): string
    {
        // $customer = Customer::find($instanceId);
        // if ($customer->products) {
        return "Aktifkan Harga Khusus";
        // } else {
        //     return "Non-Aktifkan Harga Khusus";
        // }
    }

    public function description(): string
    {
        return "Harga khusus untuk pelanggan";
    }

    public function confirmationText()
    {
        return "Aktifkan harga khusus untuk pelanggan terpilih?";
    }

    public function execute($instanceId, array $data = []): array
    {
        $customer = Customer::find($instanceId);
        if ($customer->products->count() == 0) {
            $products = Product::all();
            foreach ($products as $product) {
                CustomerProduct::create([
                    'customer_id' => $instanceId,
                    'product_id' => $product->id,
                ]);
            }
            return $this->reload();
        } else {
            return $this->info('Harga khusus suda aktif.');
        }
    }
}
