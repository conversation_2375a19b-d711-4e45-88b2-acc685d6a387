<?php

namespace App\Sharp\Commands;

use App\Models\NotificationSchedule;
use Code16\Sharp\EntityList\Commands\InstanceCommand;

// use Code16\Sharp\Form\Fields\SharpFormTextareaField;

// use Illuminate\Support\Arr;

class WbclActivate extends InstanceCommand
{
    public function label(): string
    {
        return "Activate";
    }

    public function description(): string
    {
        return "Activate notification schedule.";
    }

    // public function buildFormFields(): void
    // {
    //     $this->addField(
    //         SharpFormCheckField::make("is_start_now", "Ya")
    //                 ->setLabel("Kirim sekarang?")
    //     );
    // }

    public function execute($instanceId, array $data = []): array
    {
        // $this->validate($data, [
        //     "is_start_now" => "required"
        // ]);

        // if ($data["is_start_now"] == "error") {
        //     throw new SharpApplicativeException("Driver can't be «error»");
        // }

        $notif_schedule = NotificationSchedule::findOrFail($instanceId);
        $notif_schedule->is_active = 1;
        $notif_schedule->save();

        // $sendNotifReminder = new SendNotifReminder();
        // $sendNotifReminder();

        // return $this->refresh($instanceId);
        return $this->reload();
    }

    public function confirmationText(): string
    {
        return "Activate?";
    }

    public function authorizeFor($instanceId): bool
    {
        $notif_schedule = NotificationSchedule::findOrFail($instanceId);
        $is_active = $notif_schedule->is_active;
        return sharp_user()->role_id <= 2 && $is_active == 0;
        // return sharp_user()->role_id <= 2;
    }
}
