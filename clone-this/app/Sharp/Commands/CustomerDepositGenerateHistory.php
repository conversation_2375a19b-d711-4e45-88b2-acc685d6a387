<?php

namespace App\Sharp\Commands;

use App\Models\Store;
use App\Models\Customer;
use App\Helper\Helper;
use Code16\Sharp\EntityList\Commands\EntityCommand;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Illuminate\Database\Eloquent\Builder;

class CustomerDepositGenerateHistory extends EntityCommand
{

    /**
     * @return string
     */
    public function label(): string
    {
        return "Rapikan Deposit History";
    }

    public function description(): string
    {
        return "Buat ulang dan rapikan deposit history";
    }

    public function buildFormFields(): void
    {
        $this->addField(
            SharpFormSelectField::make(
                'store_id',
                Store::pluck("name", "id")->all(),
            )
                ->setLabel('Toko *')
                ->setDisplayAsDropdown()
        // $this->addField(
        //     SharpFormTextareaField::make("message")
        //         ->setLabel("Message")
        );
    }

    public function execute(EntityListQueryParams $params, array $data=[]): array
    {

        $helper = new Helper;
        $customers = Customer::distinct();

        $store_id = $data['store_id'];
        $customers->whereHas('addresses', function (Builder $query) use ($store_id) {
            $query->where('store_id', $store_id);
        });

        // if ($date_created = $params->filterFor("date_created")) {
        //     $date_type = $params->filterFor("date_type");
        //     if ($date_type == 'custom') {
        //         $customers->whereBetween("created_at", [
        //             $date_created['start'],
        //             $date_created['end'],
        //         ]);
        //     }
        // }

        // if ($params->filterFor("coordinate") == 'empty') {
        //     $customers->whereHas('addresses', function (Builder $query) {
        //         $query->whereNull('latlng');
        //     });
        // }
        
        // set_time_limit(300); // Extends to 5 minutes.
        foreach ($customers->get() as $customer) {
            $helper->generateDepositHistory($customer->id);
        }
        // return $this->reload();
        $store = Store::find($store_id);
        return $this->info('Deposti History semua pelanggan '.$store->name.' berhasil dirapikan!');
    }

    // public function confirmationText()
    // {
    //     return "Tunggu beberapa menit untuk membuat ulang dan merapikan deposit history";
    // }

    public function authorize():bool
    {
        return sharp_user()->role_id <= 2;
    }
}