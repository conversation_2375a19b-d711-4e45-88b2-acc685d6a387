<?php

namespace App\Sharp\Commands;

use App\Models\Order;
use App\Models\Customer;
use Code16\Sharp\EntityList\Commands\InstanceCommand;
use Code16\Sharp\Exceptions\Form\SharpApplicativeException;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormTextareaField;
use Code16\Sharp\Form\Fields\SharpFormUploadField;
use Code16\Sharp\Form\Fields\SharpFormDateField;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
use App\Sharp\CustomFormFields\SharpFormTextLocationField;
use App\Sharp\CustomFormFields\SharpFormTextLocationAccuracyField;
use Illuminate\Database\Eloquent\Builder;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;

// use Code16\Sharp\Form\Fields\SharpFormTextareaField;

// use Illuminate\Support\Arr;

class OrderFinishJob extends InstanceCommand
{
    use WithSharpFormEloquentUpdater;

    public function label(): string
    {
        return "Selesaikan Job";
    }

    public function description(): string
    {
        return "Order sampai tujuan.";
    }

    /**
     * @param $instanceId
     * @return array
     */
    protected function initialData($instanceId): array
    {
        return $this
            ->setCustomTransformer("order_details", function ($value, Order $order) {
                return [
                    'code' => $order->code,
                    'store' => $order->store->name,
                    'customer' => $order->customer->name,
                    'address' => $order->address->address,
                    'products' => $order->products,
                ];
            })
            ->setCustomTransformer("received_at", function ($value, Order $order) {
                return date("Y-m-d H:i");
            })
            ->transform(
                Order::findOrFail($instanceId)
            );
    }

    public function buildFormFields(): void
    {
        $this->addField(
            SharpFormHtmlField::make('order_details')
            ->setTemplatePath("/sharp/templates/order_details.vue")
        )->addField(
            SharpFormTextLocationField::make("received_latlng")
                ->setLabel("Koordinat *")
                // ->setHide()
        )->addField(
            SharpFormTextLocationAccuracyField::make("received_latlng_accuracy")
                ->setLabel("Akurasi Koordinat *")
                ->setHelpMessage('Dalam meter.')
                // ->setHide()
        )->addField(
            SharpFormDateField::make("received_at")
                    ->setLabel("Diterima Tanggal/Waktu")
                    ->setDisplayFormat("YYYY-MM-DD HH:mm")
                    ->setStepTime(1)
                    ->setHasTime(true)
                    ->setReadOnly()
        )->addField(
            SharpFormTextField::make('received_by')
                    ->setLabel('Diterima Oleh *')
                    // ->setReadOnly()
        )->addField(
            SharpFormTextareaField::make('driver_note')
                    ->setLabel('Catatan')
                    ->setRowCount(2)
        )->addField(
            SharpFormUploadField::make("receivephoto")
                ->setLabel("Foto Penerimaan *")
                ->setFileFilterImages()
                ->shouldOptimizeImage()
                // ->setCompactThumbnail()
                // ->setCropRatio("1:1", ["jpg","jpeg","png"])
                ->setStorageDisk("public")
                ->setStorageBasePath("img/order-received")
        );
    }

    public function execute($instanceId, array $data = []): array
    {
        $this->validate($data, [
            "received_by" => "required",
            "receivephoto" => "required"
        ]);

        if ($data["received_by"] == "error") {
            throw new SharpApplicativeException("Penerima can't be «error»");
        }
        if ($data["receivephoto"] == "error") {
            throw new SharpApplicativeException("Foto can't be «error»");
        }

        $order = Order::findOrFail($instanceId);

        $data['status_id'] = 4; // set to Job Selesai
        $this->ignore(['order_details'])->save($order, $data);

        // Send Notification by Email
        $details = Order::where('id', $instanceId)->with(['products', 'customer', 'address', 'store', 'store.banks', 'receivephoto', 'driver'])->first();
        $details['receivephoto_url'] = $details->receivephoto ? $details->receivephoto->thumbnail($width=600, $height=600, $filters=[]) : null;
        $customer = Customer::find($order["customer_id"]);
        if ($customer->email) {
            \Mail::to($customer->email)->send(new \App\Mail\MailDelivered($details));
        }

        return $this->reload();
    }

    // public function confirmationText(): string
    // {
    //     return "Antar order sekarang?";
    // }

    public function authorizeFor($instanceId): bool
    {
        $order = Order::find($instanceId);
        return in_array(auth()->user()->role_id, [5, 6]) && $order->status_id == 3; // only Driver && status Job Berjalan
    }
}
