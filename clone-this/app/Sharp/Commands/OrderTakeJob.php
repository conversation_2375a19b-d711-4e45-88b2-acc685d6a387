<?php

namespace App\Sharp\Commands;

use App\Models\Order;
use Code16\Sharp\EntityList\Commands\InstanceCommand;
use Code16\Sharp\Exceptions\Form\SharpApplicativeException;
use Code16\Sharp\Form\Fields\SharpFormCheckField;
use Illuminate\Database\Eloquent\Builder;

// use Code16\Sharp\Form\Fields\SharpFormTextareaField;

// use Illuminate\Support\Arr;

class OrderTakeJob extends InstanceCommand
{
    public function label(): string
    {
        return "Ambil Job";
    }

    public function description(): string
    {
        return "Ambil order.";
    }

    public function buildFormFields(): void
    {
        $this->addField(
            SharpFormCheckField::make("is_start_now", "Ya")
                    ->setLabel("Kirim sekarang?")
        );
    }

    public function execute($instanceId, array $data = []): array
    {
        // $this->validate($data, [
        //     "is_start_now" => "required"
        // ]);

        // if ($data["is_start_now"] == "error") {
        //     throw new SharpApplicativeException("Driver can't be «error»");
        // }

        $order = Order::findOrFail($instanceId);

        $order->update([
                'status_id' => isset($data['is_start_now']) && $data['is_start_now'] ? 3 : 2, // set to Job Berjalan : Job Diambil
                'driver_id' => auth()->user()->id,
            ]);


        return $this->reload();
    }

    // public function confirmationText(): string
    // {
    //     return "Ambil job & Tunda pengiriman?";
    // }

    public function authorizeFor($instanceId): bool
    {
        $order = Order::find($instanceId);
        return in_array(auth()->user()->role_id, [5, 6]) && $order->status_id == 1; // only Driver && status Job Bebas
    }
}
