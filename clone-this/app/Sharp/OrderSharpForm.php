<?php

namespace App\Sharp;

use App\Models\Order;
use App\Models\Store;
use App\Models\User;
use App\Models\Address;
use App\Models\Status;
use App\Models\Product;
use App\Models\ProductStore;
use App\Models\Feedback;
use App\Models\Deposit;
use App\Helper\Helper;
use App\Models\OrderProduct;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Fields\SharpFormHtmlField;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormDateField;
use Code16\Sharp\Form\Fields\SharpFormTextareaField;
use Code16\Sharp\Form\Fields\SharpFormListField;
use Code16\Sharp\Form\Fields\SharpFormNumberField;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Code16\Sharp\Form\Fields\SharpFormAutocompleteField;
use Code16\Sharp\Form\Fields\SharpFormUploadField;
use Code16\Sharp\Form\Layout\FormLayoutTab;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use Code16\Sharp\Form\Fields\SharpFormGeolocationField;
use App\Sharp\CustomFormFields\SharpFormTextCustomField;
use Code16\Sharp\Form\SharpForm;
use Illuminate\Database\Eloquent\Builder;
use Code16\Sharp\Http\WithSharpContext;
use Code16\Sharp\Form\Eloquent\Transformers\FormUploadModelTransformer;

use DateTime;

class OrderSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater, WithSharpContext;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        return $this->setCustomTransformer("address_id", function ($value, $order) {
            return Address::with(['customer'])->where('id', $order->address_id)->first();
        })->setCustomTransformer("userstamp", function ($value, $order) {
            return [
                "created_by" => $order->creator ? $order->creator->name : 'Customer via App',
                "created_at" => $order->created_at ? $order->created_at->format('jMy(G:i)') : '-',
                "updated_by" => $order->editor ? $order->editor->name : 'Customer via App',
                "updated_at" => $order->updated_at ? $order->updated_at->format('jMy(G:i)') : '-',
            ];
        })->setCustomTransformer("order_info", function ($value, $order) {
            return [
                "is_offline" => $order->is_offline ? 'y' : 'n',
                "customer_latlng" => $order->address ? $order->address->latlng : null,
                "received_latlng" => $order->received_latlng ? $order->received_latlng : '',
                "received_latlng_accuracy" => $order->received_latlng_accuracy ? number_format($order->received_latlng_accuracy, 0, '', '.') : '0',
                "distance_store_customer" => $order->distance_store_customer ? number_format($order->distance_store_customer, 0, '', '.') : '0',
                "distance_customer_received" => $order->distance_customer_received ? number_format($order->distance_customer_received, 0, '', '.') : '0',
                "total" => $order->total ? number_format($order->total, 0, '', '.') : '0',
            ];
            // })->setCustomTransformer("feedback_service_rating", function ($value, $order) {
            //     return $order->feedback ? $order->feedback->service_rating : null;
            // })->setCustomTransformer("feedback_delivery_rating", function ($value, $order) {
            //     return $order->feedback ? $order->feedback->delivery_rating : null;
            // })->setCustomTransformer("feedback_note", function ($value, $order) {
            //     return $order->feedback ? $order->feedback->note : null;
        })->setCustomTransformer("customer_latlng", function ($value, $order) {
            return $order->address ? $order->address->latlng : null;
        })->setCustomTransformer("customer_latlng_info", function ($value, $order) {
            return $order->address ? [
                'title' => 'Pelanggan',
                'value' => $order->address->latlng,
            ] : null;
        })->setCustomTransformer("received_latlng_info", function ($value, $order) {
            return $order->received_latlng ? [
                'title' => 'Penyelesaian',
                'value' => $order->received_latlng,
            ] : null;
        })->setCustomTransformer(
            "receivephoto",
            new FormUploadModelTransformer()
        )->setCustomTransformer(
            "paymentproof",
            new FormUploadModelTransformer()
        )->transform(
            Order::with(['orderproducts', 'feedback', 'receivephoto', 'paymentproof', 'additionalcosts'])->findOrFail($id)
        );
    }

    public function create(): array
    {
        $order = new Order();
        $current_time = new DateTime();
        return $this->transform(new Order([
            // "code" => $order->getCode(),
            'created_at' => $current_time->format('Y-m-d H:i'),
            'status_id' => 1
        ]));
    }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $order = $id ? Order::findOrFail($id) : new Order;
        $old_status_id = $order->status_id;
        // $old_created_at = $id ? new DateTime($order->created_at) : new DateTime($data['created']);
        // $new_created_at = new DateTime($data['created_at']);

        $old_deposit_balance_after = $order->deposit_balance_after;

        // if ($old_created_at != $new_created_at) {
        //     $data['sequence_per_day'] = Order::whereDate('created_at', $new_created_at)->max('sequence_per_day') + 1;
        //     $data['code'] = $order->getCode($data['store_id'], $data['created_at'], $data['sequence_per_day']);
        // }

        if ($data['payment'] != 'cash') {
            $data['amount_will_pay'] = null;
        }
        if (isset($data["address_id"])) {
            $address = Address::find($data["address_id"]);
            $data['customer_id'] = $address->customer_id;
        }

        if (isset($data['receiver_phone'])) {
            $phone = $data['receiver_phone'];
            $phone = str_replace(' ', '', $phone);
            $phone = str_replace('-', '', $phone);
            $phone = str_replace('+', '', $phone);
            $phone = str_replace('(', '', $phone);
            $phone = str_replace(')', '', $phone);
            if (substr($phone, 0, 1) == '0') {
                $phone = '62' . substr($phone, 1);
            }
            $data['receiver_phone'] = $phone;
        }

        if (isset($data["address_id"]) && isset($data["customer_latlng"])) {
            $address = Address::find($data["address_id"]);
            if ($address->latlng != $data["customer_latlng"]) {
                $address->update(['latlng' => $data["customer_latlng"]]);
            }
        }

        $order = $this->ignore([
            'orderproducts',
            'additionalcosts',
            'userstamp',
            'order_info',
            'customer_latlng',
            'customer_latlng_info',
            'received_latlng_info',
            // 'feedback_service_rating',
            // 'feedback_delivery_rating',
            // 'feedback_note',
        ])->save($order, $data);

        if (isset($data["orderproducts"])) {

            // ? is Updating
            if ($this->context()->isUpdate()) {

                // ? #1 Delete OrderProduct (MUST EXECUTE FIRST)
                $order_product_old_list = OrderProduct::where('order_id', $order->id)
                    ->whereNull('deleted_at')
                    ->get();
                foreach ($order_product_old_list as $order_product_old) {
                    $order_product_new_index = array_search($order_product_old->id, array_column($data["orderproducts"], 'id'));
                    if ($order_product_new_index === false) {
                        $order_product_old->delete();
                    }
                }

                // ? 2# Create or Update OrderProduct (LATER)
                foreach ($data["orderproducts"] as $product) {
                    $productstore = ProductStore::where('store_id', $data['store_id'])
                        ->where('product_id', $product['product_id'])
                        ->first();
                    $price = $productstore ? ($productstore->local_price ? $productstore->local_price : $productstore->product->price) : 0;
                    OrderProduct::updateOrCreate(
                        [
                            'order_id' => $order->id,
                            'product_id' => $product['product_id'],
                            'deleted_at' => null,
                        ],
                        [
                            'qty' => $product['qty'],
                            'price' => $price,
                        ]
                    );
                }

                // ? is Creating
            } else {

                // ? Create OrderProduct
                foreach ($data["orderproducts"] as $product) {
                    $productstore = ProductStore::where('store_id', $data['store_id'])
                        ->where('product_id', $product['product_id'])
                        ->first();
                    $price = $productstore ? ($productstore->local_price ? $productstore->local_price : $productstore->product->price) : 0;
                    OrderProduct::create([
                        'order_id' => $order->id,
                        'product_id' => $product['product_id'],
                        'qty' => $product['qty'],
                        'price' => $price,
                    ]);
                }
            }
            // $order->products()->sync($data_products);
        }
        $order = $order->fresh()->load([
            'products',
            'orderproducts',
            'additionalcosts'
        ]);

        // if ($old_status_id != $data['status_id'] && $data['driver_id']) {
        //     // Send Webpushr
        //     $end_point = 'https://api.webpushr.com/v1/notification/send/attribute';
        //     $http_header = array(
        //         "Content-Type: Application/Json",
        //         "webpushrKey: " . env('WEBPUSHR_KEY', 'd52766018d2e49e724b9b7da26dab392'),
        //         "webpushrAuthToken: " . env('WEBPUSHR_AUTH_TOKEN', '20775')
        //     );
        //     $curent_year = date('Y');
        //     $curent_month = date('m');
        //     $curent_date = date('d');
        //     $current_date = $curent_year.$curent_month.$curent_date;
        //     $ch = curl_init();
        //     curl_setopt($ch, CURLOPT_HTTPHEADER, $http_header);
        //     curl_setopt($ch, CURLOPT_URL, $end_point);
        //     curl_setopt($ch, CURLOPT_POST, 1);
        //     curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);

        //     // Send Notification to Driver
        //     $req_data = array(
        //         'title' 		=> "Order ".($old_status_id ? 'Berubah' : 'Baru')." 👤 ".$order->customer->name, //required
        //         'message' 		=> $old_status_id ? 'Lihat detail.' : 'Antar sekarang!', //required
        //         // 'target_url'	=> env('DELIVERY_APP_URL', 'http://delivery.gasplus.online').'/order/'.$order->id, //required
        //         'target_url'	=> env('DELIVERY_APP_URL', 'http://cs2.gasplus.online').'/cs/list/order?filter_status%5B0%5D=1&filter_date='.$current_date.'..'.$current_date.'&page=1', //required
        //         'attribute'		=> array(
        //             'user_id' => $data["driver_id"], // Driver
        //             // 'role_id' => 4, // Admin Toko
        //             // 'store_id' => $store->id
        //         ),
        //         //following parameters are optional
        //         //'name'		=> 'Test campaign',
        //         //'icon'		=> 'https://cdn.webpushr.com/siteassets/wSxoND3TTb.png',
        //         //'image'		=> 'https://cdn.webpushr.com/siteassets/aRB18p3VAZ.jpeg',
        //         //'auto_hide'	=> 1,
        //         //'expire_push'	=> '5m',
        //         //'send_at'		=> '2020-12-28 06:17 +5:30',
        //         //'action_buttons'=> array(
        //             //array('title'=> 'Demo', 'url' => 'https://www.webpushr.com/demo'),
        //             //array('title'=> 'Rates', 'url' => 'https://www.webpushr.com/pricing')
        //         //)
        //     );
        //     curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($req_data));
        //     $response = curl_exec($ch);

        //     // Send via WhatsApp
        //     if (json_decode($response)->status == 'failure') {
        //     }
        // }

        // if (isset($data["feedback_service_rating"]) && isset($data["feedback_delivery_rating"])) {
        //     $data_feedback = [
        //         'service_rating' => $data['feedback_service_rating'],
        //         'delivery_rating' => $data['feedback_delivery_rating'],
        //         'note' => $data['feedback_note'],
        //     ];
        //     $order->feedback()->updateOrCreate(['order_id'=>$order->id], $data_feedback);
        // } else {
        //     $order->feedback()->delete();
        // }

        $helper = new Helper;
        if ($this->context()->isUpdate()) {
            if (isset($data["additionalcosts"]) && count($data["additionalcosts"]) > 0) {
                $order->setAdditionalCost();
                // if ($order->additionalcosts()->count() == 0) {
                // $order->setAdditionalCost(true);
                // }
            } else {
                $order->additionalcosts()->delete();
            }
            // $order->setAdditionalCost();
            $order->countTotal();
            $diff_deposit_balance_after = $order->deposit_balance_after - $old_deposit_balance_after;
            if ($diff_deposit_balance_after != 0) {
                $helper->generateDepositHistory($order->customer_id, $order->created_at);
            }
            // ? Accurate update Invoice
            $accurate_result = $helper->accurateUpsertInvoice($order);
            // $this->notify('Data berhasil disimpan!')
            //     ->setDetail(json_encode($accurate_result))
            //     ->setLevelSuccess()
            //     ->setAutoHide(false);
        } else {
            $order->setAdditionalCost();
            $order->countTotal();
            Deposit::create([
                'order_id' => $order->id,
                'customer_id' => $order->customer_id,
                'amount' => $order->amount_deposit_used * -1,
                'balance' => $order->deposit_balance_after,
                'note' => 'ADD JOB (via dashboard)',
            ]);
            $order->customer->setDeposit($order->deposit_balance_after);

            if ($order->store->is_notif_wa) {
                $messages = [];
                $msg_order_new = $order->store->msg_order_new ? $order->store->msg_order_new : 'Mohon menunggu. Pesanan sedang kami proses.';
                // Admin Toko
                // if ($order->store->whatsapp_2) {
                //     array_push($messages, [
                //         'order_id'      => $order->id,
                //         'to'      => $helper->convertPhone($order->store->whatsapp_2),
                //         'from'    => $order->store->chat_server_url,
                //         'message'    => '📥 *New Job* by CS '.config('app.url').'/manager/job-list/'.$order->store->slug.'?date='.date_format($order->created_at, 'Y-m-d').'&status=total&search='.$order->code.' '.$order->customer->name,
                //         'timeout'    => 1000,
                //     ]);
                // }
                // Customer
                if ($order->customer->notif_status === 'on') {
                    $msg_order_info = $helper->createOrderMessage($order);
                    array_push($messages, [
                        'order_id'      => $order->id,
                        'to'      => $helper->convertPhone($order->receiver_phone ? $order->receiver_phone : $order->customer->phone),
                        'message'    => $msg_order_info ? $msg_order_info . $msg_order_new : $msg_order_new,
                        // 'footer' => 'Tidak ingin menerima pesan ini? Klik link di bawah:',
                        // 'button_links' => [
                        //     [
                        //         'index' => 1,
                        //         'urlButton' => [
                        //             'displayText' => '🛑 Berhenti menerima pesan dari kami!',
                        //             'url' => 'https://cs.ordergasplus.online'
                        //         ]
                        //     ]
                        // ]
                    ]);
                }
                $helper->queueSendNotif($messages);
            }

            // ? Accurate create Invoice
            $accurate_result = $helper->accurateUpsertInvoice($order, true);
            // $this->notify('Data berhasil disimpan!')
            //     ->setDetail(json_encode($accurate_result))
            //     ->setLevelSuccess()
            //     ->setAutoHide(false);
        }
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        $helper = new Helper;
        $order = Order::findOrFail($id)->find($id);
        $helper->generateDepositHistory($order->customer_id, $order->created_at);
        $order->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {
        $current_user = auth()->user();
        $order = Order::find($this->context()->instanceId());
        $is_readonly = $current_user->role_id > 2 && $order->confirmed_by;

        $this->addField(
            SharpFormTextField::make('code')
                ->setLabel('Kode *')
                ->setReadOnly()
        )->addField(
            SharpFormDateField::make("created_at")
                ->setLabel("Tanggal & Waktu")
                // ->setDisplayFormat("HH:mm:ss")
                // ->setStepTime(1)
                // ->setHasDate(false)
                ->setHasTime(true)
        )->addField(
            SharpFormSelectField::make(
                'payment',
                [
                    'cash' => "Cash",
                    'transfer' => "Transfer Bank",
                    'qris' => "QRIS",
                ]
            )
                ->setLabel('Pembayaran (by Customer)')
                ->setClearable()
                ->setDisplayAsDropdown()
                ->setReadOnly($is_readonly)
            // )->addField(
            //     SharpFormCheckField::make("is_second_floor", "Ya, tambah Biaya setiap ORDER")
            //         ->setLabel("Jasa lantai 2 @Rp3.000?")
        )->addField(
            SharpFormSelectField::make(
                'payment_method_ask',
                [
                    'cash' => "Cash",
                    'non-cash' => "Non-Cash (Transfer Bank/QRIS)",
                ]
            )
                ->setLabel('Pembayaran (Confirm by Driver)')
                ->setClearable()
                ->setDisplayAsDropdown()
                ->setReadOnly($is_readonly)
        )->addField(
            SharpFormSelectField::make(
                'payment_method_confirmed',
                [
                    'cash' => "Cash",
                    'transfer' => "Transfer Bank",
                    'qris' => "QRIS",
                    'invoice' => "INVOICE",
                ]
            )
                ->setLabel('Pembayaran (Confirm by CS)')
                ->setClearable()
                ->setDisplayAsDropdown()
                ->setReadOnly($is_readonly)
        )->addField(
            SharpFormTextareaField::make('confirm_note')
                ->setLabel('Catatan Confirm')
                ->setRowCount(1)
        )->addField(
            SharpFormSelectField::make(
                'store_id',
                Store::pluck("name", "id")->all()
            )
                ->setLabel('Toko *')
                ->setClearable()
                ->setDisplayAsDropdown()
            // )->addField(
            //     SharpFormAutocompleteField::make("customer_id", "remote")
            //         ->setDataWrapper("data")
            //         ->setLabel('Pelanggan *')
            //         ->setPlaceholder('Cari "nama" pelanggan')
            //         // ->setLocalSearchKeys(["name"])
            //         ->setListItemInlineTemplate("{{name}}")
            //         ->setResultItemInlineTemplate("{{name}}")
            //         ->setDynamicRemoteEndpoint("/customers/store/{{store_id}}")
            //         ->setSearchMinChars(2)
            //         ->setRemoteMethodPOST()
        )->addField(
            SharpFormAutocompleteField::make("address_id", "remote")
                // ->setDataWrapper("data")
                ->setLabel('Pelanggan *')
                ->setPlaceholder('Cari "nama/hp/alamat" pelanggan')
                // ->setLocalSearchKeys(["address"])
                // ->setListItemInlineTemplate("{{address}}")
                // ->setResultItemInlineTemplate("{{address}}")
                ->setListItemTemplatePath("/sharp/templates/customeraddress_list.vue")
                ->setResultItemTemplatePath("/sharp/templates/customeraddress_result.vue")
                ->setDynamicRemoteEndpoint("/customersaddresses/store/{{store_id}}")
                ->setSearchMinChars(3)
            // ->setPlaceholder('Tekan "spasi" untuk refresh')
            // ->setRemoteMethodPOST()
        )->addField(
            SharpFormTextField::make('receiver_phone')
                ->setLabel('No. WhatsApp Pemesan')
                ->setHelpMessage('Selain No.WA asli pelanggan')
        )->addField(
            SharpFormSelectField::make(
                'driver_id',
                User::whereHas('role', function (Builder $query) {
                    $query->where('title', 'Driver');
                })->pluck("name", "id")->all()
            )
                ->setLabel('Delivery Man *')
                ->setClearable()
                ->setDisplayAsDropdown()
        )->addField(
            SharpFormSelectField::make(
                'status_id',
                Status::pluck("name", "id")->all()
            )
                ->setLabel('Status *')
                ->setClearable()
                ->setDisplayAsDropdown()
        )->addField(
            SharpFormTextareaField::make('note')
                ->setLabel('Catatan by Pelanggan (KELUAR DI NOTIF)')
                ->setRowCount(1)
        )->addField(
            SharpFormTextareaField::make('note_for_driver')
                ->setLabel('Catatan untuk Delman')
                ->setRowCount(1)
        )->addField(
            SharpFormTextCustomField::make("amount_will_pay")
                ->setLabel("Akan dibayar (Pelanggan)")
                // ->setPrepend("Rp")
                ->setInputType('money')
                ->addConditionalDisplay('payment', ['cash']) // admin toko / driver
        )->addField(
            SharpFormHtmlField::make('customer_latlng_info')
                ->setTemplatePath("/sharp/templates/latlng_info.vue")
        )->addField(
            SharpFormHtmlField::make('received_latlng_info')
                ->setTemplatePath("/sharp/templates/latlng_info.vue")
        )->addField(
            SharpFormHtmlField::make('userstamp')
                ->setTemplatePath("/sharp/templates/userstamp.vue")
        )->addField(
            SharpFormHtmlField::make('order_info')
                ->setTemplatePath("/sharp/templates/order_info.vue")
        )->addField(
            SharpFormListField::make("orderproducts")
                ->setLabel("List Barang *")
                ->setAddText("Tambah Produk")
                ->setAddable()
                ->setRemovable()
                ->setItemIdAttribute("id")
                // ->setReadOnly($this->context()->isUpdate())
                ->setReadOnly($is_readonly)
                // ->setReadOnly($current_user->role_id )
                ->addItemField(
                    //     SharpFormSelectField::make(
                    //         "product_id",
                    //         Product::all()
                    //             ->mapWithKeys(function ($store) {
                    //                 return [
                    //                     $store->id => collect($store->products)
                    //                         ->mapWithKeys(function ($values, $key) {
                    //                             return [$key => $key];
                    //                         })->all()
                    //                 ];
                    //             })
                    //         ->all()
                    //     )
                    //         ->setLabel("Produk *")
                    //         ->setDisplayAsDropdown()
                    //         // ->setOptionsLinkedTo("store_id")
                    // )
                    SharpFormAutocompleteField::make("product_id", "local")
                        ->setLabel("Produk *")
                        ->setLocalSearchKeys(["code", "name"])
                        // ->setDataWrapper("data")
                        ->setItemIdAttribute('id')
                        ->setListItemTemplatePath("/sharp/templates/product_list_no_price.vue")
                        ->setResultItemTemplatePath("/sharp/templates/product_result_no_price.vue")
                        // ->setDynamicRemoteEndpoint("/products/store/{{store_id}}")
                        ->setSearchMinChars(2)
                        // ->setRemoteMethodPOST()
                        ->setLocalValues(
                            Product::orderBy("sort_order")->get()
                        )
                )
                // ->addItemField(
                //     SharpFormHtmlField::make("product_info")
                //         // ->setLabel("")
                //         ->setInlineTemplate(
                //             "<strong>{{ id }}</strong> Nama Produk <em>- Rp123.000</em>"
                //         )
                // )
                ->addItemField(
                    SharpFormNumberField::make("qty")
                        ->setLabel("Jumlah *")
                        ->setMin(1)
                        // ->setPlaceholder('999')
                        ->setShowControls(false)
                )
        )
            ->addField(
                SharpFormListField::make("additionalcosts")
                    ->setLabel("Biaya Tambahan")
                    ->setAddText("Tambah")
                    ->setAddable()
                    ->setRemovable()
                    ->setItemIdAttribute("id")
                    ->setMaxItemCount(1)
                    // ->setReadOnly($this->context()->isUpdate())
                    ->setReadOnly($is_readonly)
                    // ->setReadOnly($current_user->role_id )
                    ->addItemField(
                        SharpFormSelectField::make(
                            "name",
                            ['Biaya antar lantai atas' => 'Biaya antar lantai atas']
                        )
                            ->setLabel("Nama *")
                            ->setDisplayAsDropdown()
                        // ->setOptionsLinkedTo("store_id")
                    )
                // ->addItemField(
                //     SharpFormNumberField::make("qty")
                //         ->setLabel("Jumlah *")
                //         ->setMin(1)
                //         // ->setPlaceholder('999')
                //         ->setShowControls(false)
                // )
            );
        // )->addField(
        //     SharpFormSelectField::make(
        //         'feedback_service_rating',
        //         [
        //                 '1' => 'Bintang 1',
        //                 '2' => 'Bintang 2',
        //                 '3' => 'Bintang 3',
        //                 '4' => 'Bintang 4',
        //                 '5' => 'Bintang 5',
        //             ]
        //     )
        //             ->setLabel('Rating Servis')
        //             ->setClearable()
        //             ->setDisplayAsDropdown()
        // )->addField(
        //     SharpFormSelectField::make(
        //         'feedback_delivery_rating',
        //         [
        //                     '1' => 'Bintang 1',
        //                     '2' => 'Bintang 2',
        //                     '3' => 'Bintang 3',
        //                     '4' => 'Bintang 4',
        //                     '5' => 'Bintang 5',
        //                 ]
        //     )
        //                 ->setLabel('Rating Delivery')
        //                 ->setClearable()
        //                 ->setDisplayAsDropdown()
        // )->addField(
        //     SharpFormTextareaField::make('feedback_note')
        //             ->setLabel('Feedback')
        //             ->setRowCount(1)
        // );
        if ($this->context()->isUpdate()) {
            $this->addField(
                SharpFormGeolocationField::make("customer_latlng")
                    ->setDisplayUnitDecimalDegrees()
                    ->setGeocoding()
                    // ->setInitialPosition(-7.7921967, 110.3699972)
                    ->setZoomLevel(18)
                    ->setMapsProvider('osm')
                    ->setGeocodingProvider('osm')
                    // ->setApiKey(env("GMAPS_KEY", "AIzaSyB7Yvda9jQtLu_UmFUh6bBouhxquJfNwtc"))
                    ->setLabel("Koordinat Pelanggan (Pengiriman)")
                // ->setReadOnly()
            )->addField(
                SharpFormGeolocationField::make("received_latlng")
                    ->setDisplayUnitDecimalDegrees()
                    // ->setGeocoding()
                    // ->setInitialPosition(-7.7921967, 110.3699972)
                    ->setZoomLevel(18)
                    ->setMapsProvider('osm')
                    ->setGeocodingProvider('osm')
                    // ->setApiKey(env("GMAPS_KEY", "AIzaSyB7Yvda9jQtLu_UmFUh6bBouhxquJfNwtc"))
                    ->setLabel("Koordinat Penyelesaian")
                    ->setReadOnly()
            );
        }

        if (auth()->user()->role_id <= 3) { // only for Super Admin, Owner and CS
            $this->addField(
                SharpFormDateField::make("received_at")
                    ->setLabel("Diterima Tanggal/Waktu")
                    ->setDisplayFormat("YYYY-MM-DD HH:mm")
                    ->setStepTime(1)
                    ->setHasTime(true)
                    ->setReadOnly()
            )->addField(
                SharpFormTextField::make('received_by')
                    ->setLabel('Diterima Oleh')
                    ->setReadOnly()
            )->addField(
                SharpFormTextareaField::make('driver_note')
                    ->setLabel('Catatan dari Delivery Man')
                    ->setRowCount(1)
                    ->setReadOnly()
            )->addField(
                SharpFormUploadField::make("receivephoto")
                    ->setLabel("Foto Penerimaan")
                    ->setFileFilterImages()
                    ->shouldOptimizeImage()
                    // ->setCompactThumbnail()
                    // ->setCropRatio("1:1", ["jpg","jpeg","png"])
                    ->setStorageDisk("public")
                    ->setStorageBasePath("img/order-received")
                    ->setReadOnly()
            )->addField(
                SharpFormUploadField::make("paymentproof")
                    ->setLabel("Bukti Transfer")
                    ->setFileFilterImages()
                    ->shouldOptimizeImage()
                    // ->setCompactThumbnail()
                    // ->setCropRatio("1:1", ["jpg","jpeg","png"])
                    ->setStorageDisk("public")
                    ->setStorageBasePath("img/payment-proof")
                    ->setReadOnly()
            );
        }
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        // $this->addTab("Order", function (FormLayoutTab $tab) {
        $this->addColumn(6, function (FormLayoutColumn $column) {
            $column->withSingleField('code')
                ->withSingleField("created_at")
                ->withSingleField("store_id")
                // ->withSingleField("customer_id")
                ->withSingleField("address_id")
                ->withSingleField("receiver_phone")
                ->withSingleField("note")
                ->withSingleField("note_for_driver")
                ->withSingleField("payment")
                ->withSingleField("payment_method_ask")
                ->withSingleField("payment_method_confirmed")
                ->withSingleField("confirm_note")
                ->withSingleField("amount_will_pay");
            // ->withSingleField("total")
            // ->withSingleField("payment");
        })->addColumn(6, function (FormLayoutColumn $column) {
            $column->withSingleField('status_id')
                ->withSingleField("driver_id");

            if (auth()->user()->role_id <= 3) { // only for Super Admin, Owner and CS
                $column->withSingleField("received_at")
                    ->withSingleField("received_by")
                    ->withSingleField("driver_note")
                    ->withSingleField("receivephoto")
                    ->withSingleField("paymentproof");
            }
        })->addColumn(12, function (FormLayoutColumn $column) {
            $column->withSingleField("orderproducts", function (FormLayoutColumn $listItem) {
                $listItem->withFields("product_id|9", "qty|3");
            });
        })->addColumn(12, function (FormLayoutColumn $column) {
            $column->withSingleField("additionalcosts", function (FormLayoutColumn $listItem) {
                $listItem->withFields("name|9");
            });
        });

        if ($this->context()->isUpdate()) {
            $this->addColumn(12, function (FormLayoutColumn $column) {
                $column->withFields('customer_latlng|6', 'received_latlng|6');
            })->addColumn(12, function (FormLayoutColumn $column) {
                $column->withFields('customer_latlng_info|6', 'received_latlng_info|6');
            });
        }

        $this->addColumn(12, function (FormLayoutColumn $column) {
            $column->withSingleField('order_info');
        })->addColumn(12, function (FormLayoutColumn $column) {
            $column->withSingleField('userstamp');
        });
        // })->addTab("Feedback", function (FormLayoutTab $tab) {
        //     $tab->addColumn(6, function (FormLayoutColumn $column) {
        //         $column->withSingleField('feedback_service_rating')
        //             ->withSingleField('feedback_delivery_rating')
        //             ->withSingleField('feedback_note');
        //     });
        // })->addTab("Diterima", function (FormLayoutTab $tab) {
        //     $tab->addColumn(6, function (FormLayoutColumn $column) {
        //         $column->withSingleField('feedback_service_rating')
        //             ->withSingleField('feedback_delivery_rating')
        //             ->withSingleField('feedback_note');
        //     });
        // });
    }
}
