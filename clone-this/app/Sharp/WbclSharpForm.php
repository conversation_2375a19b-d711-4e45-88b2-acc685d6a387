<?php

namespace App\Sharp;

use App\Models\Store;
use Code16\Sharp\Form\SharpForm;
use App\Models\NotificationSchedule;
use Code16\Sharp\Http\WithSharpContext;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Fields\SharpFormCheckField;
use Code16\Sharp\Form\Fields\SharpFormSelectField;
use Code16\Sharp\Form\Fields\SharpFormUploadField;
use Code16\Sharp\Form\Fields\SharpFormMarkdownField;
use Code16\Sharp\Form\Fields\SharpFormTextareaField;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Eloquent\Transformers\FormUploadModelTransformer;

class WbclSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater, WithSharpContext;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        $data = NotificationSchedule::with([
            'notificationlogs',
            'notifreminderimage',
        ])->findOrFail($id);

        $data->store_ids = isset($data->others['store_ids']) ? $data->others['store_ids'] : null;
        return $this
            // ->setCustomTransformer("userstamp", function ($value, $model) {
            //     return [
            //         "created_by" => $model->creator ? $model->creator->name : 'System',
            //         "created_at" => $model->created_at ? $model->created_at->format('jMy(G:i)') : '-',
            //         "updated_by" => $model->editor ? $model->editor->name : 'System',
            //         "updated_at" => $model->updated_at ? $model->updated_at->format('jMy(G:i)') : '-',
            //     ];
            // })
            ->setCustomTransformer(
                "notifreminderimage",
                new FormUploadModelTransformer()
            )
            ->transform($data);
    }

    // public function create(): array
    // {
    //     return $this->transform(new NotificationSchedule([
    //         "notification_is_active" => 1
    //     ]));
    // }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $model = $id ? NotificationSchedule::findOrFail($id) : new NotificationSchedule;

        $data['others'] = $data['others'] ?? [];

        if (isset($data['store_ids'])) {
            $data['others']['store_ids'] = $data['store_ids'];
        }
        $model = $this
            ->ignore([
                'store_ids',
            ])
            ->save($model, $data);
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        NotificationSchedule::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {

        $this
            ->addField(
                SharpFormTextField::make("title")
                    ->setLabel('Nama *')
            )
            ->addField(
                SharpFormSelectField::make(
                    "store_ids",
                    Store::orderBy("sort_order")->pluck("name", "id")->all()
                )
                    ->setMultiple()
                    ->setDisplayAsList()
                    // ->setDisplayAsDropdown()
                    // ->setClearable()
                    ->setLabel("Toko *")
            )
            ->addField(
                SharpFormSelectField::make(
                    "criteria",
                    [
                        "test" => "Super Admin & Owner",
                        "gt1-lt2-month" => "1-2 bulan tidak order",
                        "gt2-lt3-month" => "2-3 bulan tidak order",
                        "gt3-lt6-month" => "3-6 bulan tidak order",
                        "gt6-month" => "> 6 bulan tidak order",
                    ]
                )
                    ->setDisplayAsDropdown()
                    ->setClearable()
                    ->setLabel("Target *")
            )
            // ->addField(
            //     SharpFormMarkdownField::make("message_template")
            //         ->setLabel('Message Notif *')
            //         ->setToolbar([
            //             SharpFormMarkdownField::B,
            //             SharpFormMarkdownField::I,
            //             SharpFormMarkdownField::UL,
            //             SharpFormMarkdownField::OL,
            //         ])
            //         ->setHelpMessage('Gunakan tag {{nama_pelanggan}} untuk menampilkan nama pelanggan. Gunakan tag {{nama_toko}} untuk menampilkan nama toko. Gunakan {{link_unsubscribe}} untuk menampilkan link unsubscribe.')
            // )
            ->addField(
                SharpFormTextareaField::make("message_template")
                    ->setLabel('Message Notif *')
                    ->setRowCount(10)
                    // ->setToolbar([
                    //     SharpFormMarkdownField::B,
                    //     SharpFormMarkdownField::I,
                    //     SharpFormMarkdownField::UL,
                    //     SharpFormMarkdownField::OL,
                    // ])
                    ->setHelpMessage('Opsi tag: {nama_pelanggan} {nama_toko} {link_unsubscribe} {link_feedback:pindah_rumah} {link_feedback_with_input:kecewa}')
            )
            ->addField(
                SharpFormUploadField::make("notifreminderimage")
                    ->setLabel("Image on Message")
                    ->setFileFilterImages()
                    ->shouldOptimizeImage()
                    // ->setCompactThumbnail()
                    // ->setCropRatio("1:1", ["jpg","jpeg","png"])
                    ->setStorageDisk("public")
                    ->setStorageBasePath("img/notif")
            )
            ->addField(
                SharpFormCheckField::make("is_active", "Aktif")
                    ->setLabel("WBCL?")
            )
        ;
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $this
            ->addColumn(6, function (FormLayoutColumn $column) {
                $column
                    ->withSingleField("title")
                    ->withSingleField("store_ids")
                    ->withSingleField("criteria")
                    ->withSingleField("message_template")
                    ->withSingleField("notifreminderimage")
                    ->withSingleField("is_active")
                ;
            })
            // ->addColumn(12, function (FormLayoutColumn $column) {
            //     $column->withSingleField('userstamp');
            // })
        ;
    }
}
