<?php

namespace App\Sharp;

use App\Models\Permission;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use Code16\Sharp\Utils\LinkToEntity;

class PermissionSharpList extends SharpEntityList
{
    /**
    * Build list containers using ->addDataContainer()
    *
    * @return void
    */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('title')
                ->setLabel('Nama')
                ->setSortable()
                // ->setHtml()
        )->addDataContainer(
            EntityListDataContainer::make("roles")
                // ->setSortable()
                ->setLabel("Roles")
                // ->setHtml()
        );
    }

    /**
    * Build list layout using ->addColumn()
    *
    * @return void
    */

    public function buildListLayout()
    {
        $this->addColumn("title", 6, 12)
            ->addColumn("roles", 6, 12);
    }

    /**
    * Build list config
    *
    * @return void
    */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('title', 'asc')
            ->setPaginated();
    }

    /**
    * Retrieve all rows data as array.
    *
    * @param EntityListQueryParams $params
    * @return array
    */
    public function getListData(EntityListQueryParams $params)
    {
        $permissions = Permission::distinct();

        if ($params->sortedBy()) {
            $permissions->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            foreach ($params->searchWords() as $word) {
                $permissions->where('title', 'like', $word);
            }
        }

        return $this
            // ->setCustomTransformer("permissions", function ($value, $permission) {
            //     return $permission->roles->title;
            // })
            ->setCustomTransformer("roles", function ($roles, $permission) {
                return $permission->roles->map(function ($role) {
                    // return $roles->title;
                    return (new LinkToEntity($role->title, "role"))
                        ->setTooltip("See related roles")
                        // ->setSearch($role->title)
                        ->toFormOfInstance($role)
                        ->render();
                })->implode(" | ");
                // });
            })
            ->transform($permissions->with(["roles"])->paginate(30));
    }
}
