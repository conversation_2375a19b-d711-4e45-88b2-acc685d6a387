<?php

namespace App\Sharp;

use App\Models\Employee;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use Code16\Sharp\Utils\LinkToEntity;
// use App\Sharp\Filters\EmployeeRoleFilter;
// use App\Sharp\Filters\EmployeeStoreFilter;
use Illuminate\Database\Eloquent\Builder;

class EmployeeSharpList extends SharpEntityList
{
    /**
     * Build list containers using ->addDataContainer()
     *
     * @return void
     */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('full_name')
                ->setLabel('<PERSON>a <PERSON>')
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("armada")
                // ->setSortable()
                ->setLabel("Armada")
        )->addDataContainer(
            EntityListDataContainer::make("user")
                // ->setSortable()
                ->setLabel("User Akun")
        )->addDataContainer(
            EntityListDataContainer::make("role")
                // ->setSortable()
                ->setLabel("Role")
        );
    }

    /**
     * Build list layout using ->addColumn()
     *
     * @return void
     */

    public function buildListLayout()
    {
        $this->addColumn("full_name", 3, 12)
            ->addColumn("user", 3, 12)
            ->addColumn("role", 3, 12)
            ->addColumn("armada", 3, 12);
    }

    /**
     * Build list config
     *
     * @return void
     */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('full_name', 'asc')
            // ->addFilter("role", EmployeeRoleFilter::class)
            // ->addFilter("store", EmployeeStoreFilter::class)
            ->setPaginated();
    }

    /**
     * Retrieve all rows data as array.
     *
     * @param EntityListQueryParams $params
     * @return array
     */
    public function getListData(EntityListQueryParams $params)
    {
        $model = Employee::distinct();

        if ($params->sortedBy()) {
            $model->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            foreach ($params->searchWords() as $word) {
                $model->where('full_name', 'like', $word);
            }
        }

        // if ($role_filter = $params->filterFor("role")) {
        //     $model->where("role_id", $role_filter);
        // }

        // if ($stores_filter = $params->filterFor("store")) {
        //     $model->where(function (Builder $query) use ($stores_filter) {
        //         $query->where("store_id", $stores_filter)
        //             ->orWhereHas('stores', function (Builder $subquery) use ($stores_filter) {
        //                 $subquery->where('id', $stores_filter);
        //             });
        //     });
        // }

        return $this
            ->setCustomTransformer("user", function ($value, $model) {
                return (new LinkToEntity('<i class="fas fa-user mr-1"></i>' . $model->user->name . ($model->user->deleted_at ? ' 🔴 DELETED' : ''), "user"))
                    ->setTooltip("See related user")
                    // ->setSearch($model->role->title)
                    ->toFormOfInstance($model->user)
                    ->render();
                // $role_link = (new LinkToEntity('<i class="fas fa-tag mr-1"></i>'.$model->role->title, "role"))
                //     ->setTooltip("See related role")
                //     // ->setSearch($model->role->title)
                //     ->toFormOfInstance($model->role)
                //     ->render();
                // $store_link = $model->store ? (new LinkToEntity('<p class="mt-2"><i class="fas fa-store mr-1"></i>'.$model->store->name.'</p>', "store"))
                //     ->setTooltip("See related store")
                //     // ->setSearch($model->store->name)
                //     ->toFormOfInstance($model->store)
                //     ->render() : '';
                // $stores = $model->stores->map(function ($store) {
                //     return (new LinkToEntity('<p class="mt-2"><i class="fas fa-store mr-1"></i>'.$store->name.'</p>', "store"))
                //         ->setTooltip("See related store")
                //         // ->setSearch($store->title)
                //         ->toFormOfInstance($store)
                //         ->render();
                // })->implode("");
                // return $role_link . ($stores ? $stores : $store_link);
            })
            ->setCustomTransformer("role", function ($value, $model) {
                return $model->user->role->title;
                // $role_link = (new LinkToEntity('<i class="fas fa-tag mr-1"></i>'.$model->role->title, "role"))
                //     ->setTooltip("See related role")
                //     // ->setSearch($model->role->title)
                //     ->toFormOfInstance($model->role)
                //     ->render();
                // $store_link = $model->store ? (new LinkToEntity('<p class="mt-2"><i class="fas fa-store mr-1"></i>'.$model->store->name.'</p>', "store"))
                //     ->setTooltip("See related store")
                //     // ->setSearch($model->store->name)
                //     ->toFormOfInstance($model->store)
                //     ->render() : '';
                // $stores = $model->stores->map(function ($store) {
                //     return (new LinkToEntity('<p class="mt-2"><i class="fas fa-store mr-1"></i>'.$store->name.'</p>', "store"))
                //         ->setTooltip("See related store")
                //         // ->setSearch($store->title)
                //         ->toFormOfInstance($store)
                //         ->render();
                // })->implode("");
                // return $role_link . ($stores ? $stores : $store_link);
            })
            ->setCustomTransformer("armada", function ($value, $model) {
                return $model->armada ? (new LinkToEntity('<i class="fas fa-motorcycle mr-1"></i>' . $model->armada->licence_number, "armada"))
                    ->setTooltip("See related armada")
                    // ->setSearch($model->role->title)
                    ->toFormOfInstance($model->armada)
                    ->render() : '-';
                // $role_link = (new LinkToEntity('<i class="fas fa-tag mr-1"></i>'.$model->role->title, "role"))
                //     ->setTooltip("See related role")
                //     // ->setSearch($model->role->title)
                //     ->toFormOfInstance($model->role)
                //     ->render();
                // $store_link = $model->store ? (new LinkToEntity('<p class="mt-2"><i class="fas fa-store mr-1"></i>'.$model->store->name.'</p>', "store"))
                //     ->setTooltip("See related store")
                //     // ->setSearch($model->store->name)
                //     ->toFormOfInstance($model->store)
                //     ->render() : '';
                // $stores = $model->stores->map(function ($store) {
                //     return (new LinkToEntity('<p class="mt-2"><i class="fas fa-store mr-1"></i>'.$store->name.'</p>', "store"))
                //         ->setTooltip("See related store")
                //         // ->setSearch($store->title)
                //         ->toFormOfInstance($store)
                //         ->render();
                // })->implode("");
                // return $role_link . ($stores ? $stores : $store_link);
            })
            ->transform($model->with([
                "armada",
                "profilephoto",
                "kkphoto",
                "ktpphoto",
                "simphoto",
                "user",
            ])->paginate(30));
    }
}
