<?php

namespace App\Sharp;

use App\Models\Order;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use Code16\Sharp\Utils\LinkToEntity;
use App\Sharp\Filters\OrderStatusFilter;
use App\Sharp\Filters\OrderStoreFilter;
use App\Sharp\Filters\OrderDateFilter;
use App\Sharp\Filters\OrderViaFilter;
use App\Sharp\Filters\OrderDriverFilter;
use App\Sharp\Filters\OrderTypeFilter;
use App\Sharp\Commands\OrderTakeJob;
use App\Sharp\Commands\OrderStartJob;
use App\Sharp\Commands\OrderFinishJob;
use App\Sharp\Commands\OrderCancelJob;
use App\Sharp\Commands\OrderAssignDriver;
// use App\Sharp\Commands\AllDataSync;
use Illuminate\Database\Eloquent\Builder;

class OrderSharpList extends SharpEntityList
{
    /**
     * Build list containers using ->addDataContainer()
     *
     * @return void
     */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('order')
                ->setLabel('Order')
            // ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("customer")
                // ->setSortable()
                ->setLabel("Pelanggan")
        )->addDataContainer(
            EntityListDataContainer::make("products")
                // ->setSortable()
                ->setLabel("Produk")
        )->addDataContainer(
            EntityListDataContainer::make("created_at")
                ->setSortable()
                ->setLabel("Waktu")
        );
    }

    /**
     * Build list layout using ->addColumn()
     *
     * @return void
     */

    public function buildListLayout()
    {
        $this->addColumn("order", 3, 12)
            ->addColumn("customer", 3, 12)
            ->addColumn("products", 3, 12)
            ->addColumn("created_at", 3, 12);
    }

    /**
     * Build list config
     *
     * @return void
     */
    public function buildListConfig()
    {
        $current_user = auth()->user();

        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('created_at', 'desc')
            ->setPaginated()

            // ->addEntityCommand("synchronize", AllDataSync::class)
            ->addInstanceCommand("assign_driver", OrderAssignDriver::class)
            ->addInstanceCommand("take_job", OrderTakeJob::class)
            ->addInstanceCommand("start_job", OrderStartJob::class)
            ->addInstanceCommand("finish_job", OrderFinishJob::class)
            ->addInstanceCommand("cancel_job", OrderCancelJob::class);

        if ($current_user->role_id <= 3) { // except Admin Toko and Driver
            $this->addFilter("store", OrderStoreFilter::class);
        }

        $this->addFilter("status", OrderStatusFilter::class)
            ->addFilter("date", OrderDateFilter::class);

        if ($current_user->role_id <= 3) { // except Admin Toko and Driver
            $this->addFilter("via", OrderViaFilter::class);
        }

        $this
            ->addFilter("drivers", OrderDriverFilter::class)
            ->addFilter("types", OrderTypeFilter::class);
    }

    /**
     * Retrieve all rows data as array.
     *
     * @param EntityListQueryParams $params
     * @return array
     */
    public function getListData(EntityListQueryParams $params)
    {
        $current_user = auth()->user();
        $orders = Order::distinct()->orderBy('status_id');

        if (!$params->filterFor("store")) {
            if ($current_user->role_id == 3) { // filter for Admin Toko and Driver
                $stores_id = $current_user->stores->pluck('id');
                $orders->whereIn('store_id', $stores_id);
            }

            if ($current_user->role_id >= 4) { // filter for Admin Toko and Driver
                $orders->where('store_id', $current_user->store_id);
            }
        }

        if (in_array($current_user->role_id, [5, 6])) { // filter for Driver
            $orders->where(function ($query) use ($current_user) {
                $query->where('status_id', 1)
                    ->orWhere(function ($query) use ($current_user) {
                        $query->where('status_id', '>', 1)
                            ->where('driver_id', $current_user->id);
                    });
            });
        }

        if ($params->sortedBy()) {
            $orders->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            $words = '';
            foreach ($params->searchWords(false) as $key => $word) {
                if ($key != 0) {
                    $words = $words . ' ';
                }
                $words = $words . $word;
            }
            $words = '%' . $words . '%';
            $orders->where(function ($parentquery) use ($words) {
                $parentquery->whereHas('products', function (Builder $query) use ($words) {
                    $query->where('slug', 'like', $words)
                        ->orWhere('name', 'like', $words)
                        ->orWhere('code', 'like', $words);
                })->orWhereHas('customer', function (Builder $query) use ($words) {
                    $query->where('name', 'like', $words)
                        ->orWhere('phone', 'like', $words)
                        ->orWhere('email', 'like', $words);
                })->orWhereHas('address', function (Builder $query) use ($words) {
                    $query->where('address', 'like', $words)
                        ->orWhere('latlng', 'like', $words)
                        ->orWhere('label', 'like', $words);
                })->orWhereHas('feedback', function (Builder $query) use ($words) {
                    $query->where('note', 'like', $words);
                })->orWhere('code', 'like', $words)
                    ->orWhere('total', 'like', $words)
                    ->orWhere('note', 'like', $words)
                    ->orWhere('received_by', 'like', $words)
                    ->orWhere('driver_note', 'like', $words);
            });
        }

        if ($date = $params->filterFor("date")) {
            $orders->whereBetween("created_at", [
                $date['start'],
                $date['end'],
            ]);
        }

        if ($store = $params->filterFor("store")) {
            $orders->whereIn('store_id', (array)$store);
        }

        if ($drivers = $params->filterFor("drivers")) {
            $orders->whereIn('driver_id', (array)$drivers);
        }

        if ($status = $params->filterFor("status")) {
            $orders->whereIn('status_id', (array)$status);
        }

        if ($via = $params->filterFor("via")) {
            if ($via == 'system') {
                $orders->whereNotNull('created_by'); // via system by cs
            } elseif ($via == 'app') {
                $orders->whereNull('created_by'); // via app by customer
            }
        }

        if ($types = $params->filterFor("types")) {
            if (in_array('lt2', (array)$types)) {
                $orders->where(function (Builder $qsub) {
                    $qsub->whereHas(
                        'orderproducts',
                        function (Builder $sub) {
                            $sub
                                ->where('product_id', '154');
                        }
                    )
                        ->orWhereHas(
                            'additionalcosts',
                            function (Builder $sub) {
                                $sub
                                    ->where('name', 'Biaya antar lantai atas');
                            }
                        );
                });
            }
            if (in_array('notyet_sync_acc', (array)$types)) {
                $orders->where(function (Builder $qsub) {
                    $qsub->doesntHave('accurateInvoice')
                        // ->orDoesntHave('accurateReceipt')
                    ;
                });
            }
        }

        return $this
            ->setCustomTransformer("order", function ($value, $order) {
                $code = '<div>' . $order->code . '</div>';
                $status = $order->status ? '<div style="color: ' . $order->status->color . ';"><i class="' . $order->status->icon . ' mr-1"></i><strong>' . $order->status->name . '</strong></div>' : null;
                $store = $order->store ? '<div><i class="mr-1 fas fa-store"></i>' . $order->store->name . '</div>' : null;
                $gps_off = !$order->received_latlng ? '<span class="ml-1" style="color: red;">📵 GPS OFF</span>' : null;
                $driver = $order->driver ? '<div><i class="mr-1 fas fa-motorcycle"></i>' . $order->driver->name . $gps_off . '</div>' : null;
                $accurate_invoice = $order->accurateInvoice ? '<br><strong style="font-size: 10px !important; color: purple;">Invoice: ' . $order->accurateInvoice->accurate_no . ($order->accurateInvoice->synced_at ? ' / ' . date_format(date_create($order->accurateInvoice->synced_at), 'jMy(G:i)') : '') . ($order->accurateInvoice->error_message ? ' (' . $order->accurateInvoice->error_message . ')' : '') . '</strong>' : '';
                $accurate_receipt = $order->accurateReceipt ? '<br><strong style="font-size: 10px !important; color: purple;">Invoice: ' . $order->accurateReceipt->accurate_no . ($order->accurateReceipt->synced_at ? ' / ' . date_format(date_create($order->accurateReceipt->synced_at), 'jMy(G:i)') : '') . ($order->accurateReceipt->error_message ? ' (' . $order->accurateReceipt->error_message . ')' : '') . '</strong>' : '';
                $feedback = null;
                if ($order->feedback) {
                    $service = '<i class="mr-1 fas fa-star" style="color: gold;"></i>Ser <strong>' . ($order->feedback->service_rating ? $order->feedback->service_rating : '-') . '</strong>';
                    $delivery = '<i class="ml-2 mr-1 fas fa-medal" style="color: gold;"></i>Del <strong>' . ($order->feedback->delivery_rating ? $order->feedback->delivery_rating : '-') . '</strong>';
                    $feedback = '<div>' . (new LinkToEntity($service . $delivery, "feedback"))
                        ->setTooltip("See related feedback")
                        ->toFormOfInstance($order->feedback)
                        // ->setSearch($product->name)
                        ->render() . '</div>';
                }
                return $code . $store . $status . $driver . $feedback . $accurate_invoice . $accurate_receipt;
            })
            ->setCustomTransformer("customer", function ($value, $order) {
                $customer = '<div><i class="mr-1 fas fa-user"></i>' . $order->customer->name . '</div>';
                $whatsapp = '<div><a href="https://wa.me/' . ($order->receiver_phone ? $order->receiver_phone : $order->customer->phone) . '" target="_blank"><i class="mr-1 fab fa-whatsapp"></i>' . ($order->receiver_phone ? $order->receiver_phone : $order->customer->phone) . '</a></div>';
                $address = '<div><i class="mr-1 fas fa-home"></i>' . $order->address->address . '</div>';
                $map = '<div><a href="http://www.google.com/maps/place/' . $order->address->latlng . '" target="_blank"><i class="mr-1 fas fa-location-arrow"></i>Go to ' . $order->address->latlng . '</a></div>';
                return $customer . $whatsapp . $address . $map;
            })
            ->setCustomTransformer("products", function ($value, $order) {
                $products = $order->products->map(function ($product) {
                    // return $permission->title;
                    // return (new LinkToEntity($product->name . ' ('.$product->pivot->qty.'x)', "product"))
                    //     ->setTooltip("See related product")
                    //     ->toFormOfInstance($product)
                    //     // ->setSearch($product->name)
                    //     ->render();
                    return '<strong style="color: green;">🟢 ' . $product->name . ' (' . $product->pivot->qty . 'x)</strong>';
                })->implode("<br>");
                // });
                $total = '<div><strong>TOTAL: Rp' . number_format($order->total, 0, '', '.') . '</strong>' . ($order->payment ? ' (' . $order->payment . ')' : null) . '</div>';
                $amount_deposit_used = $order->amount_deposit_used != 0 ? '<div><strong>' . ($order->amount_deposit_used < 0 ? 'KURANG BAYAR' : 'DEPOSIT') . ':</strong> Rp' . number_format($order->amount_deposit_used, 0, '', '.') . '</div>' : null;
                $total_after_deposit = $order->amount_deposit_used != 0 ? '<div><strong>TAGIHAN:</strong> Rp' . number_format($order->total_after_deposit, 0, '', '.') . '</div>' : null;
                $amount_pay = $order->payment == 'cash' && $order->amount_will_pay ? '<div><strong>Bayar:</strong> Rp' . number_format($order->amount_will_pay, 0, '', '.') . '</div>' : null;
                $amount_return = $order->payment == 'cash' && $order->amount_will_pay ? '<div><strong>Kembalian:</strong> Rp' . number_format($order->amount_will_pay - $order->total, 0, '', '.') . '</div>' : null;
                $order_note = $order->note ? '<div><strong style="color: red;">📝 ' . $order->note . ' (by Pelanggan)</strong></div>' : null;
                $order_note_for_driver = $order->note_for_driver ? '<div><strong style="color: blue;">🛵 ' . $order->note_for_driver . ' (for Delman)</strong></div>' : null;
                return $products . $total . $amount_deposit_used . $total_after_deposit . $amount_pay . $amount_return . $order_note . $order_note_for_driver;
            })
            ->setCustomTransformer("created_at", function ($value, $order) {
                $created_at = '<strong>Created</strong> ' . $order->created_at->format('jMy(G:i)') . "<br>";
                $created_by = '<strong>Via</strong> ' . ($order->created_by ? 'System by CS' : 'App by Customer') . '<br>';
                $received_at = $order->received_at ? '<strong>Received</strong> ' . date_format(date_create($order->received_at), 'jMy(G:i)') . '<br>' : null;
                $received_by = $order->received_by ? '<strong>By</strong> ' . $order->received_by . "<br>" : null;
                $driver_note = $order->driver_note ? '<strong>Note</strong> ' . $order->driver_note : null;
                return $created_at . $created_by . $received_at . $received_by . $driver_note;
            })
            ->transform($orders->with(["products", "store", "customer", "driver", "status", "address", "feedback"])->paginate(30));
    }
}
