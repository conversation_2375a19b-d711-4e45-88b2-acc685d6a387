<?php

namespace App\Sharp\Filters;

use App\Models\Status;
use Code16\Sharp\EntityList\EntityListSelectMultipleFilter;

class OrderStatusFilter implements EntityListSelectMultipleFilter
{
    public function values()
    {
        return Status::orderBy("id")
            ->get()
            ->map(function (Status $status) {
                return [
                    "id" => $status->id,
                    "label" => $status->name,
                    "icon" => $status->icon,
                    "color" => $status->color,
                    // "count" => $status->orders->count(),
                ];
            })
            ->all();
    }

    public function label()
    {
        return "Status";
    }

    // public function defaultValue()
    // {
    //     return Status::pluck("id");
    // }

    // public function retainValueInSession()
    // {
    //     return true;
    // }

    // public function isSearchable(): bool
    // {
    //     return true;
    // }

    // public function searchKeys(): array
    // {
    //     return ["name"];
    // }

    public function template(): string
    {
        // return '<i v-bind:class="[\'mr-1\', icon]" v-bind:style="{ color: color}"></i>{{ label }} <strong>{{ count }}</strong>';
        return '<div v-bind:style="{ color: color}"><i v-bind:class="[\'mr-1\', icon]"></i><strong>{{ label }}</strong></div>';
    }
}
