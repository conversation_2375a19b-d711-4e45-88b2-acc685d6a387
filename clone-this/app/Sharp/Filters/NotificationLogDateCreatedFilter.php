<?php

namespace App\Sharp\Filters;

use Carbon\Carbon;
use Code16\Sharp\EntityList\EntityListDateRangeFilter;

class NotificationLogDateCreatedFilter implements EntityListDateRangeFilter
{
    public function label()
    {
        return "Tanggal Kirim";
    }

    /**
     * @return array
     *
     * awaited format:
     *
     *    [
     *       "start" => Carbon::yesterday(),
     *       "end" => Carbon::today(),
     *    ]
     *
     * @throws \Exception
     */
    public function defaultValue()
    {
        return [
            "start" => Carbon::yesterday(),
            "end" => Carbon::today(),
        ];
    }

    public function dateFormat()
    {
        return "YYYY-MM-DD";
    }

    // public function isMondayFirst()
    // {
    //     return false;
    // }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}
