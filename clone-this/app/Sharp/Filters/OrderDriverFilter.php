<?php

namespace App\Sharp\Filters;

use App\Models\User;
use Code16\Sharp\EntityList\EntityListSelectMultipleFilter;

class OrderDriverFilter implements EntityListSelectMultipleFilter
{
    public function values()
    {
        // if (auth()->user()->role_id == 3) {
        //     return auth()->user()->stores()->pluck("name", "id")->all();
        // } else {
            return User::where('role_id', 5)->pluck("name", "id")->all();
        // }
    }

    public function label()
    {
        return "Delman";
    }

    public function isSearchable(): bool
    {
        return true;
    }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}