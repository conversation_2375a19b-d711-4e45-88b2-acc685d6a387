<?php

namespace App\Sharp\Filters;

use Code16\Sharp\EntityList\EntityListSelectRequiredFilter;

class TransactionTypeFilter implements EntityListSelectRequiredFilter
{
    public function values()
    {
        return [
            'all' => 'Semua',
            'deposit_only' => 'Deposit Only',
        ];
    }

    public function defaultValue()
    {
        return 'all';
    }

    public function label()
    {
        return "Tipe";
    }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}