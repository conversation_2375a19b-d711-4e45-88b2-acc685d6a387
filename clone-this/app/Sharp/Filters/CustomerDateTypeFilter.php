<?php

namespace App\Sharp\Filters;

use Code16\Sharp\EntityList\EntityListSelectRequiredFilter;

class CustomerDateTypeFilter implements EntityListSelectRequiredFilter
{
    public function values()
    {
        return [
          'all' => 'Semua',
          'custom' => 'Custom',
        ];
    }

    public function defaultValue()
    {
        return 'custom';
    }

    public function label()
    {
        return "Tanggal";
    }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}
