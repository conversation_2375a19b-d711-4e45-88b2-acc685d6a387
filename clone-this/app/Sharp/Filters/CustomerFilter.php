<?php

namespace App\Sharp\Filters;

use Code16\Sharp\EntityList\EntityListSelectMultipleFilter;

class CustomerFilter implements EntityListSelectMultipleFilter
{
    public function values()
    {
        return [
            'is_wbcl' => 'Sudah WBCL',
            'no_sc' => 'No Social Chat',
            'notyet_sync_acc' => 'Belum Sync ACC',
            'notyet_sync_acc' => 'Belum Sync ACC',
            'is_company' => 'Sebagai Perusahaan',
            'empty_coordinate' => 'Koordinat Kosong',
            'special_price' => 'Harga Khusus Aktif',
            'notif_wa_unset' => 'Notif WA - UNSET',
            'notif_wa_on' => 'Notif WA 📳 ON ',
            'notif_wa_off' => 'Notif WA 🛑 OFF',
            'lt2' => 'Alamat Lantai 2',
            'duplicated_phone' => 'No. WhatsApp Duplikat',
            'blashed' => 'Sudah Blash',
            'unblash' => 'Belum Blash',
            'distance' => '<PERSON><PERSON><PERSON> (ON)',
        ];
    }

    // public function defaultValue()
    // {
    //     return 'all';
    // }

    public function label()
    {
        return "Filter";
    }

    // public function retainValueInSession()
    // {
    //     return true;
    // }
}
