<?php

namespace App\Sharp\Filters;

use Code16\Sharp\EntityList\EntityListSelectFilter;

class OrderViaFilter implements EntityListSelectFilter
{
    public function values()
    {
        return [
            'app' => "App by Customer",
            'system' => "System by CS",
        ];
    }

    public function label()
    {
        return "Via";
    }

    // public function defaultValue()
    // {
    //     return Status::pluck("id");
    // }

    // public function retainValueInSession()
    // {
    //     return true;
    // }

    // public function isSearchable(): bool
    // {
    //     return true;
    // }

    // public function searchKeys(): array
    // {
    //     return ["name"];
    // }

    // public function template(): string
    // {
    //     return '<i v-bind:class="[\'mr-1\', icon]" v-bind:style="{ color: color}"></i>{{ label }} <strong>{{ count }}</strong>';
    // }
}
