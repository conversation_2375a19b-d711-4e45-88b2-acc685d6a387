<?php

namespace App\Sharp;

use App\Models\Armada;
use Code16\Sharp\EntityList\Containers\EntityListDataContainer;
use Code16\Sharp\EntityList\EntityListQueryParams;
use Code16\Sharp\EntityList\SharpEntityList;
use Code16\Sharp\Utils\LinkToEntity;
// use App\Sharp\Filters\ArmadaRoleFilter;
// use App\Sharp\Filters\ArmadaStoreFilter;
use Illuminate\Database\Eloquent\Builder;

class ArmadaSharpList extends SharpEntityList
{
    /**
    * Build list containers using ->addDataContainer()
    *
    * @return void
    */
    public function buildListDataContainers()
    {
        $this->addDataContainer(
            EntityListDataContainer::make('licence_number')
                ->setLabel('Nomor Polisi')
                ->setSortable()
        )->addDataContainer(
            EntityListDataContainer::make("employee")
                // ->setSortable()
                ->setLabel("SDM")
        )->addDataContainer(
            EntityListDataContainer::make("store")
                // ->setSortable()
                ->setLabel("Toko")
        )
        ;
    }

    /**
    * Build list layout using ->addColumn()
    *
    * @return void
    */

    public function buildListLayout()
    {
        $this->addColumn("licence_number", 4, 12)
            ->addColumn("employee", 3, 12)
            ->addColumn("store", 3, 12)
            ;
    }

    /**
    * Build list config
    *
    * @return void
    */
    public function buildListConfig()
    {
        $this->setInstanceIdAttribute('id')
            ->setSearchable()
            ->setDefaultSort('licence_number', 'asc')
            // ->addFilter("role", ArmadaRoleFilter::class)
            // ->addFilter("store", ArmadaStoreFilter::class)
            ->setPaginated();
    }

    /**
    * Retrieve all rows data as array.
    *
    * @param EntityListQueryParams $params
    * @return array
    */
    public function getListData(EntityListQueryParams $params)
    {
        $model = Armada::distinct();

        if ($params->sortedBy()) {
            $model->orderBy($params->sortedBy(), $params->sortedDir());
        }

        if ($params->hasSearch()) {
            foreach ($params->searchWords() as $word) {
                $model->where('licence_number', 'like', $word);
            }
        }

        // if ($role_filter = $params->filterFor("role")) {
        //     $model->where("role_id", $role_filter);
        // }

        // if ($stores_filter = $params->filterFor("store")) {
        //     $model->where(function (Builder $query) use ($stores_filter) {
        //         $query->where("store_id", $stores_filter)
        //             ->orWhereHas('stores', function (Builder $subquery) use ($stores_filter) {
        //                 $subquery->where('id', $stores_filter);
        //             });
        //     });
        // }

        return $this
            ->setCustomTransformer("employee", function ($value, $model) {
                return $model->employee ? (new LinkToEntity('<i class="fas fa-user mr-1"></i>'.$model->employee->full_name, "user"))
                    ->setTooltip("See related SDM")
                    // ->setSearch($model->role->title)
                    ->toFormOfInstance($model->employee)
                    ->render() : '-';
                // $role_link = (new LinkToEntity('<i class="fas fa-tag mr-1"></i>'.$model->role->title, "role"))
                //     ->setTooltip("See related role")
                //     // ->setSearch($model->role->title)
                //     ->toFormOfInstance($model->role)
                //     ->render();
                // $store_link = $model->store ? (new LinkToEntity('<p class="mt-2"><i class="fas fa-store mr-1"></i>'.$model->store->name.'</p>', "store"))
                //     ->setTooltip("See related store")
                //     // ->setSearch($model->store->name)
                //     ->toFormOfInstance($model->store)
                //     ->render() : '';
                // $stores = $model->stores->map(function ($store) {
                //     return (new LinkToEntity('<p class="mt-2"><i class="fas fa-store mr-1"></i>'.$store->name.'</p>', "store"))
                //         ->setTooltip("See related store")
                //         // ->setSearch($store->title)
                //         ->toFormOfInstance($store)
                //         ->render();
                // })->implode("");
                // return $role_link . ($stores ? $stores : $store_link);
            })
            ->setCustomTransformer("store", function ($value, $model) {
                return $model->employee ? ($model->employee->user->store ? $model->employee->user->store->name : '-') : '-';
                // $role_link = (new LinkToEntity('<i class="fas fa-tag mr-1"></i>'.$model->role->title, "role"))
                //     ->setTooltip("See related role")
                //     // ->setSearch($model->role->title)
                //     ->toFormOfInstance($model->role)
                //     ->render();
                // $store_link = $model->store ? (new LinkToEntity('<p class="mt-2"><i class="fas fa-store mr-1"></i>'.$model->store->name.'</p>', "store"))
                //     ->setTooltip("See related store")
                //     // ->setSearch($model->store->name)
                //     ->toFormOfInstance($model->store)
                //     ->render() : '';
                // $stores = $model->stores->map(function ($store) {
                //     return (new LinkToEntity('<p class="mt-2"><i class="fas fa-store mr-1"></i>'.$store->name.'</p>', "store"))
                //         ->setTooltip("See related store")
                //         // ->setSearch($store->title)
                //         ->toFormOfInstance($store)
                //         ->render();
                // })->implode("");
                // return $role_link . ($stores ? $stores : $store_link);
            })
            ->transform($model->with([
                "employee",
                "employee.user",
                "employee.user.store",
            ])->paginate(30));
    }
}