<?php

namespace App\Sharp;

use App\Models\Status;
use Code16\Sharp\Form\Eloquent\WithSharpFormEloquentUpdater;
use Code16\Sharp\Form\Fields\SharpFormTextField;
use Code16\Sharp\Form\Layout\FormLayoutColumn;
use App\Sharp\CustomFormFields\SharpFormTextCustomField;
use Code16\Sharp\Form\SharpForm;

class StatusSharpForm extends SharpForm
{
    use WithSharpFormEloquentUpdater;

    /**
     * Retrieve a Model for the form and pack all its data as JSON.
     *
     * @param $id
     * @return array
     */
    public function find($id): array
    {
        return $this->transform(
            Status::findOrFail($id)
        );
    }

    /**
     * @param $id
     * @param array $data
     * @return mixed the instance id
     */
    public function update($id, array $data)
    {
        $status = $id ? Status::findOrFail($id) : new Status;
        $this->save($status, $data);
    }

    /**
     * @param $id
     */
    public function delete($id)
    {
        Status::findOrFail($id)->find($id)->delete();
    }

    /**
     * Build form fields using ->addField()
     *
     * @return void
     */
    public function buildFormFields()
    {
        $this->addField(
            SharpFormTextField::make('name')
                ->setLabel('Nama Status *')
        )->addField(
            SharpFormTextCustomField::make("code")
                ->setLabel("Kode *")
                ->setSlugify()
        )->addField(
            SharpFormTextField::make('icon')
                ->setLabel('Icon *')
                ->setHelpMessage('Lihat di 🌐 https://www.fontawesomecheatsheet.com/font-awesome-cheatsheet-5x')
        )->addField(
            SharpFormTextField::make('color')
                ->setLabel('Warna Icon *')
        );
    }

    /**
     * Build form layout using ->addTab() or ->addColumn()
     *
     * @return void
     */
    public function buildFormLayout()
    {
        $this->addColumn(6, function (FormLayoutColumn $column) {
            $column->withSingleField('code')
                ->withSingleField("name")
                ->withSingleField("icon")
                ->withSingleField("color");
        });
    }
}
