<?php

namespace App\Sharp;

use App\Models\User;
use Code16\Sharp\Show\Fields\SharpShowTextField;
use Code16\Sharp\Show\Layout\ShowLayoutColumn;
use Code16\Sharp\Show\Layout\ShowLayoutSection;
use Code16\Sharp\Show\SharpSingleShow;

class AccountSharpShow extends SharpSingleShow
{
    public function buildShowFields(): void
    {
        $this
            ->addField(
                SharpShowTextField::make("name")
                    ->setLabel("Nama:")
            )->addField(
                SharpShowTextField::make("phone")
                    ->setLabel("No. WhatsApp:")
            )->addField(
                SharpShowTextField::make("email")
                    ->setLabel("Email:")
            )->addField(
                SharpShowTextField::make("notification_is_active")
                    ->setLabel("Notifikasi:")
            );
    }

    public function buildShowLayout(): void
    {
        $this
            ->addSection('Profil', function (ShowLayoutSection $section) {
                $section
                    ->addColumn(7, function (ShowLayoutColumn $column) {
                        $column
                            ->withSingleField("name")
                            ->withSingleField("phone")
                            ->withSingleField("email")
                            ->withSingleField("notification_is_active");
                    });
            });
    }

    // public function buildShowConfig(): void
    // {
    //     $this
    //         ->addInstanceCommand("rename", AccountUpdateName::class)
    //         ->setEntityState("status", AccountStatusState::class);
    // }

    public function findSingle(): array
    {
        return $this->setCustomTransformer(
            "notification_is_active",
            function ($value, $model) {
                return $value == 0 ? 'Non-aktif' : 'Aktif';
            }
        )->transform(User::findOrFail(auth()->id()));
    }
}
