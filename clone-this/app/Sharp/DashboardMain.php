<?php

namespace App\Sharp;

use App\SpaceshipType;
use Code16\Sharp\Dashboard\DashboardQueryParams;
use Code16\Sharp\Dashboard\Layout\DashboardLayoutRow;
use Code16\Sharp\Dashboard\SharpDashboard;
use Code16\Sharp\Dashboard\Widgets\SharpPanelWidget;

class DashboardMain extends SharpDashboard
{
    public function buildWidgets()
    {
        $this->addWidget(
            SharpPanelWidget::make("welcome")
                ->setInlineTemplate("<h1>👋 Halo</h1>")
        );
    }

    public function buildWidgetsLayout()
    {
        $this->addRow(function (DashboardLayoutRow $row) {
            $row->addWidget(12, "welcome");
        });
    }

    public function buildWidgetsData(DashboardQueryParams $params)
    {
        $this
            ->setPanelData(
                "welcome",
                []
            );
    }
}
