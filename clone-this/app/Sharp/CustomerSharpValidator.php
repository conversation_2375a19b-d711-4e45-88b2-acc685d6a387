<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;

class CustomerSharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'name' => 'required',
            'phone' => [
                'required',
                $this->context()->instanceId() ? 'unique:customers,phone,' . $this->context()->instanceId() : 'unique:customers',
            ],
            'addresses' => 'required|array',
            'addresses.*.label' => 'required',
            'addresses.*.store_id' => 'required',
            'addresses.*.address' => 'required',
            'ppobcustomerdatas.*.key' => 'required',
            'ppobcustomerdatas.*.product_id' => 'required',
        ];
    }
}
