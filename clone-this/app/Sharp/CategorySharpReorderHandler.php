<?php

namespace App\Sharp;

use App\Models\Category;
use Code16\Sharp\EntityList\Commands\ReorderHandler;

class CategorySharpReorderHandler implements ReorderHandler
{
    public function reorder(array $ids)
    {
        Category::whereIn('id', $ids)->get()->each(function ($category) use ($ids) {
            $category->sort_order = array_search($category->id, $ids) + 1;
            $category->save();
        });
    }
}
