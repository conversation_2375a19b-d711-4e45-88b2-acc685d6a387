<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;
// use Illuminate\Validation\Rule;

class WbclSharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'title'    => 'required',
            'store_ids'    => 'required',
            'criteria'    => 'required',
            'message_template'    => 'required',
        ];
    }
}
