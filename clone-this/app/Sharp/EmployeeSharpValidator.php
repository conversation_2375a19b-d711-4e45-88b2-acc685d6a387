<?php

namespace App\Sharp;

use Code16\Sharp\Form\Validator\SharpFormRequest;
use Code16\Sharp\Http\WithSharpContext;
use Illuminate\Validation\Rule;

class EmployeeSharpValidator extends SharpFormRequest
{
    use WithSharpContext;

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules()
    {
        return [
            'full_name'    => 'required',
            'user_id'    => 'required',
            // 'ktp_number'    => 'required',
            // 'sim_number'    => 'required',
            // 'sim_valid_until'    => 'required',
            // 'profilephoto'    => 'required',
            // 'ktpphoto'    => 'required',
            // 'kkphoto'    => 'required',
            // 'simphoto'    => 'required',
        ];
    }
}