<?php

namespace App\Sharp\Policies;

use App\Models\User;
use App\Models\Store;

class CustomerPolicy
{

    /**
     * @param User $user
     * @return bool
     */
    public function entity(User $user)
    {
        return $user->role_id <= 3;
    }

    public function view(User $user, $modelId)
    {
        return true;
    }

    public function update(User $user, $modelId)
    {
        // return true;
        if ($user->role_id <= 3) {
            return true;
        } else {
            return false;
            // $store = Store::find($modelId);
            // return $store->order ? false : true;
        }
    }

    public function delete(User $user, $modelId)
    {
        if ($user->role_id <= 3) {
            $store = Store::find($modelId);
            return $store && $store->slug === 'belanja-kantor' ? false : true;
        } else {
            return false;
            // $store = Store::find($modelId);
            // return $store->order ? false : true;
        }
    }

    public function create(User $user)
    {
        if ($user->role_id <= 3) {
            return true;
        } else {
            return false;
        }
    }
}
