<?php

namespace App\Sharp\Policies;

use App\Models\User;

class OperatingCostPolicy
{

    /**
     * @param User $user
     * @return bool
     */
    public function entity(User $user)
    {
        return $user->role_id <= 2;
    }

    /**
     * @param User $user
     * @param $order_id
     * @return bool
     */
    public function view(User $user)
    {
        return $user->role_id <= 2;
    }

    /**
     * @param User $user
     * @param $order_id
     * @return bool
     */
    public function update(User $user)
    {
        return $user->role_id <= 2;
    }

    /**
     * @param User $user
     * @return bool
     */
    public function create()
    {
        return false;
    }

    /**
     * @param User $user
     * @param $order_id
     * @return bool
     */
    public function delete(User $user)
    {
        return $user->role_id <= 2;
    }
}
