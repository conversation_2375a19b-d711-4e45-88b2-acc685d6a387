<?php

namespace App\Jobs;

use App\Models\Store;
// use Illuminate\Contracts\Queue\ShouldBeUnique;
use App\Helper\Helper;
use App\Models\Setting;
use App\Models\ProductStore;
use Illuminate\Bus\Queueable;
use Illuminate\Queue\SerializesModels;

// use App\Models\Feedback;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
// use Illuminate\Queue\Middleware\WithoutOverlapping;

class ProcessUpdateStock implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    protected $datawebhook;

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct($datawebhook)
    {
        $this->datawebhook = $datawebhook;
    }

    /**
     * The number of seconds after which the job's unique lock will be released.
     *
     * @var int
     */
    public $uniqueFor = 3600;

    /**
     * The unique ID of the job.
     *
     * @return string
     */
    public function uniqueId()
    {
        $fake_id = '9b7d62f1-2ff5-420a-8beb-d68df0b173cf';
        return $fake_id;
    }

    /**
     * Calculate the number of seconds to wait before retrying the job.
     *
     * @return array
     */
    public function backoff()
    {
        return [2, 5, 10];
    }

    /**
     * Get the middleware the job should pass through.
     *
     * @return array
     */
    // public function middleware()
    // {
    //     $fake_id = '9b7d62f1-2ff5-420a-8beb-d68df0b173cf';
    //     return [new WithoutOverlapping($fake_id)];
    // }

    /**
     * Execute the job.
     *
     * @return void
     */
    public function handle()
    {
        $helper = new Helper;

        $data = $this->datawebhook;

        $stores = null;
        $product_count = 0;
        if (isset($data['purchaseInvoiceId']) && !empty($data['purchaseInvoiceId'])) {
            $params = [
                'id' => $data['purchaseInvoiceId']
            ];
            $body = [];
            $result_invoice_detail = $helper->fetchApiAccurate('/accurate/api/purchase-invoice/detail.do', 'GET', $params, $body);
            Setting::updateOrCreate(
                [
                    'name' => 'test-result-invoice-detail',
                ],
                ['value' => $result_invoice_detail]
            );
            if (is_object($result_invoice_detail) && $result_invoice_detail->s) {
                $branchId = $result_invoice_detail->d->branchId;
                if ($branchId) {
                    $stores = Store::where('accurate_branch_id', $branchId)->get();
                }
            }
        }
        if (!$stores) {
            // $stores = Store::all();
            return;
        }
        foreach ($stores as $store) {
            if (empty($store->accurate_warehouse_name)) continue;
            $params = [
                'sp.pageSize' => 1000,
                'warehouseName' => $store->accurate_warehouse_name,
            ];
            $body = [];
            $result = $helper->fetchApiAccurate('/accurate/api/item/list-stock.do', 'GET', $params, $body, true);
            if ($result) {
                foreach ($result->d as $stock) {
                    ProductStore::where('store_id', $store->id)
                        ->whereHas('product', function ($q) use ($stock) {
                            $q->where('accurate_item_no', $stock->no);
                        })
                        ->update([
                            'stock' => $stock->quantity,
                        ]);
                    $product_count++;
                }
            }
        }
        // Setting::updateOrCreate(
        //     [
        //         'name' => 'test-stock-updated',
        //     ],
        //     ['value' => [
        //         'product_count' => $product_count,
        //     ]]
        // );

        // $helper->fetchApiAccurate('https://account.accurate.id/api/webhook-renew.do', 'GET', null, null, false, true);

        // if (isset($res['status']) && $res['status'] === 'error') {
        //     $this->fail();
        // }
    }
}
