<?php

namespace App\ScheduleObjects;

use Carbon\Carbon;
use App\Models\Store;
use App\Models\Customer;
use App\Models\Statistic;

class RecordCustomerStat
{
  public function __invoke($force = false)
  {
    $data = [];
    $month01 = Carbon::now();
    $month02 = Carbon::now();
    $month03 = Carbon::now();
    $month06 = Carbon::now();
    $month01->subDays(30);
    $month02->subDays(60);
    $month03->subDays(90);
    $month06->subDays(180);
    $stores = Store::all();
    foreach ($stores as $store) {
      $customers = Customer::distinct()
        ->whereHas('addresses', function ($query) use ($store) {
          $query->where('store_id', $store->id);
        });

      $count_total = (clone $customers)->count();
      $count_lt1_month = (clone $customers)
        ->whereDate('options->last_order_at', '>=', $month01->toDateString())
        ->count();
      $count_lt2_month = (clone $customers)
        ->whereDate('options->last_order_at', '<=', $month01->toDateString())
        ->whereDate('options->last_order_at', '>', $month02->toDateString())
        ->count();
      $count_lt3_month = (clone $customers)
        ->whereDate('options->last_order_at', '<=', $month02->toDateString())
        ->whereDate('options->last_order_at', '>', $month03->toDateString())
        ->count();
      $count_lt6_month = (clone $customers)
        ->whereDate('options->last_order_at', '<=', $month03->toDateString())
        ->whereDate('options->last_order_at', '>', $month06->toDateString())
        ->count();
      $count_gt6_month = (clone $customers)
        ->where(function ($q) use ($month06) {
          $q->whereDate('options->last_order_at', '<=', $month06->toDateString())
            ->orWhereNull('options->last_order_at')
          ;
        })
        ->whereNull('options->last_notified_at')
        ->count();
      $count_gt6_month_and_wbcl = (clone $customers)
        ->where(function ($q) use ($month06) {
          $q->whereDate('options->last_order_at', '<=', $month06->toDateString())
            ->orWhereNull('options->last_order_at')
          ;
        })
        ->whereNotNull('options->last_notified_at')
        ->count();

      $data[] = [
        'store_id' => $store->id,
        'store_name' => $store->name,
        'data' => [
          'total' => $count_total,
          'lt1_month' => $count_lt1_month,
          'gt2_lt1_month' => $count_lt2_month,
          'gt2_lt3_month' => $count_lt3_month,
          'gt3_lt6_month' => $count_lt6_month,
          'gt6_month' => $count_gt6_month,
          'gt6_month_and_wbcl' => $count_gt6_month_and_wbcl,
        ]
      ];
    }

    Statistic::create([
      'title' => 'customer_last_order',
      'value' => $data,
    ]);


    return 'DONE';
  }
}
