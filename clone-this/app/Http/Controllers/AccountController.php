<?php

namespace App\Http\Controllers;

use App\Http\Controllers\Controller;
use App\Helper\Helper;
use Illuminate\Http\Request;
use App\Models\User;
use App\Models\Employee;
use App\Models\Store;
use App\Models\Armada;

class AccountController extends Controller
{
    public function index()
    {
        $helper = new Helper();
        return redirect()->route('calendar', ['store_slug' => $helper->getUserStoreSlug()]);
    }

    public function show($store_slug)
    {
        $helper = new Helper();
        $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'account');
        if (!$validateStoreSlug['status']) {
            return redirect()->route('account', ['store_slug' => $validateStoreSlug['store_slug']]);
        }
        $store = Store::where('slug', $store_slug)->first();
        $store_id = $store->id;
        $sdms = $store->sdms ? $store->sdms()->with([
            'employee'
        ])->get() : $store->sdms;
        $armadas = Armada::whereHas('employee', function ($query) use ($store) {
            $query->whereHas('user', function ($subquery) use ($store) {
                $subquery->where('store_id', $store->id);
            });
        })->get();
        $driver_kosong = User::whereIn('role_id', [5])
            ->where('store_id', 0)
            ->whereNull('deleted_at')
            ->get();
        $driveradmin_kosong = User::whereIn('role_id', [6])
            ->doesntHave('stores')
            ->whereNull('deleted_at')
            ->get();
        $driver_kosong = $driver_kosong->concat($driveradmin_kosong);
        $sdms = $sdms->concat($driver_kosong);
        if ((int)auth()->user()->role_id <= 2) {
            $stores = Store::all();
        } else {
            $stores = auth()->user()->stores->count() > 0 ? auth()->user()->stores : (auth()->user()->store_id ? Store::where('id', auth()->user()->store_id)->get() : []);
        }
        return view('manager.account', compact('store_slug', 'stores', 'store_id', 'sdms', 'armadas'));
    }

    public function sdm($store_slug, $employee_id)
    {
        $helper = new Helper();
        $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'account');
        if (!$validateStoreSlug['status']) {
            return redirect()->route('account', ['store_slug' => $validateStoreSlug['store_slug']]);
        }
        $store = Store::where('slug', $store_slug)->first();
        $sdm = Employee::where('id', $employee_id)
            ->with([
                'user',
                'armada',
                "profilephoto",
                "ktpphoto",
                "kkphoto",
                "simphoto",
            ])
            ->first();
        $sdm->profilephoto_url = $sdm->profilephoto ? $sdm->profilephoto->thumbnail($width = 1000, $height = 1000, $filters = []) : null;
        $sdm->kkphoto_url = $sdm->kkphoto ? $sdm->kkphoto->thumbnail($width = 1000, $height = 1000, $filters = []) : null;
        $sdm->ktpphoto_url = $sdm->ktpphoto ? $sdm->ktpphoto->thumbnail($width = 1000, $height = 1000, $filters = []) : null;
        $sdm->simphoto_url = $sdm->simphoto ? $sdm->simphoto->thumbnail($width = 1000, $height = 1000, $filters = []) : null;
        // $sdms = $store->sdms()->with([
        //     'employee'
        // ])->get();
        // $armadas = Armada::whereHas('employee', function($query) use ($store) {
        //     $query->whereHas('user', function($subquery) use ($store) {
        //         $subquery->where('store_id', $store->id);
        //     });
        // })->get();
        // $driver_kosong = User::where('role_id', 5)
        //     ->where('store_id', 0)
        //     ->whereNull('deleted_at')
        //     ->get();
        // $sdms = $sdms->concat($driver_kosong);
        // if (auth()->user()->role_id == 3) {
        //     $stores = auth()->user()->stores;
        // } else {
        //     $stores = Store::all();
        // }
        return view('manager.sdm', compact('store_slug', 'sdm'));
    }

    public function changesdmstore(Request $request)
    {
        $data = $request->all();
        // dd($data);
        $is_success = User::where('id', $data['user_id'])->update([
            'store_id' => $data['store_id'],
        ]);
        return redirect()->back();
    }

    public function armada($store_slug, $armada_id)
    {
        $helper = new Helper();
        $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'account');
        if (!$validateStoreSlug['status']) {
            return redirect()->route('account', ['store_slug' => $validateStoreSlug['store_slug']]);
        }
        $store = Store::where('slug', $store_slug)->first();
        $armada = Armada::where('id', $armada_id)
            ->with([
                'employee',
                'brand',
                'model',
                "stnkphoto",
                "frontphotos",
                "sidephotos",
                "backphotos",
            ])
            ->first();
        $armada->stnkphoto_url = $armada->stnkphoto ? $armada->stnkphoto->thumbnail($width = 1000, $height = 1000, $filters = []) : null;
        $armada->frontphotos = $armada->frontphotos ? $armada->frontphotos->map(
            function ($photo) {
                $photo->url = $photo->thumbnail($width = 1000, $height = 1000, $filters = []);
                //   $photo->created = $photo->created_at->format('d-j-y H:i:s');
                return $photo;
            }
        ) : null;
        $armada->sidephotos = $armada->sidephotos ? $armada->sidephotos->map(
            function ($photo) {
                $photo->url = $photo->thumbnail($width = 1000, $height = 1000, $filters = []);
                //   $photo->created = $photo->created_at->format('d-j-y H:i:s');
                return $photo;
            }
        ) : null;
        $armada->backphotos = $armada->backphotos ? $armada->backphotos->map(
            function ($photo) {
                $photo->url = $photo->thumbnail($width = 1000, $height = 1000, $filters = []);
                //   $photo->created = $photo->created_at->format('d-j-y H:i:s');
                return $photo;
            }
        ) : null;
        // $sdms = $store->sdms()->with([
        //     'employee'
        // ])->get();
        // $armadas = Armada::whereHas('employee', function($query) use ($store) {
        //     $query->whereHas('user', function($subquery) use ($store) {
        //         $subquery->where('store_id', $store->id);
        //     });
        // })->get();
        // $driver_kosong = User::where('role_id', 5)
        //     ->where('store_id', 0)
        //     ->whereNull('deleted_at')
        //     ->get();
        // $sdms = $sdms->concat($driver_kosong);
        // if (auth()->user()->role_id == 3) {
        //     $stores = auth()->user()->stores;
        // } else {
        //     $stores = Store::all();
        // }
        return view('manager.armada', compact('store_slug', 'armada'));
    }
}
