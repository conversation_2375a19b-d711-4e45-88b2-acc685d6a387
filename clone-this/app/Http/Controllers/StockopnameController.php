<?php

namespace App\Http\Controllers;

use Illuminate\Support\Facades\DB;
use DateTime;
use App\Helper\Helper;
use App\Models\Order;
use App\Models\Store;
use App\Models\Product;
use App\Models\Stockopname;
use App\Models\StockopnameCost;
use App\Models\StockopnameProduct;
use App\Models\OperatingCostItem;
use App\Models\OrderProduct;
use App\Models\User;
// use App\Models\Order;
// use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
// use PhpOffice\PhpSpreadsheet\IOFactory;

class StockopnameController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function index(Request $request, $store_slug)
    {
        if ((int)auth()->user()->role_id <= 2) {
            $stores = Store::all();
        } else {
            $stores = auth()->user()->stores->count() > 0 ? auth()->user()->stores : (auth()->user()->store_id ? Store::where('id', auth()->user()->store_id)->get() : []);
        }

        $helper = new Helper();
        $validateStoreSlug = $helper->checkUserStoreSlug($store_slug, 'stockopname');
        if (!$validateStoreSlug['status']) {
            return redirect()->route('jobs', [
                'store_slug' => $validateStoreSlug['store_slug'],
                'date' => $request->query('date'),
            ]);
        }
        $store = Store::where('slug', $store_slug)->first();
        $date_now = date('Y-m-d');

        $date_now = $request->query('date') ? $request->query('date') : $date_now;
        $date_compare = date('Y-m-d', strtotime($date_now . " +1 day"));
        $date_prev = date('Y-m-d', strtotime($date_now . " -1 day"));
        $datetime_current = new DateTime;
        $datetime_current->modify('-1 day');
        $datetime_now = new DateTime($date_now);
        $datetime_cache = new DateTime;
        $datetime_cache->modify(config('constants.cache.days_cached'));
        $date_next = $datetime_current > $datetime_now ? date('Y-m-d', strtotime($date_now . " +1 day")) : null;

        $seconds_cache_expire = $datetime_now < $datetime_cache ? config('constants.cache.expire_in_seconds') : config('constants.cache.expire_quick_in_seconds');

        $hari = array(1 => "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu", "Minggu");
        $day = $hari[date('N', strtotime($date_now))];

        $stockopname = Stockopname::where('store_id', $store->id)
            ->whereDate('date_assigned', date('Y-m-d', strtotime($date_now)))
            ->with([
                'products',
                'products.product.categories',
                'costs',
                'store',
                'assignedto',
                'submittedby',
                'confirmedby',
                'editor',
            ])
            ->first();

        $products = Product::whereHas('stores', function ($q) use ($store_slug) {
            $q->where('slug', $store_slug);
        })
            ->with([
                'categories',
            ])
            ->get()
            // ->map(function ($product) use ($store, $date_now, $helper) {
            //     // $params = [
            //     //     'fromDate' => date('d/m/Y', strtotime($date_now)),
            //     //     'toDate' => date('d/m/Y', strtotime($date_now)),
            //     //     'itemNo' => $product->code,
            //     //     'warehouseName' => $store->name,
            //     // ];
            //     // $body = [];
            //     // $result = $helper->fetchApiAccurate('/accurate/api/report/stock-mutation-summary.do', 'GET', $params, $body);
            //     // if (is_object($result) && property_exists($result, 'd') && is_array($result->d) && is_object($result->d[0]) && property_exists($result->d[0], '')) {

            //     // }
            //     return $product;
            // })
        ;

        $splitcash_orders = Order::whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->whereDate('created_at', date('Y-m-d', strtotime($date_now)))
            ->whereNotNull('amount_split_to_cash')
            ->with([
                'customer'
            ])
            ->get();


        return view('manager.stockopname-list', compact(
            'store_slug',
            'stores',
            'store',
            'date_now',
            'date_prev',
            'date_next',
            'day',
            'stockopname',
            'products',
            'splitcash_orders',
        ));
    }

    public static function upsertaccstocksummary($data, $store, $admin)
    {
        $helper = new Helper;

        $stockopname = null;
        $data_stockopname = [];
        $is_update = isset($data['stockopname_id']) && !empty($data['stockopname_id']);
        if ($is_update) {
            $stockopname = Stockopname::find($data['stockopname_id']);
        } else {
            $data_stockopname['store_id'] = $data['store_id'];
            $data_stockopname['date_assigned'] = $data['date'];
            $data_stockopname['assigned_to'] = $admin->id;
        }

        if ($store->accurate_branch_id) {
            $params = [
                'fields' => 'totalAmount',
                'sp.pageSize' => 1000,
                // 'filter.dueDate.val[0]' => date('d/m/Y'),
                'filter.transDate.val[0]' => date('d/m/Y', strtotime($data['date'])),
                'filter.branchName' => $store->name,
            ];
            $body = [];
            $result = $helper->fetchApiAccurate('/accurate/api/sales-invoice/list.do', 'GET', $params, $body);
            if (is_object($result) && $result->s) {
                $sales_invoices = $result->d;
                $data_stockopname['total_spd_acc'] = array_reduce($sales_invoices, function ($carry, $item) {
                    return $carry + $item->totalAmount;
                });
                $data_stockopname['qty_spd_acc'] = count($sales_invoices);
            }
        }

        // Product ID: 102 => CPR (Cek Produk dan Keluhan Pelanggan)
        $order = DB::table('orders')
            ->whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->whereDate('created_at', date('Y-m-d', strtotime($data['date'])))
            ->selectRaw("SUM(IF(status_id != 5,total,0)) AS total_nota")
            ->selectRaw("SUM(IF(status_id != 5,deposit_job,0)) AS total_deposit")
            ->selectRaw("COUNT(CASE WHEN status_id != 5 AND (total IS NULL OR total = 0) AND (deposit_job IS NOT NULL OR deposit_job != 0) THEN 1 END) AS qty_deposit")
            ->selectRaw("SUM(IF((status_id = 4 OR status_id = 6) AND payment_method_ask = 'non-cash',total,0)) AS order_terkirim_non_cash_total")
            ->first();
        $qty_nota = Order::whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->where('status_id', '!=', 5)
            ->where(function ($q) {
                $q->whereNotNull('total')
                    ->orWhere('total', '!=', 0);
            })
            ->whereDate('created_at', date('Y-m-d', strtotime($data['date'])))
            ->whereHas('orderproducts', function ($q) {
                $q->where('product_id', '!=', 102);
            })
            ->count();
        $qty_nota_cpr = Order::whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->where('status_id', '!=', 5)
            ->whereDate('created_at', date('Y-m-d', strtotime($data['date'])))
            ->whereHas('orderproducts', function ($q) {
                $q->where('product_id', 102);
            })
            ->with('orderproducts')
            ->withCount('orderproducts')
            ->has('orderproducts', '=', 1)
            ->count();
        if ($order) {
            $data_stockopname['total_spd_gpa'] = $order->total_nota;
            $data_stockopname['total_spd_deposit'] = $order->total_deposit;
            $data_stockopname['qty_spd_gpa'] = $qty_nota;
            $data_stockopname['qty_spd_deposit'] = $order->qty_deposit;
            $data_stockopname['total_std'] = $data_stockopname['qty_spd_gpa'];
            $data_stockopname['total_std_cpr'] = $qty_nota_cpr;
            $data_stockopname['total_noncash_in'] = $order->order_terkirim_non_cash_total;
        }
        if (isset($data_stockopname['total_spd_gpa']) && $data_stockopname['total_std']) {
            $data_stockopname['total_apc'] = round($data_stockopname['total_spd_gpa'] / $data_stockopname['total_std']);
        }

        $date_start = new DateTime();
        $date_start = $date_start->createFromFormat('Y-m-d', $data['date']);
        $date_start->modify('first day of this month');
        $date_start = $date_start->format("Y-m-d");
        $date_now = new DateTime();
        $date_now = $date_now->createFromFormat('Y-m-d', $data['date']);
        $count_days_this_month = (int)$date_now->format("d");
        $date_now = $date_now->format("Y-m-d");
        $order_accumulative = DB::table('orders')
            ->whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->whereBetween('created_at', [$date_start, $date_now])
            ->selectRaw("SUM(IF(status_id != 5,total,0)) AS total_nota_accumulative")
            ->first();
        $qty_nota_accumulative = Order::whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->whereBetween('created_at', [$date_start, $date_now])
            ->where('status_id', '!=', 5)
            ->where(function ($q) {
                $q->whereNotNull('total')
                    ->orWhere('total', '!=', 0);
            })
            ->whereHas('orderproducts', function ($q) {
                $q->where('product_id', '!=', 102);
            })
            ->count();
        $qty_nota_cpr_accumulative = Order::whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->whereBetween('created_at', [$date_start, $date_now])
            ->where('status_id', '!=', 5)
            ->whereHas('orderproducts', function ($q) {
                $q->where('product_id', 102);
            })
            ->with('orderproducts')
            ->withCount('orderproducts')
            ->has('orderproducts', '=', 1)
            ->count();
        if ($order_accumulative) {
            $data_stockopname['avg_std'] = round($qty_nota_accumulative / $count_days_this_month);
            $data_stockopname['avg_std_cpr'] = round($qty_nota_cpr_accumulative / $count_days_this_month);
            if ((int)$qty_nota_accumulative > 0) {
                $data_stockopname['avg_apc'] = round($order_accumulative->total_nota_accumulative / $qty_nota_accumulative);
            }
        }

        $operatingcost_total = OperatingCostItem::
            // ->whereNull('deleted_at')
            whereHas('operatingcost', function ($q) use ($store, $date_now) {
                $q->where('store_id', $store->id)
                    ->whereDate('submit_at', $date_now);
            })
            ->whereNotIn('cost_category_id', [1, 2, 3, 4])
            ->selectRaw("SUM(price) AS total_cost")
            ->first();
        if ($operatingcost_total) {
            $data_stockopname['total_cost'] = $operatingcost_total->total_cost;
        }
        $splitcash_orders = Order::whereNull('deleted_at')
            ->where('store_id', $store->id)
            ->whereDate('created_at', date('Y-m-d', strtotime($date_now)))
            ->whereNotNull('amount_split_to_cash')
            ->selectRaw("SUM(amount_split_to_cash) AS total_splitcash")
            ->first();
        if ($order && $operatingcost_total) {
            $total_cash_to_deposit = $order->total_nota - $order->order_terkirim_non_cash_total - $operatingcost_total->total_cost;
            if ($splitcash_orders) {
                $total_cash_to_deposit += $splitcash_orders->total_splitcash;
            }
            $data_stockopname['total_cash_to_deposit'] = $total_cash_to_deposit;
        }

        if ($is_update) {
            $stockopname->update($data_stockopname);
            $stockopname->load([
                'products',
                'products.product.categories',
                'costs',
                'store',
                'assignedto',
                'submittedby',
                'confirmedby',
                'editor',
            ]);
        } else {
            $stockopname = Stockopname::create($data_stockopname);
            $products = Product::whereHas('stores', function ($q) use ($data) {
                $q->where('stores.id', $data['store_id']);
            })
                ->get();
            foreach ($products as $product) {
                StockopnameProduct::create([
                    'stockopname_id' => $stockopname->id,
                    'product_id' => $product->id,
                    'product_code' => $product->code,
                ]);
            }

            $stockopname = Stockopname::where('id', $stockopname->id)
                ->with([
                    'products',
                    'products.product.categories',
                    'costs',
                    'store',
                    'assignedto',
                    'submittedby',
                    'confirmedby',
                    'editor',
                ])
                ->first();
        }

        return $stockopname;
    }

    public function accstocksummary(Request $request)
    {
        $data = $request->all();
        $store = Store::find($data['store_id']);
        $admin = User::whereIn('role_id', [4, 6])
            ->where(function ($q) use ($data) {
                $q->whereHas('stores', function ($sq) use ($data) {
                    $sq->where('id', $data['store_id']);
                })
                    ->orWhere('store_id', $data['store_id']);
            })
            ->first();
        if (!$admin) {
            return response()->json(['error' => 'Admin tidak ditemukan di toko ' . $store->name], 404);
        }

        $stockopname = self::upsertaccstocksummary($data, $store, $admin);

        return response()->json($stockopname);
    }

    public function accstockproduct(Request $request)
    {
        $helper = new Helper();
        $data = $request->all();
        if ($data['stockopname_product_id'] === 'new') {
            $data['stockopname_product_id'] = StockopnameProduct::create([
                'stockopname_id' => $data['stockopname_id'],
                'product_id' => $data['product_id'],
                'product_code' => $data['product_code'],
            ])->id;
        }
        $params = [
            'fromDate' => date('d/m/Y', strtotime($data['date'])),
            'toDate' => date('d/m/Y', strtotime($data['date'])),
            'itemNo' => $data['product_code'],
            'warehouseName' => $data['warehouse_name'],
        ];
        $params_prevday = [
            'fromDate' => date('d/m/Y', strtotime($data['date'] . "-1 day")),
            'toDate' => date('d/m/Y', strtotime($data['date'] . "-1 day")),
            'itemNo' => $data['product_code'],
            'warehouseName' => $data['warehouse_name'],
        ];
        $body = [];
        $result = $helper->fetchApiAccurate('/accurate/api/report/stock-mutation-summary.do', 'GET', $params, $body);
        $result_prevday = $helper->fetchApiAccurate('/accurate/api/report/stock-mutation-summary.do', 'GET', $params_prevday, $body);
        if ($result && $result_prevday) {
            if (is_object($result) && property_exists($result, 's') && is_object($result_prevday) && property_exists($result_prevday, 's') && $result->s && $result_prevday->s) {
                $stock = $result->d[0];
                $stock_prevday = $result_prevday->d[0];
                $stockopname_product = StockopnameProduct::find($data['stockopname_product_id']);
                $stockopname_product->start_balance = $stock_prevday->startBalance;
                $stockopname_product->quantity_in = $stock->quantityIn;
                // ? If all data null
                if ($stock_prevday->startBalance === 0 && $stock->quantityIn === 0 && $stock->quantityOut === 0 && $stock->startBalance === 0) {
                    // $params = [
                    //     'fields' => 'totalAmount',
                    //     'sp.pageSize' => 1000,
                    //     // 'filter.dueDate.val[0]' => date('d/m/Y'),
                    //     'filter.transDate.val[0]' => date('d/m/Y', strtotime($data['date'])),
                    //     'filter.branchName' => $store->name,
                    // ];
                    // $body = [];
                    // $result_penjualan = $helper->fetchApiAccurate('/accurate/api/sales-invoice/list.do', 'GET', $params, $body);
                    $order_product = OrderProduct::whereNull('deleted_at')
                        ->whereHas('order', function ($q) use ($data) {
                            $q->where('store_id', $data['store_id']);
                        })
                        ->whereDate('created_at', $data['date'])
                        // ->selectRaw("SUM(IF((status_id = 4 OR status_id = 6) AND payment_method_ask = 'cash',qty,0)) AS total_qty")
                        ->selectRaw("SUM(IF(product_id = " . $data['product_id'] . ",qty,0)) AS total_qty")
                        ->first();
                    // ->get();
                    $stockopname_product->quantity_out = (int) $order_product->total_qty;
                } else {
                    $stockopname_product->quantity_out = (int) $stock->quantityOut;
                }
                $stockopname_product->last_balance = $stock->startBalance;
                $stockopname_product->quantity_check = $stock->startBalance;
                $stockopname_product->balance_diff = 0;
                $stockopname_product->last_synced_at = new DateTime;
                $stockopname_product->error = null;
                // $stockopname_product->note = null;
                $stockopname_product->load(['product.categories']);
                $stockopname_product->save();
                return response()->json($stockopname_product);
            } else {
                $error_message = $result->d[0];
                $stockopname_product = StockopnameProduct::find($data['stockopname_product_id']);
                // $stockopname_product->note = $error_message;
                $stockopname_product->error = $error_message;
                $stockopname_product->last_synced_at = new DateTime;
                $stockopname_product->load(['product.categories']);
                $stockopname_product->save();
                return response()->json([
                    'message' => $error_message,
                    'stockopname_product' => $stockopname_product,
                ], 404);
            }
        } else {
            return response()->json(['error' => 'Gagal akses ke Accurate'], 400);
        }
    }

    public function store(Request $request)
    {
        $data = $request->all(); // date: 2024-05-22
        // return response()->json($data);

        $store = Store::find($data['store_id']);
        $admin = User::whereIn('role_id', [4, 6])
            ->where(function ($q) use ($data) {
                $q->whereHas('stores', function ($sq) use ($data) {
                    $sq->where('id', $data['store_id']);
                })
                    ->orWhere('store_id', $data['store_id']);
            })
            ->first();
        if (!$admin) {
            return response()->json(['error' => 'Admin tidak ditemukan di toko ' . $store->name], 404);
        }

        $stockopname = self::upsertaccstocksummary($data, $store, $admin);

        return response()->json($stockopname);
    }

    // public function show()
    // {
    //     return "TEST";
    // }

    // public function edit()
    // {
    //     return "TEST";
    // }

    public function update($id, Request $request)
    {
        $data = $request->all();
        $stockopname = Stockopname::find($data['id']);
        // $stockopname->total_cost = $data['total_cost'];
        if (isset($data['is_submit']) && $data['is_submit']) {
            $stockopname->submitted_at = new DateTime;
            $stockopname->submitted_by = auth()->user()->id;
        }
        if (isset($data['is_confirm']) && $data['is_confirm']) {
            $stockopname->confirmed_at = new DateTime;
            $stockopname->confirmed_by = auth()->user()->id;
        }
        $stockopname->save();
        foreach ($data['costs'] as $cost) {
            if ($cost['note'] === '__delete_this_row__') {
                StockopnameCost::where('id', $cost['id'])->delete();
            } else {
                $data_cost = $cost;
                $data_cost['stockopname_id'] = $stockopname->id;
                unset($data_cost['id']);
                unset($data_cost['created_at']);
                unset($data_cost['created_by']);
                unset($data_cost['updated_at']);
                unset($data_cost['updated_by']);
                StockopnameCost::updateOrCreate([
                    'id' => $cost['id'],
                ], $data_cost);
            }
        }
        foreach ($data['products'] as $product) {
            $data_product = $product;
            unset($data_product['id']);
            unset($data_product['product']);
            unset($data_product['created_at']);
            unset($data_product['created_by']);
            unset($data_product['updated_at']);
            unset($data_product['updated_by']);
            unset($data_product['is_loading']);
            StockopnameProduct::where('id', $product['id'])->update($data_product);
        }
        $stockopname = Stockopname::where('id', $stockopname->id)
            ->with([
                'products',
                'products.product.categories',
                'costs',
                'store',
                'assignedto',
                'submittedby',
                'confirmedby',
                'editor',
            ])
            ->first();
        return response()->json($stockopname);
    }

    public function confirm()
    {
        return "TEST";
    }
}
