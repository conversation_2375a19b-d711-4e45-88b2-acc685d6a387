<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Models\Address;
use App\Models\Area;
use App\Models\CostCategory;
use App\Models\Customer;
use App\Models\VhBrand;
use App\Models\VhModel;

class SharpController extends Controller
{
    /**
     * Create a new controller instance.
     *
     * @return void
     */
    public function __construct()
    {
        $this->middleware('auth');
    }

    /**
     * Show the application dashboard.
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function customersaddresses(Request $request, $store_id)
    {
        $data = $request->all();
        $q = $data['query'];
        $addresses = Address::with(['customer'])
            ->where("store_id", $store_id)
            ->whereHas('customer', function ($query) use ($q) {
                $query->where('name', "like", "%" . $q . "%")
                    ->orWhere('phone', "like", "%" . $q . "%");
            })->orWhere('address', "like", "%" . $q . "%")
            ->orderBy('customer_id');

        return $addresses->get();
    }

    public function searchcustomers(Request $request)
    {
        $data = $request->all();
        $q = $data['query'];
        $customers = Customer::with(['addresses', 'addresses.store'])
            ->whereNull("merger_id")
            ->where(function ($subquery) use ($q) {
                $subquery->where('name', "like", "%" . $q . "%")
                    ->orWhere('phone', "like", "%" . $q . "%");
            })
            ->orderBy('name');

        return $customers->get();
    }

    public function areabystore(Request $request, $store_id)
    {
        $data = $request->all();
        $keyword = $data['query'];
        $areas = Area::where(function ($query) use ($store_id) {
            $query->where("store_id", $store_id)
                ->orWhereNull('store_id');
        });

        if ($keyword) {
            $areas->where('name', "like", "%" . $keyword . "%");
        }
        // ->whereHas('customer', function ($query) use ($q) {
        //     $query->where('name', "like", "%" . $q . "%")
        //         ->orWhere('phone', "like", "%" . $q . "%");
        // })->orWhere('address', "like", "%" . $q . "%")
        //     ->orderBy('customer_id');

        return $areas->get();
    }

    public function searchVhBrands(Request $request)
    {
        $data = $request->all();
        $q = $data['query'];
        $data = VhBrand::where('name', "like", "%" . $q . "%")
            ->get();
        $new = [
            [
                'id' => 'new-=>' . $q,
                'name' => $q,
            ]
        ];
        return $data->count() > 0 ? $data : $new;
    }

    public function searchVhModels(Request $request, $brand_id)
    {
        $data = $request->all();
        $q = $data['query'];
        $data = VhModel::where('vh_brand_id', $brand_id)
            ->where('name', "like", "%" . $q . "%")
            // ->with(['type'])
            ->get();
        $new = [
            [
                'id' => 'new-=>' . $q,
                'name' => $q,
            ]
        ];
        return $data->count() > 0 ? $data : $new;
    }

    public function searchCostCategory(Request $request, $parent_cost_category_id)
    {
        $data = $request->all();
        $keyword = $data['query'];
        $cost_categories = CostCategory::where('is_root', 0);
        if ($parent_cost_category_id > 0) {
            $cost_categories->where('parent_id', $parent_cost_category_id);
        }
        if ($keyword !== "+" && $keyword !== "++") {
            $cost_categories->where('title', "like", "%" . $keyword . "%");
        }
        return $cost_categories->get();
        // $new = [
        //     [
        //         'id' => 'new-=>' . $q,
        //         'title' => $q,
        //     ]
        // ];
        // return $data->count() > 0 ? $data : $new;
    }
}
