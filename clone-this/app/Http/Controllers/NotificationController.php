<?php

namespace App\Http\Controllers;

use App\Models\Store;
use App\Helper\Helper;
use App\Models\Customer;
use Illuminate\Http\Request;
use App\Models\NotificationLog;
use App\Helper\HelperSocialchat;
use App\Http\Controllers\Controller;

class NotificationController extends Controller
{
    public function reviewInvitation($customer_id_store_id, Request $request)
    {
        $helper = new Helper();
        $customer_id_store_id = $helper->miniDecrypt($customer_id_store_id);
        $customer_id = explode('-', $customer_id_store_id)[0];
        $store_id = explode('-', $customer_id_store_id)[1];
        $store = Store::find($store_id);
        if (!$store) {
            $message = '☕️ Link invalid!';
            return view('feedback', compact('message'));
        }
        $url_gmap = $store->meta['url_gmap'] ?? '';
        if (!$url_gmap) {
            $message = '☕️ Link invalid!';
            return view('feedback', compact('message'));
        }
        $customer = Customer::find($customer_id);
        if (!$customer) {
            $message = '☕️ Link invalid!';
            return view('feedback', compact('message'));
        }
        $customer->options = $customer->options ?? [];
        $customer->options = [
            ...$customer->options,
            'ignore_review_invitation' => 1,
        ];
        $customer->save();
        return redirect($url_gmap);
    }
    public function storeFeedback($notification_log_id, $feedback_key, Request $request)
    {
        $helper = new Helper();
        $notification_log_id = $helper->miniDecrypt($notification_log_id);
        $notification_log = NotificationLog::find($notification_log_id);
        if (!$notification_log) {
            $message = '☕️ Link invalid!';
            return view('feedback', compact('message'));
        }

        $notification_log->feedback_title = $feedback_key;
        $notification_log->feedback_message = $request->input('msg');
        $notification_log->save();

        $customer = $notification_log->customer;
        $customer->options = $customer->options ?? [];
        $customer->options = [
            ...$customer->options,
            'ignore_notif_reminder' => 1,
        ];
        $customer->save();

        $is_with_input = $request->query('in');
        if ($is_with_input === '1') {
            return view('feedback-with-input');
        }

        // ? Send feedback message
        if ($notification_log->customer->socialchat_conversation_id) {
            $helper_socialchat = new HelperSocialchat;
            if ($request->input('msg')) {
                $feedback_beautify = str_replace(['_', '-'], ' ', $feedback_key);
                $feedback_beautify = strtoupper($feedback_beautify);
                $helper_socialchat->sendMessage($notification_log->customer->socialchat_conversation_id, 'Terima kasih, feedback kamu *' . $feedback_beautify . '* (' . $request->input('msg') . ') 🙏');
            } else if ($is_with_input !== '1') {
                $feedback_beautify = str_replace(['_', '-'], ' ', $feedback_key);
                $feedback_beautify = strtoupper($feedback_beautify);
                $helper_socialchat->sendMessage($notification_log->customer->socialchat_conversation_id, 'Terima kasih, feedback kamu *' . $feedback_beautify . '* 🙏');
            }
        }

        // $feedback_key = str_replace(['_', '-'], ' ', $feedback_key);
        // return ucfirst($feedback_key);

        $message = 'Terimakasih atas masukannya 👍';
        return view('feedback', compact('message'));
    }

    public function unsubscribe($notification_log_id, Request $request)
    {
        $helper = new Helper();
        $notification_log_id = $helper->miniDecrypt($notification_log_id);
        $notification_log = NotificationLog::find($notification_log_id);
        if (!$notification_log) {
            $message = '☕️ Link invalid!';
            return view('feedback', compact('message'));
        }
        $customer = $notification_log->customer;
        $customer->options = $customer->options ?? [];
        $customer->options = [
            ...$customer->options,
            'ignore_notif_reminder' => 1,
        ];
        $customer->save();

        // ? Send feedback message
        if ($notification_log->customer->socialchat_conversation_id) {
            $helper_socialchat = new HelperSocialchat;
            $helper_socialchat->sendMessage($notification_log->customer->socialchat_conversation_id, 'Terima kasih, kamu *tidak akan menerima pesan* ini lagi 🙏');
        }

        $message = 'Unsubscribed ✅';
        return view('feedback', compact('message'));
    }
}
