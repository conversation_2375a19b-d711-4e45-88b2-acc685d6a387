<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Sqits\UserStamps\Concerns\HasUserStamps;

class Merger extends Model
{
    use HasFactory, HasUserStamps;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'main_customer_id',
        'note',
    ];

    public $rules = [
        'main_customer_id' => 'required|integer',
        'note' => 'string',
    ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    // protected static function boot()
    // {
    //     parent::boot();

    //     static::deleted(function ($model) {
    //         $model->phone = 'xxx' . $model->phone . 'xxx';
    //         $model->save();
    //     });

    //     // Event::listen(TranslationHasBeenSet::class, function (TranslationHasBeenSet $event) {
    //     //     $event->model->updateCustomProperty($event->key, $event->newValue);
    //     //     unset($event->model->attributes[$event->key]);
    //     // });
    // }

    public function members()
    {
        return $this->hasMany(Customer::class);
    }

    public function maincustomer()
    {
        return $this->belongsTo(Customer::class, 'main_customer_id', 'id')->withTrashed();
    }
}