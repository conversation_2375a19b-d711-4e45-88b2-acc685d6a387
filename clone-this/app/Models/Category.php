<?php

namespace App\Models;

use App\Models\Store;
use App\Models\CategoryStore;
use Illuminate\Database\Eloquent\Model;
use Sqits\UserStamps\Concerns\HasUserStamps;
use Illuminate\Database\Eloquent\Factories\HasFactory;

// use Illuminate\Database\Eloquent\SoftDeletes;

class Category extends Model
{
    use HasFactory, HasUserStamps;
    // use SoftDeletes;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'slug',
        'name',
        'sort_order',
    ];

    // public $rules = [
    //     'slug' => 'required|string|unique:categories',
    //     'name' => 'required|string|unique:categories',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    protected static function booted()
    {
        static::creating(function ($model) {
            $model->sort_order = Self::max('sort_order') + 1;
        });
        static::created(function ($category) {
            $stores = Store::all();
            foreach ($stores as $store) {
                CategoryStore::create([
                    'store_id' => $store->id,
                    'category_id' => $category->id,
                ]);
            }
        });
    }

    public function products()
    {
        return $this->belongsToMany(Product::class)->withTimestamps();
    }
}
