<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Carbon\Carbon;
use Illuminate\Support\Facades\Hash;
use Illuminate\Auth\Notifications\ResetPassword;
// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;
use Sqits\UserStamps\Concerns\HasUserStamps;

class User extends Authenticatable
{
    use HasFactory, Notifiable, SoftDeletes, HasUserStamps;

    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * The attributes that should be cast to native types.
     *
     * @var array
     */
    protected $casts = [
        'email_verified_at' => 'datetime',
        'options' => 'array',
    ];

    protected $dates = [
        'updated_at',
        'created_at',
        'deleted_at',
        'email_verified_at',
    ];

    protected $fillable = [
        'name',
        'email',
        'phone',
        'password',
        // 'created_at',
        // 'updated_at',
        // 'deleted_at',
        'role_id',
        // 'armada_id',
        'store_id',
        'subscriber_id',
        'notification_is_active',
        'remember_token',
        'email_verified_at',
        'options',
    ];

    protected static function boot()
    {
        parent::boot();

        static::deleted(function ($model) {
            $model->email = 'xxx_' . $model->email . '_' . time();
            $model->phone = 'xxx_' . $model->phone . '_' . time();
            $model->save();
        });

        // Event::listen(TranslationHasBeenSet::class, function (TranslationHasBeenSet $event) {
        //     $event->model->updateCustomProperty($event->key, $event->newValue);
        //     unset($event->model->attributes[$event->key]);
        // });
    }

    public function getEmailVerifiedAtAttribute($value)
    {
        return $value ? Carbon::createFromFormat('Y-m-d H:i:s', $value)->format(config('panel.date_format') . ' ' . config('panel.time_format')) : null;
    }

    public function setEmailVerifiedAtAttribute($value)
    {
        $this->attributes['email_verified_at'] = $value ? Carbon::createFromFormat(config('panel.date_format') . ' ' . config('panel.time_format'), $value)->format('Y-m-d H:i:s') : null;
    }

    public function setPasswordAttribute($input)
    {
        if ($input) {
            $this->attributes['password'] = app('hash')->needsRehash($input) ? Hash::make($input) : $input;
        }
    }

    public function sendPasswordResetNotification($token)
    {
        $this->notify(new ResetPassword($token));
    }

    public function role()
    {
        return $this->belongsTo(Role::class);
    }

    // public function armada()
    // {
    //     return $this->belongsTo(Armada::class);
    // }
    public function employee()
    {
        return $this->hasMany(Employee::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function stores()
    {
        return $this->belongsToMany(Store::class);
    }

    public function orders()
    {
        return $this->hasMany(Order::class, 'driver_id', 'id');
    }

    public function marketings()
    {
        return $this->hasMany(Marketing::class, 'driver_id', 'id');
    }

    public function totaldelivers()
    {
        return $this->morphMany(Total::class, 'totalable');
    }
}