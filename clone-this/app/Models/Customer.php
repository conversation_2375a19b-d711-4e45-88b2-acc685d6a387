<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;
use App\Helper\Helper;

class Customer extends Model
{
    use HasFactory, SoftDeletes, HasUserStamps;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        // 'store_id',
        'is_main',
        'merger_id',
        'is_company',
        'phone',
        'name',
        'cp_phone',
        'cp_name',
        'email',
        'payment',
        'amount',
        'note',
        'note_special',
        'deposit_amount',
        'notif_status',
        'blash_updated_at',
        'socialchat_conversation_id',
        'options',
        // 'accurate_id',
        // 'accurate_code',
        // 'created_at',
        // 'updated_at',
    ];

    public $rules = [
        // 'store_id' => 'required|integer',
        'phone' => 'required|string|unique:customers',
        'name' => 'required|string',
        'email' => 'string',
        'payment' => 'string',
        'amount' => 'integer',
        'note' => 'string',
    ];

    // Convert Input
    protected $casts = [
        // 'is_admin' => 'boolean',
        'options' => 'array',
    ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    protected static function boot()
    {
        parent::boot();

        static::created(function ($model) {
            $helper = new Helper;
            foreach ($model->addresses as $address) {
                $helper->accurateUpsertCustomer($address, true);
            }
        });

        static::updated(function ($model) {
            $helper = new Helper;
            foreach ($model->addresses as $address) {
                $helper->accurateUpsertCustomer($address);
            }
        });

        static::deleted(function ($model) {
            if (env('APP_ENV') === 'production') {
                $helper = new Helper;
                foreach ($model->addresses as $address) {
                    $helper->accurateDeleteCustomer($address);
                }
            }
            $model->phone = 'xxx_' . $model->phone . '_' . time();
            $model->save();
        });

        // Event::listen(TranslationHasBeenSet::class, function (TranslationHasBeenSet $event) {
        //     $event->model->updateCustomProperty($event->key, $event->newValue);
        //     unset($event->model->attributes[$event->key]);
        // });
    }

    public function lastorder()
    {
        return $this->hasOne(Order::class)->latestOfMany();
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function addresses()
    {
        return $this->hasMany(Address::class);
    }

    public function socialchat()
    {
        return $this->hasMany(Socialchat::class);
    }

    public function lastsocialchat()
    {
        return $this->hasOne(Socialchat::class)->latestOfMany();
    }

    public function merger()
    {
        return $this->belongsTo(Merger::class);
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }

    public function deposits()
    {
        return $this->hasMany(Deposit::class);
    }

    public function products()
    {
        return $this->belongsToMany(Product::class)
            ->withPivot(
                'stock',
                'local_price',
                'is_available'
            )
            ->withTimestamps();
    }

    public function delmanexcludes()
    {
        return $this->belongsToMany(User::class, 'customer_driver_exclude');
    }

    public function customerproducts()
    {
        return $this->hasMany(CustomerProduct::class);
    }

    public function ppobcustomerdatas()
    {
        return $this->hasMany(PpobCustomerData::class);
    }

    public function ppobresponsedatas()
    {
        return $this->hasMany(PpobResponseData::class);
    }

    public function setDeposit($val)
    {
        $this->fresh();

        $this->deposit_amount = $val;

        $this->save();
    }

    public function addDeposit($val)
    {
        $this->fresh();

        $this->deposit_amount += $val;

        $this->save();
    }

    // public function stores()
    // {
    //     return $this->hasManyThrough(Store::class, Address::class);
    // }
}
