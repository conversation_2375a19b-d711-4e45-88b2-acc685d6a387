<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
// use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;

class CostCategory extends Model
{
    use HasFactory, SoftDeletes, HasUserStamps;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'title',
        'parent_id',
        'is_root',
    ];

    // public $rules = [
    //     'code' => 'required|string',
    // ];

    // Convert Input
    protected $casts = [
        'is_root' => 'boolean',
    ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    public function parent()
    {
        return $this->belongsTo(CostCategory::class, 'parent_id', 'id');
    }

    public function children()
    {
        return $this->hasMany(CostCategory::class, 'parent_id', 'id');
    }

    public function operatingcosts()
    {
        return $this->hasMany(OperatingCost::class);
    }

    public function operatingcostitems()
    {
        return $this->hasMany(OperatingCostItem::class);
    }
}