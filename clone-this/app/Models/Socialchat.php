<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

// use Illuminate\Database\Eloquent\SoftDeletes;

class Socialchat extends Model
{
    use HasFactory;
    // use SoftDeletes;

    // protected $table = 'test_products';
    public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'store_id',
        'customer_id',
        'phone',
        'conversation_id',
    ];

    // public $rules = [
    //     'bank_name' => 'required|string',
    //     'account_number' => 'required|string',
    //     'holder_name' => 'required|string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    // protected static function boot()
    // {
    //     parent::boot();

    //     Bank::creating(function ($model) {
    //         $model->sort_order = Bank::max('sort_order') + 1;
    //     });
    // }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class);
    }
}
