<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Sqits\UserStamps\Concerns\HasUserStamps;

// use Illuminate\Database\Eloquent\SoftDeletes;

class Deposit extends Model
{
    use HasFactory, HasUserStamps;
    // use SoftDeletes;

    protected $table = 'deposit_flows';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'job_order_id',
        'order_id',
        'invoice_id',
        'customer_id',
        'amount',
        'balance',
        'note',
        'created_at',
    ];

    // public $rules = [
    //     'bank_name' => 'required|string',
    //     'account_number' => 'required|string',
    //     'holder_name' => 'required|string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    // protected static function boot()
    // {
    //     parent::boot();

    //     Bank::creating(function ($model) {
    //         $model->sort_order = Bank::max('sort_order') + 1;
    //     });
    // }

    public function order()
    {
        return $this->belongsTo(Order::class)->withTrashed();
    }

    public function invoice()
    {
        return $this->belongsTo(Invoice::class)->withTrashed();
    }

    public function customer()
    {
        return $this->belongsTo(Customer::class)->withTrashed();
    }

    public function paymentproof()
    {
        return $this->morphOne(Media::class, "model")
            ->where("model_key", "paymentproof");
    }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, ['paymentproof'])
            ? ["model_key" => $attribute]
            : [];
    }
}