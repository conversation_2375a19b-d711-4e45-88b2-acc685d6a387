<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;
// use App\Helper\Helper;

class Purchase extends Model
{
    use HasFactory, HasUserStamps, SoftDeletes;

    // protected $table = 'test_products';
    // public $timestamps = false;

    protected $dates = [
        'created_at',
        'updated_at',
        'deleted_at',
    ];

    protected $fillable = [
        'store_id',
        'bill_number',
        'payment',
        'vendor_acc_no',
        'vendor_acc_name',
        'transaction_date',
        'note',
    ];

    // public $rules = [
    //     'name' => 'required|string',
    //     'latlng' => 'string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    // protected static function booted()
    // {
    //     static::saving(function ($model) {
    //         $model->countDistance();
    //     });
    // }

    public function store()
    {
        return $this->belongsTo(Store::class);
    }

    public function products()
    {
        return $this->belongsToMany(Product::class)
            ->withPivot([
                'qty',
                'price',
                'warehouse_acc_name',
            ])
            ->withTimestamps();
    }

    public function accuratePurchase()
    {
        return $this->morphOne(AccurateData::class, 'accuratable')
            ->where("accuratable_key", "purchase-invoice");
    }

    public function photoreceipts()
    {
        return $this->morphMany(Media::class, "model")
            ->where("model_key", "photoreceipts")
            ->orderBy("order");
    }

    public function getDefaultAttributesFor($attribute)
    {
        return in_array($attribute, ["photoreceipts"])
            ? ["model_key" => $attribute]
            : [];
    }
}
