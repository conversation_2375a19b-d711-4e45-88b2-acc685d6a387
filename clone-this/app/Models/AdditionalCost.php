<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class AdditionalCost extends Model
{
    use HasFactory;
    use SoftDeletes;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'name',
        'cost',
        'min_unit',
        'round',
        'total_cost',
        'order_id',
        'accurate_invoice_item_id',
        'code',
        // 'created_at',
        // 'updated_at',
        'deleted_at',
    ];

    // public $rules = [
    //     'name' => 'required|string',
    //     'latlng' => 'string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    // protected static function booted()
    // {
    //     static::saving(function ($model) {
    //         $model->countDistance();
    //     });
    // }

    public function order()
    {
        return $this->belongsTo(Order::class);
    }
}
