<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
// use Illuminate\Database\Eloquent\SoftDeletes;
use Sqits\UserStamps\Concerns\HasUserStamps;
// use App\Helper\Helper;

class Stockopname extends Model
{
    use HasFactory, HasUserStamps;

    // protected $table = 'test_products';
    // public $timestamps = false;

    protected $dates = [
        'created_at',
        'updated_at',
        // 'deleted_at',
    ];

    protected $fillable = [
        'status',
        'store_id',
        'count_products',
        'count_products_checked',
        'total_products_less',
        'total_products_equal',
        'total_products_over',
        'total_qty_product_out',
        'total_qty_product_in',
        'total_variant_product',
        'total_cash_in',
        'total_cash_to_deposit',
        'date_assigned',
        // 'date_submitted',
        // 'date_confirmed',
        'assigned_to',
        'submitted_by',
        'confirmed_by',
        'submitted_at',
        'confirmed_at',
        'total_spd_acc',
        'qty_spd_acc',
        'total_spd_gpa',
        'total_spd_deposit',
        'qty_spd_gpa',
        'qty_spd_deposit',
        'total_std',
        'total_std_cpr',
        'avg_std',
        'avg_std_cpr',
        'total_apc',
        'avg_apc',
        'total_noncash_in',
        'total_cost',
    ];

    // public $rules = [
    //     'name' => 'required|string',
    //     'latlng' => 'string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    // protected static function booted()
    // {
    //     static::saving(function ($model) {
    //         $model->countDistance();
    //     });
    // }

    public function products()
    {
        return $this->hasMany(StockopnameProduct::class);
    }

    public function costs()
    {
        return $this->hasMany(StockopnameCost::class);
    }

    public function store()
    {
        return $this->belongsTo(Store::class)->withTrashed();
    }

    public function assignedto()
    {
        return $this->belongsTo(User::class, 'assigned_to', 'id')->withTrashed();
    }

    public function submittedby()
    {
        return $this->belongsTo(User::class, 'submitted_by', 'id')->withTrashed();
    }

    public function confirmedby()
    {
        return $this->belongsTo(User::class, 'confirmed_by', 'id')->withTrashed();
    }

    // public function countDistance($manual = false)
    // {
    //     if ($manual) {
    //         $this->fresh();
    //     }

    //     if ($this->store && $this->latlng && $this->store->latlng) {
    //         $helper = new Helper();
    //         $this->distance_store_area = $helper->getDistance($this->store->latlng, $this->latlng);
    //     }

    //     // $this->distance_store_customer = $helper->getDistance('-7.8157452,110.3434034', '-7.810886,110.3476412');

    //     if ($manual) {
    //         $this->save();
    //     }
    // }
}
