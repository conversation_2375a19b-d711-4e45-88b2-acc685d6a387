<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Sqits\UserStamps\Concerns\HasUserStamps;

// use Illuminate\Database\Eloquent\SoftDeletes;

class Bank extends Model
{
    use HasFactory, HasUserStamps;
    // use SoftDeletes;

    // protected $table = 'test_products';
    // public $timestamps = false;

    // protected $dates = [
    //     'created_at',
    //     'updated_at',
    //     'deleted_at',
    // ];

    protected $fillable = [
        'bank_name',
        'account_number',
        'holder_name',
        'accurate_glaccount_id',
    ];

    // public $rules = [
    //     'bank_name' => 'required|string',
    //     'account_number' => 'required|string',
    //     'holder_name' => 'required|string',
    // ];

    // Convert Input
    // protected $casts = [
    //     'is_admin' => 'boolean',
    // ];

    // Default Value
    // protected $attributes = [
    //     'sort_order' => 0,
    // ];

    protected static function boot()
    {
        parent::boot();

        Bank::creating(function ($model) {
            $model->sort_order = Bank::max('sort_order') + 1;
        });
    }

    public function stores()
    {
        return $this->belongsToMany(Store::class)->withTimestamps();
    }

    public function orders()
    {
        return $this->hasMany(Order::class);
    }

    public function invoices()
    {
        return $this->hasMany(Invoice::class);
    }
}
